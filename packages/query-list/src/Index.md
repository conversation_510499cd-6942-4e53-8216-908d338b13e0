示例
```vue
<template>
  <div class="h-60">
    <query-list
      :data="data"
      @searchInfo="searchInfo"
      @searchClear="searchClear"
      @toggleFilterExpand="toggleFilterExpand"
    >
    </query-list>
  </div>
 </template>

<script>
import { Moment } from 'moment';
const data = [
  {
    label: '中文名称',
    name: 'name1',
    value: 'name1',
    type: 'input'
  },
  {
    label: '状态',
    name: 'status',
    value: [],
    type: 'select',
    mode: 'multiple',
    optList: [
      {label: '全部', value: '', id: 'm0'},
      {label: '成功', value: 0, id: 'm1'},
      {label: '失败', value: 1, id: 'm2'},
      {label: '更新中', value: 2, id: 'm3'},
    ]
  },
  {
    label: '更新时间',
    name: 'updateDate',
    type: 'date',
    value: null
  },
  // {
  //   label: '描述',
  //   name: 'desc',
  //   value: '',
  //   type: 'input'
  // },
]


export default {
  data() {
    return {
      data: data
    }
  },
  methods: {
    searchInfo (data) {
      console.log(data)
    },
    searchClear () {
      console.log('清空')
    },
    toggleFilterExpand (filterExpand) {
      console.log(filterExpand)
    }
  }
}
</script>
```
