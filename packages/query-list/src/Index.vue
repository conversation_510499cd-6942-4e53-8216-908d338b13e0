<template>
  <div class="query-list" :class="{'show': filterExpand, 'pick-up': !filterExpand, 'border-b-line' : showLine}">
    <ul class="flex-1" ref="queryList" :class="{'h-12 overflow-hidden': !filterExpand}">
      <li v-for="(item,index) in data" :key="index" :class="`col-${col}`">
        <span class="label-wrap">{{item.label}}</span>
        <div v-if="item.type === 'input'" class="value-wrap">
          <a-input v-model="item.value" @keyup.enter="search()" @clear="search()" />
        </div>
        <div v-if="item.type === 'select'" class="value-wrap">
          <a-select
            v-model="item.value"
            class="w-full"
            @change="search()"
            >
            <a-select-option
              v-for="it in item.optList"
              :key="it.label"
              :value="it.value">
              {{it.label}}
            </a-select-option>
          </a-select>
        </div>
        <div v-if="item.type === 'date'" class="value-wrap">
          <a-range-picker v-model="item.value" class="w-full" format="YYYY-MM-DD" @change="search()" @clear="search()" />
        </div>
      </li>
    </ul>
    <div class="btn-wrap" v-if="showQueryBtn">
      <a-button class="font-semibold"  @click="search()">查询</a-button>
      <a-button class="font-semibold ml-2" type="primary-text" @click="clear()">清空</a-button>
      <a-button
        type="primary-text"
        class="font-semibold"
        @click="toggleFilterExpand"
        :class="{'hidden': !expandBtnShow}"
      >{{ filterExpand ? '收起' : '展开' }}<hz-icon hzName="triangle-filled-down" class="ml-0.5 -mt-px " :class="{'transform rotate-180': filterExpand}" /></a-button>
    </div>
  </div>
</template>

<script>
import { hzIcon } from '@hertz/base-components'
import moment from 'moment'
/**
 * @displayName query-list 查询组件
 */
export default {
  components: { hzIcon },
  name: 'QueryList',
  props: {
    data: {
      default: () => [],
      type: Array
    },
    showQueryBtn: {
      type: Boolean,
      default: true
    },
    col: {
      type: Number,
      default: 0
    },
    showLine: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      filterExpand: false,
      expandBtnShow: false
    }
  },
  mounted () {
    this.init()
    window.addEventListener('resize', this.initElemEvent)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.initElemEvent)
  },
  methods: {
    init () {
      // if (window.screen.width <= 1440) {
      //   if (this.data.length <= 2) {
      //     this.expandBtnShow = false
      //   }
      // }
      // if (window.screen.width > 1440) {
      //   if (this.data.length <= 3) {
      //     this.expandBtnShow = false
      //   }
      // }
      this.initElemEvent()
    },
    initElemEvent () {
      const queryListElem = this.$refs.queryList
      const childrenElems = queryListElem.children
      if (!childrenElems) return
      let top = 0;
      for( let i = 0; i < childrenElems.length; i++) {
        if(childrenElems.hasOwnProperty(i)) {
          const item = childrenElems[i]
          if (i === 0) {
            top = item.offsetTop
          }
          if (item.offsetTop > top) {
            this.expandBtnShow = true
          } else {
            this.expandBtnShow = false
          }
        }
      }
    },
    search () {
      const resData = {}
      this.data.forEach((item) => {
        if (item.type === 'date') {
          resData[item.name] = item.value && item.value.length ? [moment(item.value[0]).format('YYYY-MM-DD'), moment(item.value[1]).format('YYYY-MM-DD')] : null
        } else {
          resData[item.name] = item.value
        }
      })
      this.$emit('searchInfo', resData)
      // return resData
    },
    clear () {
      this.data.forEach((item) => {
        if (item.type === 'select') {
          item.value = item.optList[0].value
        } else if (item.type === 'date') {
          item.value = null
        } else {
          item.value = ''
        }
      })
      this.$emit('searchClear')
    },
    toggleFilterExpand () {
      this.filterExpand = !this.filterExpand
      this.$emit('toggleFilterExpand', this.filterExpand)
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    }
  }
}
</script>

<style scoped>
.query-list {
  @apply flex
}
.query-list.border-b-line {
  @apply border-b-1 border-mono-a-300 pb-6
}
.query-list ul {
  @apply flex flex-wrap -mt-4;
}
.query-list li {
  @apply lg:w-1/3 xl:w-1/3 2xl:w-1/4 flex pr-8 mt-4
}
.query-list li.col-1 {
  @apply w-full
}
.query-list li.col-2 {
  @apply w-1/2
}
.query-list li.col-4 {
  @apply w-1/4
}
.query-list.show li{
  @apply flex
}
.query-list div.btn-wrap {
  @apply flex justify-end flex-shrink-0;
  width: 15rem;
}
@media (max-width: 1540px) {
  .query-list li:nth-child(3n+3){
    @apply pr-0
  }
  /* .query-list.pick-up li:nth-child(n+3):not(.btn-wrap){
    @apply hidden
  } */
}
@media (min-width: 1540px) {
  .query-list li:nth-child(4n+4){
    @apply pr-0
  }
  /* .query-list.pick-up li:nth-child(n+4):not(.btn-wrap){
    @apply hidden
  } */
}
.label-wrap {
  max-width: 5rem;
  @apply leading-8 pr-2 text-right whitespace-nowrap text-type-700 font-semibold flex-shrink-0
}
.value-wrap {
  @apply flex-grow
}
</style>
