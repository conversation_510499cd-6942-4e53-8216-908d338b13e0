<template>
  <div class="hertz-pro-menu-left shadow-container-c-200">
    <a-menu
      :defaultSelectedKeys="defaultSelectedKeys"
      mode="inline"
      @select="selectMenu"
      :inlineCollapsed="isCollapsed">
      <a-menu-item v-for="item in list" :key="item.key">
        <hz-icon :hzName="item.iconName || 'default-icon'" class="mr-2 w-5 h-5"></hz-icon>
        <span>{{item.title}}</span>
      </a-menu-item>
    </a-menu>
  </div>
</template>
<script>
export default {
  name: 'MenuLeft',
  props: {
    list: {
      type: Array,
      default: () => [
        {
            key: 'rule-template',
            title: '',
            iconName: 'data-quality'
        }
      ]
    },
    // 默认选中项
    defaultSelectedKeys: {
      type: Array,
      default: () => ['']
    },
    // 是否收起
    isCollapsed: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 展开收起
      // collapsed: false
    }
  },
  methods: {
    // 选中的菜单项
    selectMenu (obj) {
      // console.log('选中的导航', obj)
      this.$emit('selectMenu', obj)
    }
    // 展开收起菜单
    // toggleCollapsed () {
    //   this.collapsed = !this.collapsed
    // }
  }
}
</script>
<style scoped lang="scss">
.hertz-pro-menu-left {
  /* 左侧菜单列表 */
  .ant-menu-inline, .ant-menu-vertical {
    border-right: unset;
  }
  .ant-menu-inline > .ant-menu-item {
    border-radius: 8px;
    @apply h-12 leading-12 text-sm font-semibold;
  }

  .ant-menu-inline .ant-menu-item-selected::after {
    @apply border-r-2 border-primary-900;
    // border-right: 2px solid #1f71ff;
    height: 8px;
    top: calc(50% - 4px);
  }

  .ant-menu-inline .ant-menu-item::after {
    height: 8px;
    top: calc(50% - 4px);
  }
  /* 菜单收起时 */
  .ant-menu-inline-collapsed > .ant-menu-item {
    padding: 0 16px !important;
  }

  .ant-menu-inline-collapsed > .ant-menu-item span {
    display: none;
  }

  .ant-menu-vertical > .ant-menu-item {
    border-radius: 8px;
    @apply h-12 leading-12;
  }

  /* .ant-menu-vertical > .ant-menu-item:hover {
    background-color: rgba(15, 34, 67, 0.05);
  } */

  .ant-menu-inline-collapsed {
    width: 52px;
  }
}
</style>
