## 示例

## 基础用法
```vue
<template>
<div class="h-auto bg-mono-300 overflow-hidden p-3 flex">
  <MenuLeft class="w-64"
    :list="list"
    :defaultSelectedKeys="defaultSelectedKeys"
    @selectMenu="selectMenu"
    >
  </MenuLeft>
  <div class="flex-1"></div>
</div>
</template>
<script>
export default {
  name: 'MenuLeftDefault',
  data () {
    return {
      // 菜单列表
      list: [
        {
            key: "rule-template",
            title: "规则模板",
            iconName: 'data-quality'
        },
        {
            key: "rule-manage",
            title: "规则管理",
            iconName: 'dynamic-lib'
        }
      ],
      defaultSelectedKeys: ['rule-template']
    }
  },
  methods: {
    // 选中的菜单项
    selectMenu (obj) {
      console.log('选中的菜单项', obj.key, obj)
    }
  }
}
</script>
```

## 展开收起
```vue
<template>
<div class="h-auto bg-mono-300 overflow-hidden p-3 flex">
  <div class="left-menu-class" :class="{'left-menu-collapsed': collapsed}">
    <MenuLeft
      :list="list"
      :defaultSelectedKeys="defaultSelectedKeys"
      @selectMenu="selectMenu"
      :isCollapsed="collapsed"
      >
    </MenuLeft>
    <div class="icon-collapsed-btn" :class="{'is-collapsed-icon': collapsed}"   @click="toggleCollapsed">
      <hz-icon hzName="menu-hide" class="mr-2 w-5 h-5"></hz-icon>
    </div>
  </div>
  <div class="flex-1"></div>
</div>
</template>
<script>
export default {
  name: 'MenuLeftDefault',
  data () {
    return {
      // 收起状态
      collapsed: false,
      list: [
        {
            key: "rule-template",
            title: "规则模板",
            iconName: 'data-quality'
        },
        {
            key: "rule-manage",
            title: "规则管理",
            iconName: 'dynamic-lib'
        }
      ],
      defaultSelectedKeys: ['rule-template']
    }
  },
  methods: {
    // 选中的菜单项
    selectMenu (obj) {
      console.log('选中的菜单项', obj.key, obj)
    },
    // 展开收起菜单
    toggleCollapsed () {
      this.collapsed = !this.collapsed
    }
  }
}
</script>
<style scoped lang="scss">
.left-menu-class {
  background-color: #fbfbfb;
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  padding: 0 8px;
  height: 200px;
  width: 256px;
  transition: width 0.3s;
}
.icon-collapsed-btn {
  margin-left: 210px;
  display: flex;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 24px;
  cursor: pointer;
  padding-left: 8px;
  line-height: 48px;
  background-color: #eff0f4;
  margin-bottom: 8px;
}
.left-menu-collapsed {
  width: 68px;
}
.is-collapsed-icon {
  transform: rotate(180deg);
  margin: 0 auto;
}
</style>
```
