<template>
  <div class="section-head">
    {{title}}
    <div class="float-right">
      <slot name="info"></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'SectionHead',
  props: {
    title: {
      type: String,
      default: '标题'
    }
  }
}
</script>
<style scoped>
.section-head {
  @apply leading-8 text-type-900 font-semibold relative pl-3 text-sm
}
.section-head::after {
  content: '';
  @apply absolute w-1 h-2 rounded-sm bg-primary-900 left-0 top-3
}
</style>
