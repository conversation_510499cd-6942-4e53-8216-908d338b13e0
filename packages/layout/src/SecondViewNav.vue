<template>
  <div class="flex h-14  bg-background-100 justify-between" :class="{'shadow-container-c100': hasContainerShadow}">
    <div class="w-14 flex justify-center items-center hover:bg-mono-a-200 cursor-pointer" :class="{'hidden': backBtnHidden}" @click="back()">
      <hz-icon hzName="back" className="w-6 h-6" />
    </div>
    <slot name="title">
      <div class="leading-14 text-left flex-auto pl-4" :class="{'text-xl': titleSize !== 'base', 'text-base': titleSize === 'base'}">
        {{title}}
      </div>
    </slot>
    <div class="justify-center items-center flex pr-8">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'SecondViewNav',
  props: {
    title: String,
    backBtnHidden: Boolean,
    titleSize: String,
    hasContainerShadow: {
      default: true,
      type: Boolean
    }
  },
  methods: {
    back () {
      this.$emit('backhandle')
    }
  }
}
</script>
<style scoped>

</style>
