<template>
  <span>
    <span v-if="text && text.indexOf(keyword) > -1">
      {{text.substr(0, text.indexOf(keyword))}}<span class="text-primary-900">{{keyword}}</span>{{text.substr(text.indexOf(keyword) + keyword.length)}}
    </span>
    <span v-else>
      {{text}}
    </span>
  </span>
</template>
<script>
export default {
  name: 'TextLighthight',
  props: {
    text: String,
    keyword: String
  }
}
</script>
