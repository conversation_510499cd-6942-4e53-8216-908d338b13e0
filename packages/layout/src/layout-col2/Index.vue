<template>
  <div class="h-full subcol2-layout" :class="{'close': !showSider}">
    <div class="sider" >
      <div class="sider-content">
        <slot name="siderLayout" class="content"></slot>
      </div>
      <a class="slice-bar" @click="switchSlice()">
        <hz-icon hzName="next-batch" className="w-6 h-6" />
      </a>
    </div>
    <div class="main-layout flex">
      <slot name="mainLayout"></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LayoutCol2',
  data () {
    return {
      showSider: true
    }
  },
  methods: {
    switchSlice () {
      this.showSider = !this.showSider
    }
  }
}
</script>
<style scoped>
.subcol2-layout{
  @apply relative w-full;
}
.sider {
  @apply transform transition-transform w-79 h-full bg-background-200 absolute left-0 top-0;
}
.slice-bar {
  @apply absolute w-6 h-6 rounded-full bg-background-200 -right-2.5 top-16 transform transition-transform rotate-180 cursor-pointer;
  box-shadow: 0px 9.6px 28.8px -7.2px rgba(15, 34, 67, 0.16), 0px 1.2px 3.6px rgba(15, 34, 67, 0.12), 0px 0px 1.2px rgba(15, 34, 67, 0.16);
}
/* .slice-bar .slice-content {
  @apply transition-all;
} */
/* .slice-bar:hover {
  @apply border-1 border-solid border-primary-900
} */
.close .sider {
  @apply  -translate-x-full z-6;
  /* transform: translateX(calc(-100% + 16px)); */
}
.close .main-layout {
  @apply ml-0
}

.sider-content {
  @apply h-full;
}
/* .sider.close .sider-content {
  @apply -translate-x-full;
} */
.close .slice-bar {
  @apply rotate-0 -right-4
}
.main-layout {
  @apply ml-79 transform transition-all h-full;
}
</style>
