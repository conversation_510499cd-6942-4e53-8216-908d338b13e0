示例
```vue
<template>
  <div class="h-88 w-79"> 
    <TbBaseInfo :data="data"></TbBaseInfo>
  </div>
 </template>

<script>
import { Moment } from 'moment';
const data = [
  {
    title: '表基础信息',
    list: [
      {
        name: '表类型',
        value: '普通表'
      },
      {
        name: '数据来源',
        value: '数据源名称'
      },
      {
        name: '数据量',
        value: '12条'
      },
      {
        name: '数据说明',
        value: '数据来源：【被捺印指纹人员信息】【禁毒案件嫌疑人员指纹信息】'
      }
    ]
  },
  {
    title: '技术信息',
    list: [
      {
        name: '更新方式',
        value: '自动更新'
      },
      {
        name: '最近更新时间',
        value: '2022-04-04 12:23:23'
      },
      {
        name: '状态',
        type: 'tag',
        value: '成功',
        color: 'ant-tag-green'
      }
    ]
  }
]


export default {
  data() {
    return { 
      data: data
    }
  },
  methods: {
  }
}
</script>
```
