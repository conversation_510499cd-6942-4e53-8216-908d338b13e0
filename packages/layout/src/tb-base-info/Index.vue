<template>
  <div>
    <div v-for="(item, index) in data" :key="index" class="box-wrap">
      <h5 class="title">
        {{item.title}}
        <span class="transform rotate-90 absolute right-1 top-0.5" v-if="switchBtnShow">
          <hz-icon hzName="slicenav-arrow"></hz-icon>
        </span>
      </h5>
      <ul class="leading-6">
        <li v-for="(it, i) in item.list" :key="i" class="flex mb-1">
          <label class="w-22 text-type-700">{{it.name}}</label>
          <div class="flex-1 text-type-900 break-all" v-if="it.type !== 'tag'">
          <div v-if="!Array.isArray(it.value)" class="string inline-block">
            <!-- <span v-if="it.type === 'tb_type'" class="text-type-primary">{{ it.value }}</span> -->
            <a-tag class="ant-tag-primary" v-if="it.type === 'tb_storage_flag'" color="blue">{{ it.value }}</a-tag>
            <span v-else>{{ it.value }}</span>

          </div>
          <div v-else class="array">
            <!-- <div v-for="(im, k) in it.value" :key="k" class="flex-1 text-type-900 array">
              <a-tag :class="[im.tag_color]">{{im.tag_name}}</a-tag>
            </div> -->
            <template v-for="(tag, index) in it.value">
              <a-tooltip v-if="tag.tag_name.length > 10" :key="tag.tag_name" :title="tag.tag_name">
                <a-tag class="mr-0 bg-background-100 border border-mono-a-500 rounded-3xl mr-1 mb-1" :key="tag.tag_name" :closable="true" @close="handleClose(tag, $event)">
                  {{`${tag.tag_name.slice(0, 10)}...`}}
                </a-tag>
              </a-tooltip>
              <a-tag class="mr-0 bg-background-100 border border-mono-a-500 rounded-3xl align-top mr-1 mb-1" v-else :key="index" closable @close="handleClose(tag, $event)">
                {{tag.tag_name}}
              </a-tag>
            </template>

            <a-input
              v-if="inputVisible"
              class="block w-48"
              ref="input"
              type="text"
              size="small"
              placeholder="输入标签回车确认"
              :value="inputValue"
              :maxLength="32"
              @blur="handleBlur"
              @change="handleInputChange"
              @keyup.enter="handleInputConfirm(it.value)"
            />
            <!-- <a-tag v-else @click="showInput" style="background: #fff; borderStyle: dashed;">
              <a-icon type="plus" />添加标签
            </a-tag> -->
            <a-button v-else @click="showInput" class="block flex items-center" type="text"><hz-icon hzName="plus1" class="mr-1"></hz-icon>添加标签</a-button>
          </div>
          </div>
          <div v-if="it.type === 'tag'" class="flex-1">
            <a-tag :class="[it.color]" color="green">{{it.value}}</a-tag>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: 'TbBaseInfo',
  data () {
    return {
      inputVisible: false,
      inputValue: '',
      tags: []
    }
  },
  props: {
    data: {
      default: () => [],
      type: Array
    },
    switchBtnShow: {
      default: false,
      type: Boolean
    }
  },
  mounted () {
    console.log('data', this.data)
    // this.tags = this.data[0].list.filter(item => item.name === '标签')[0].value
  },
  methods: {
    handleClose (tag, e) {
      e.preventDefault()
      // tags = tags.filter(tag => tag !== removedTag)
      // this.tags = tags
      this.$emit('deleteTag', tag)
    },

    showInput () {
      this.inputVisible = true
      this.$nextTick(function () {
        this.$refs.input[0].focus()
      })
    },

    handleBlur () {
      this.inputVisible = false
      this.inputValue = ''
    },

    handleInputChange (e) {
      this.inputValue = e.target.value
    },

    handleInputConfirm (list) {
      const inputValue = this.inputValue.trim()
      const tags = list
      const hasTag = tags.filter(tag => tag.tag_name === inputValue).length > 0
      if (hasTag && inputValue) {
        this.$message.error('标签名不能重复')
        return
      }
      if (!inputValue) {
        this.$message.error('标签名不能为空')
        return
      }

      // if (inputValue && !hasTag) {
      //   this.data[0].list.filter(item => item.name === '标签')[0].value.splice(0,0, { tag_name: inputValue })
      // }
      this.$emit('addTag', { tag_name: inputValue })
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>
<style scoped>
  .title {
    @apply text-base font-semibold text-type-900 mb-3 relative pl-2;
  }
  .title::after {
    content:'';
    @apply absolute w-1 h-2 bg-primary-900 left-0 top-2 rounded;
  }
  .box-wrap{
    @apply mt-7;
  }
  .box-wrap:first-child{
    @apply mt-0;
  }
  .ant-tag-primary {
    @apply border border-solid border-primary-900 bg-transparent text-primary-900 rounded-md ;
  }
</style>
