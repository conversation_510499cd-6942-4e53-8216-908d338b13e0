示例

## 基础用法
```vue
<template>
<div class="h-auto bg-mono-300 overflow-hidden p-3">
  <HeaderLayout title="资产注册">
    <template v-slot:headerLayoutRight>
      <a-button type="primary">新建</a-button>
    </template>
  </HeaderLayout>
</div>
</template>
```
## 回退按钮
```vue
<template>
<div class="h-auto bg-mono-300 overflow-hidden p-3">
  <HeaderLayout :title="'资产注册'" :showLine="true" :backBtn="true" @back="back()">
    <template v-slot:headerLayoutRight>
        <a-button type="primary">Primary</a-button>
    </template>
  </HeaderLayout>
</div>
</template>
<script>
export default {
  name: 'headerLayoutBack',
  methods: {
    back () {
      alert('click back')
    }
  }
}
</script>
```
## 综合用法
```vue
<template>
<div class="h-auto bg-mono-300 overflow-hidden p-3">
  <HeaderLayout :title="'资产注册'" :showLine="true">
    <template v-slot:headerLayoutRight>
        <a-button type="primary">Primary</a-button>
    </template>
    <template v-slot:searchInfo>
      <div class="py-4 w-145 mx-auto">
        <a-input-search placeholder="input search text" />
      </div>
    </template>
  </HeaderLayout>
</div>
</template>
```
