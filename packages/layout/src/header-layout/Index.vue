<template>
  <div class="bg-background-300 shadow-container-c200 z-10">
    <div class="flex justify-between px-6" :class="{'border-b-1 border-solid border-mono-a-300': showLine}">
      <div class="flex-auto header-layout-left flex">
        <div v-if="backBtn" class="flex items-center cursor-pointer w-10" @click="back()">
          <hz-icon hzName="back" class="text-type-700" className="w-6 h-6 hz-action-icon"/>
        </div>
        <slot name="title">
          <h5 class="header-title-wrap">{{title}}</h5>
        </slot>
      </div>
      <div class="header-layout-right">
        <slot name="headerLayoutRight">
        </slot>
      </div>
    </div>
    <!-- @slot  导航搜索-->
    <slot name="searchInfo"></slot>
  </div>
</template>
<script>
import { hzIcon } from '@hertz/base-components'
export default {
  components: { hzIcon },
  name: 'HeaderLayout',
  data () {
    return {
      showSider: true
    }
  },
  props: {
    /**
     * 导航标题
     */
    title: {
      type: String,
      require: true
    },
    /**
     * 是否显示导航线
     */
    showLine: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示回退按钮
     */
     backBtn: {
      type: Boolean,
      default: false
     }
  },
  methods: {
    switchSlice () {
      this.showSider = !this.showSider
    },
    back () {
      this.$emit('back')
    }
  }
}
</script>
<style scoped>
  .header-layout-right {
    @apply pt-3;
  }
  .header-title-wrap {
    @apply leading-14 font-semibold text-base h-14 text-left text-type-900 mb-0;
  }
</style>
