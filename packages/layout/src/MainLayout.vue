<template>
  <div ref="mainlayout" class="flex h-full">
    <div class="sider">
      <slot name="siderLayout"></slot>
      <a class="drag-bar" v-if="canDrag"  draggable="true" @mousedown="dragElem"></a>
    </div>
    <div class="main-layout w-0 overflow-auto relative flex flex-col">
      <div class="m-4 rounded-md flex-auto flex flex-col">
        <slot name="mainLayout"></slot>
      </div>
    </div>
  </div>
</template>
<script>
import $ from 'cash-dom'
export default {
  name: 'MainLayout',
  data () {
    return {
      dragBarElem: '',
      left: '',
      right: '',
      maxWidth: 400,
      minWidth: 150
    }
  },
  props: {
    canDrag: {
      type: Boolean,
      default: false
    }
  },
  mounted () {
    if (this.canDrag) {
      this.dragBarElem = $(this.$refs.mainlayout).find('.drag-bar')
      this.left = $(this.$refs.mainlayout).find('.sider')
      this.right = $(this.$refs.mainlayout).find('.main-layout')
    }
  },
  methods: {
    dragElem (event) {
        const minWidth = this.minWidth
        const maxWidth = this.maxWidth
        // const delX = event.pageX - this.dragBarElem.outerWidth()
        const self = this
        event.preventDefault() // 防止文本选中
        const silderRect = this.left[0].getBoundingClientRect()
        // const silderLeft = this.left[0].getBounsingClientRect().left // silder元素距浏览器左侧宽度
        const silderLeft = silderRect.left
        $(document).on('mousemove', function (e) {
            let width = e.pageX - silderLeft
            width = Math.max(minWidth, width)
            width = Math.min(maxWidth, width)
            self.left.css('width', width + 'px')
        })
        $(document).on('mouseup', function (e) {
            $(document).off('mousemove')
            $(document).off('mouseup')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
  .sider{
    @apply h-full w-56 shadow-container-c200 relative overflow-visible;
  }
  .main-layout{
    @apply flex-1 w-0;
  }
  .drag-bar{
    @apply absolute h-full top-0 w-1.5 cursor-move z-10;
    right: -0.625rem;
    &::after{
      content: '';
      @apply absolute right-0 top-1/2 w-1 h-3.5 border-l-1 border-r-1 border-mono-600;
    }
  }
</style>
