<template>
  <div class="date-value-container">
    <a-date-picker :showToday="false" :disabled="noLimit" :allowClear="false" class="w-50" v-model="dateTime" @change="onTimeChange"/>
    <div class="date-hour-container w-24 inline-block mx-4 align-middle">
      <a-select class="block w-full" v-model="dateHour" :disabled="noLimit" @change="onHourChange">
        <a-select-option v-for="option in hourOptions " :key="option.value" :value="option.value">{{option.label}}</a-select-option>
      </a-select>
    </div>
    <a-checkbox v-if="noLimitBtnShow" v-model="noLimit" @change="onCheckboxChange">不限</a-checkbox>
  </div>
</template>
<script>
/** 日期选择范围
 * <AUTHOR>
 */
import moment from 'moment'
const hourOptions = [
  {
    value: '00:00:00',
    label: '00:00'
  },
  {
    value: '01:00:00',
    label: '01:00'
  },
  {
    value: '02:00:00',
    label: '02:00'
  },
  {
    value: '03:00:00',
    label: '03:00'
  },
  {
    value: '04:00:00',
    label: '04:00'
  },
  {
    value: '05:00:00',
    label: '05:00'
  },
  {
    value: '06:00:00',
    label: '06:00'
  },
  {
    value: '07:00:00',
    label: '07:00'
  },
  {
    value: '08:00:00',
    label: '08:00'
  },
  {
    value: '09:00:00',
    label: '09:00'
  },
  {
    value: '10:00:00',
    label: '10:00'
  },
  {
    value: '11:00:00',
    label: '11:00'
  },
  {
    value: '12:00:00',
    label: '12:00'
  },
  {
    value: '13:00:00',
    label: '13:00'
  },
  {
    value: '14:00:00',
    label: '14:00'
  },
  {
    value: '15:00:00',
    label: '15:00'
  },
  {
    value: '16:00:00',
    label: '16:00'
  },
  {
    value: '17:00:00',
    label: '17:00'
  },
  {
    value: '18:00:00',
    label: '18:00'
  },
  {
    value: '19:00:00',
    label: '19:00'
  },
  {
    value: '20:00:00',
    label: '20:00'
  },
  {
    value: '21:00:00',
    label: '21:00'
  },
  {
    value: '22:00:00',
    label: '22:00'
  },
  {
    value: '23:00:00',
    label: '23:00'
  }
]
/**
 * @displayName dateRange 日期范围选择
 */
export default {
  name: 'dateRange',
  components: {
  },
  props: {
    /** 日期 传值为null，选中不限
    */
    date: String,
    noLimitBtnShow: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      timeVal: '',
      hourOptions: hourOptions,
      showHourOptions: false,
      noLimit: false,
      dateTime: moment(new Date()).format('YYYY-MM-DD'),
      dateHour: '00:00:00'
    }
  },
  beforeMount () {},
  watch: {},
  created () {
    if (this.date == null) {
      this.noLimit = true
    } else {
      const arr = this.date.split(' ')
      this.dateHour = arr[1] || '00:00:00'
      this.dateTime = moment(new Date(arr[0] || ''), 'YYYY-MM-DD')
    }
  },
  mounted () {},
  computed: {},
  methods: {
    onTimeChange () {
      this.emitDate()
    },
    emitDate (value) {
      /**
     * 每次更新date值回调
     * @event changeVal
     * @type {string}
     * @property {string} called
      */
      this.$emit('changeVal', value ? null : moment(this.dateTime).format('YYYY-MM-DD') + ' ' + this.dateHour)
    },
    onCheckboxChange (e) {
      this.emitDate(e.target.checked)
    },
    onHourChange () {
      this.emitDate()
    }
  },
  destroyed () {
  }
}

</script>
<style scoped>
::v-deep .ant-calendar-picker:hover .has-value .ant-calendar-picker-icon {
  @apply opacity-100 cursor-pointer
}
</style>
