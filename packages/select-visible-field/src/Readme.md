示例
```vue
<template>
  <div>
    <selectVisibleField :fieldList="fieldList" :checkedIds="checkedIds" selectFieldMode="1" ref="selectVisibleField"></selectVisibleField>
    <button @click="getData" class="mt-4 font-semibold w-20 h-8 leading-8 bg-primary-a100 text-primary-900 cursor-pointer">确定</button>
  </div>
 </template>
 
<script>
const fieldList = [
  {
    name: '人员类别汉字syrklbhz',
    data_type: 'date',
    fid: 'fk98682415'
  },
  {
    name: '身份证件号码zjhm',
    data_type: 'string',
    fid: 'fk0cfe4cf2'
  },
  {
    name: '姓名xm',
    data_type: 'string',
    fid: 'fk929de238'
  }
]
const checkedIds = ['fk98682415']
export default {
  data() {
    return { 
     fieldList: fieldList,
     checkedIds: checkedIds
    }
  },
  methods: {
    getData () {
      console.log(this.$refs.selectVisibleField.getParamData())
    }
  }
}
</script>
```
