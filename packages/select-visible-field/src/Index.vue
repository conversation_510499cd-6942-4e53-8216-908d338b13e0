<template>
  <div class="select-visible-field-container w-full h-full">
    <div class="header" :class="placement==='right' ? 'search-right-container': 'search-bottom-container'">
      <span class="text-type-700 font-semibold mr-4 h-8 leading-8 inline-block">可见字段范围</span>
      <a-radio-group name="radioGroup" v-model="selectValue" @change="onRadioChange" class="h-8 leading-8">
        <a-radio :value="tab.key" v-for="tab in tabs" :key="tab.key">{{tab.name}}</a-radio>
      </a-radio-group>
      <a-input placeholder="请输入字段名称" v-model="serachText" :class="placement==='right' ? 'search-right': 'search-bottom'" :disabled="selectValue=='1'">
        <template #prefix>
          <hz-icon hzName="search" />
        </template>
      </a-input>
    </div>
    <div class="mt-2 overflow-auto" :class="placement==='right' ? 'h-calc10': 'h-calc20'">
      <fieldSetting :fieldList="fieldList" :field_ids="fieldIds" :searchText="serachText" ref="fieldSetting"/>
    </div>
  </div>
</template>
<script>
import { each as _each, map as _map, cloneDeep as _cloneDeep } from 'lodash'
import fieldSetting from '@hertz/fieldsetting'
/**
 * @displayName selectVisibleField 选择字段
 */
export default {
  name: 'selectVisibleField',
  components: {
    fieldSetting
  },
  props: {
    /**
     * 表数据，结构如下：
     * [{name: '**',fid:'**',data_type:'**'}]
     */
    fieldList: {
      type: Array,
      default: () => []
    },
    /**
     *  选中的字段
     */
    checkedIds: {
      type: Array,
      default: () => []
    },
    /**
     * 全部:1或者自定义: 2，如果是全部，checkedIds可以为[]
     */
    selectFieldMode: {
      type: String,
      default: ''
    },
    /**
     * 搜索框位置
     * @value right bottom
     */
    placement: {
      type: String,
      default: 'right'
    }
  },
  data () {
    return {
      fieldIds: [],
      serachText: '',
      tabs: [
        {
          name: '全部',
          key: '1'
        },
        {
          name: '自定义',
          key: '2'
        }
      ],
      selectValue: ''
    }
  },
  watch: {
    fieldList: {
      handler (val) {
        this.initData()
      },
      deep: true
    }
  },
  mounted () {
    this.initData()
  },
  methods: {
    initData () {
      this.fieldIds = this.checkedIds
      this.selectValue = this.selectFieldMode
      this.setDisabledField(this.selectValue)
      if (this.selectFieldMode === '1') {
        this.getAllChecked()
      }
    },
    onRadioChange (e) {
      this.setDisabledField(e.target.value)
      this.getAllChecked()
    },
    getAllChecked () {
      this.fieldIds = _map(this.fieldList, field => field.fid)
    },
    setDisabledField (value) {
      _each(this.fieldList, field => {
        field.disabled = value === '1'
      })
    },
    /**
     *  获取数据
     * @public This is a public method
     * @returns {object} {selectFieldMode: **,checkedIds:[]}
    */
    getParamData () {
      return {
        selectFieldMode: this.selectValue,
        checkedIds: _cloneDeep(this.$refs.fieldSetting.fid)
      }
    }
  }
}
</script>
<style scoped>
.search-right-container {
  @apply border-mono-a-300 border-b h-10 leading-8
}
.search-bottom-container {
  @apply h-16 leading-8
}
.search-right {
  @apply float-right w-40
}
.search-bottom {
  @apply block w-full
}
</style>
