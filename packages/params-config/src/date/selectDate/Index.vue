<template>
  <a-modal width="50rem" :visible="modalVisible" title="选择日期" @ok="handleOk" @cancel="cancelModal">
    <div>
      <div>
        <p class=" text-type-700 font-semibold mb-2">请选择日期</p>
        <dateRange :date="dateVal" :noLimitBtnShow="false" @changeVal="onChangeVal($event)"/>
      </div>
    </div>
  </a-modal>
</template>

<script>
import moment from 'moment'
import dateRange from '@hertz/date-range'
export default {
  name: 'SelectDate',
  components: {
    dateRange
  },
  props: {
    date: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dateVal: null,
      modalVisible: false
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if (!this.date) {
        this.dateVal = moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00'
      } else {
        this.dateVal = this.date
      }
      this.modalVisible = true
    },
    onChangeVal (event) {
      this.dateVal = event
    },
    handleOk () {
      this.$emit('callback', {
        status: 1,
        result: {
          date: this.dateVal
        }
      })
      this.modalVisible = false
    },
    cancelModal () {
      this.modalVisible = false
      this.$emit('callback', {
        status: 0,
        result: {}
      })
    }
  }
}
</script>
<style scoped>

</style>
