<template>
  <div class="flex">
    <a-select class="w-35 flex-shrink-0" v-if="paramsConfig.use_type === 1" v-model="dateoperator" placeholder="请选择" @change="changeDateOperator">
      <a-select-option v-for="(item,index) in dateOperatorOptions" :key="index" :value="item.type">{{item.name}}</a-select-option>
    </a-select>
    <!-- 15 绝对时间  -->
    <a-select class="w-30 ml-1 flex-shrink-0" v-model="operator" placeholder="请选择" v-if="dateoperator === 15 && paramsConfig.use_type === 1" @change="changeAbsoluteTime">
      <a-select-option v-for="(item,index) in absoluteTime" :key="index" :value="item.type">{{item.name}}</a-select-option>
    </a-select>
    <!-- 16 相对当前时间点  -->
    <a-select class="w-30 ml-1 flex-shrink-0" v-model="key" placeholder="请选择" v-if="dateoperator === 16" @change="changeRelativeTimePoint">
      <a-select-option v-for="(item,index) in relativeTimePoint" :key="index" :value="item.key">{{item.name}}</a-select-option>
    </a-select>
    <!-- 17 相对当前时间  -->
    <a-select class="w-30 ml-1 flex-shrink-0" v-model="operator" placeholder="请选择" v-if="dateoperator === 17" @change="changeRelativeTime" >
      <a-select-option v-for="(item,index) in relativeTime" :key="index" :value="item.type">{{item.name}}</a-select-option>
    </a-select>
    <div class="flex-auto flex ml-1" v-if="dateoperator === 15">
      <a-input class="w-50" v-if="operator !== 10" placeholder="选择时间" :disabled="operator === 8 || operator === 9" :readonly=true  v-model="paramValue" @click="changeDate" >
        <template #suffix >
          <hz-icon hzName="type-date" />
        </template>
      </a-input>
      <!-- <a-date-picker class="w-full" v-if="operator !== 10" v-model="paramValue" @change="changeDate" :disabled="operator === 8 || operator === 9" /> -->
      <a-input class="w-1/2" v-if="operator === 10" placeholder="选择开始时间" :readonly=true v-model="startDate" @click="changeDateRange" >
        <template #suffix >
          <hz-icon hzName="type-date" />
        </template>
      </a-input>
      <span class="mx-1 leading-8" v-if="operator === 10">~</span>
      <a-input class="w-1/2" v-if="operator === 10" placeholder="选择截止时间" :readonly=true v-model="endDate" @click="changeDateRange" >
        <template #suffix >
          <hz-icon hzName="type-date" />
        </template>
      </a-input>
    </div>
    <!-- 相对当前时间 -->
    <div class="flex ml-1 w-130" v-if="dateoperator == 17 && operator === 0">
      <a-select v-model="weekVal" class="w-25" @change="changeWeek($event,'week')">
        <a-select-option v-for="(item, index) in weekOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select>
      <a-select v-model="hourVal" class="w-20 ml-1" @change="changeTime($event, 'hour')">
        <a-select-option v-for="(item, index) in hourOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select><span class="mx-1 leading-8">点</span>
      <a-select v-model="minuteVal" class="w-20 ml-1" @change="changeTime($event, 'minute')">
        <a-select-option v-for="(item, index) in minutesOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select><span class="mx-1 leading-8">分</span>
    </div>
    <div class="flex ml-1 w-130 flex-shrink-0" v-if="dateoperator == 17 && operator === 10">
      <a-select v-model="startWeekVal" class="w-25" @change="changeWeek($event,'startWeek')">
        <a-select-option v-for="(item, index) in weekOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select>
      <a-select v-model="startHourVal" class="w-20 ml-1" @change="changeTime($event, 'startHour')">
        <a-select-option v-for="(item, index) in hourOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select><span class="mx-1 leading-8">点</span>
      <a-select v-model="startMinuteVal" class="w-20 ml-1" @change="changeTime($event, 'startMinute')">
        <a-select-option v-for="(item, index) in minutesOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select><span class="mx-1 leading-8">分</span>
      <span class="mx-1 leading-8">~</span>
      <a-select v-model="endWeekVal" class="w-25" @change="changeWeek($event,'endWeek')">
        <a-select-option v-for="(item, index) in weekOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select>
      <a-select v-model="endHourVal" class="w-20 ml-1" @change="changeTime($event, 'endHour')">
        <a-select-option v-for="(item, index) in hourOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select><span class="mx-1 leading-8">点</span>
      <a-select v-model="endMinuteVal" class="w-20 ml-1" @change="changeTime($event, 'endMinute')">
        <a-select-option v-for="(item, index) in minutesOptions" :key="index" :value="item.value">{{item.name}}</a-select-option>
      </a-select><span class="mx-1 leading-8">分</span>
    </div>
    <SelectDateRange ref="selectDateRangeModal" v-if="selectDateRangeModal" :startDate="startDate" :endDate="endDate" @callback="selectDateRangeModalCallback" />
    <SelectDate ref="selectDateModal" v-if="selectDateModal" :date="paramValue" @callback="selectDateModalCallback" />
  </div>
</template>
<script>
import { isEmpty, isNil } from 'lodash'
import SelectDateRange from './selectDateRange/Index.vue'
import SelectDate from './selectDate/Index.vue'
/**
 * @displayName ParamDateConfig 日期参数配置
 */
export default {
  name: 'ParamDateConfig',
  components: {
    SelectDateRange,
    SelectDate
  },
  props: {
    paramInfo: {
      type: Object,
      default: () => {}
    },
    paramVal: Object
  },
  data () {
    return {
      paramsConfig: {},
      dateoperator: null,
      operator: null,
      paramValue: null,
      startDate: null,
      endDate: null,
      selectDateRangeModal: false,
      selectDateModal: false,
      // 相对当前时间 等于
      hourVal: '00',
      minuteVal: '00',
      weekVal: 0,
      // 相对当前时间 范围
      startWeekVal: 0,
      startHourVal: '00',
      startMinuteVal: '00',
      endWeekVal: 0,
      endHourVal: '00',
      endMinuteVal: '00',
      key: undefined, // 相对当前时间点 - 昨天 | 最近3天
      dateOperatorOptions: [
        { type: 15, name: '绝对时间' },
        { type: 16, name: '相对当前时间点' },
        { type: 17, name: '相对当前时间' },
        { type: 18, name: '无' }
      ],
      absoluteTime: [
        { type: 0, name: '等于' },
        { type: 1, name: '不等于' },
        { type: 2, name: '大于' },
        { type: 3, name: '小于' },
        { type: 4, name: '大于等于' },
        { type: 5, name: '小于等于' },
        { type: 10, name: '选择日期范围' },
        { type: 8, name: '为空' },
        { type: 9, name: '不为空' }
      ], // 等于、选择日期范围、小于、大于、不等于、大于等于、小于等于、为空、不为空
      relativeTime: [
        { type: 0, name: '等于' },
        { type: 10, name: '选择日期范围' }
      ],
      relativeTimePoint: [
        { key: 0, value: 1, name: '昨天', granularity: 'day' },
        { key: 1, value: 0, name: '今天', granularity: 'day' },
        { key: 2, value: 3, name: '最近3天', granularity: 'day' },
        { key: 3, value: 7, name: '最近7天', granularity: 'day' },
        { key: 4, value: 15, name: '最近15天', granularity: 'day' },
        { key: 5, value: 1, name: '最近一个月', granularity: 'month' },
        { key: 6, value: 3, name: '最近三个月', granularity: 'month' },
        { key: 7, value: 0.5, name: '最近半年', granularity: 'year' },
        { key: 8, value: 1, name: '最近一年', granularity: 'year' }
      ],
      weekOptions: [
        { key: 0, value: 0, name: '每天', granularity: 'week' },
        { key: 1, value: 1, name: '每周一', granularity: 'week' },
        { key: 2, value: 2, name: '每周二', granularity: 'week' },
        { key: 3, value: 3, name: '每周三', granularity: 'week' },
        { key: 4, value: 4, name: '每周四', granularity: 'week' },
        { key: 5, value: 5, name: '每周五', granularity: 'week' },
        { key: 6, value: 6, name: '每周六', granularity: 'week' },
        { key: 7, value: 7, name: '每周日', granularity: 'week' }
      ],
      hourOptions: [],
      minutesOptions: []
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      // "param_var": "a",# 参数变量
      // "param_title": "字段a",# 参数名称
      // "param_value": "222",# 值（用户填写）
      // "param_type": 2,# 参数类型（参考建模）
      // "is_fill": 1,# 是否必填（0|非必填 1|必填）
      // "operator": 0,# 操作符（参考建模）
      // dateoperator  日期
      // start_date : "2022-11-15 01:00:00" | null 开始日期
      // end_date : "2022-11-30 23:00:00" | null 开始日期
      // key: 8, 相对时间点
      // "is_full_parm": 1,#
      // "use_type": 0
      // 相对当前时间
      // "week_day": 4,
      // "key": 4,
      // "param_value": "00:00",
      //  范围
      this.initHourOptions()
      this.initMinutesOptions()
      if (isEmpty(this.paramVal)) {
        this.paramsConfig = {
          param_title: this.paramInfo.var_title,
          param_var: this.paramInfo.var_name,
          param_value: '',
          is_fill: this.paramInfo.is_fill,
          operator: undefined,
          param_type: this.paramInfo.type,
          is_full_parm: 1,
          use_type: this.paramInfo.use_type
        }
        if (this.paramsConfig.use_type === 0) {
          this.normalDateInit()
        }
      } else {
        this.dateoperator = this.paramVal.dateoperator
        this.operator = this.paramVal.operator
        this.paramsConfig = { ...this.paramVal }
        if (this.paramsConfig.use_type === 0) {
          this.normalDateInit()
        }
        this.initDateVal()
      }
      this.emitHandle()
    },
    // 普通语法值初始化
    normalDateInit () {
      this.paramsConfig.dateoperator = this.dateoperator = 15
      this.paramsConfig.operator = this.operator = 0
    },
    // 初始化日期参数值
    initDateVal () {
      // 绝对时间
      if (this.paramsConfig.dateoperator === 15) {
        if (this.paramsConfig.operator === 10) {
          this.startDate = this.paramsConfig.start_date
          this.endDate = this.paramsConfig.end_date
        } else if (this.paramsConfig.operator === 8 || this.paramsConfig.operator === 9) {
          this.paramValue = this.paramsConfig.param_value = ''
        } else {
          this.paramValue = this.paramsConfig.param_value
        }
      }
      // 相对当前时间点
      if (this.paramsConfig.dateoperator === 16) {
        this.key = this.paramsConfig.key
        this.paramsConfig.operator = undefined
      }
      // 相对当前时间
      if (this.paramsConfig.dateoperator === 17) {
        // operator 0 等于
        // hourVal: '00'
        // minuteVal: '00'
        // weekVal: 0

        if (this.paramsConfig.operator === 0) {
          this.weekVal = this.paramsConfig.start_week
          const timeVal = this.paramsConfig.param_value.split(':')
          this.hourVal = timeVal[0]
          this.minuteVal = timeVal[1]
        }
        // operator 17 时间范围
        // start_value: "01:01"
        // start_week : 1
        // startWeekVal: 0,
        // startHourVal: '00',
        // startMinuteVal: '00',
        // endWeekVal: 0,
        // endHourVal: '00',
        // endMinuteVal: '00',
        // end_value: "05:05"
        // end_week: 5
        if (this.paramsConfig.operator === 10) {
          this.startWeekVal = this.paramsConfig.start_week
          this.endWeekVal = this.paramsConfig.end_week
          const startValue = this.paramsConfig.start_value.split(':')
          this.startHourVal = startValue[0]
          this.startMinuteVal = startValue[1]
          const endValue = this.paramsConfig.end_value.split(':')
          this.endHourVal = endValue[0]
          this.endMinuteVal = endValue[1]
        }
      }
    },
    initHourOptions () {
      const arr = []
      for (let i = 0; i <= 23; i++) {
        arr.push({
          value: i < 10 ? `0${i}` : i.toString(),
          name: i < 10 ? `0${i}` : i.toString()
        })
      }
      this.hourOptions = [...arr]
    },
    initMinutesOptions () {
      const arr = []
      for (let i = 0; i <= 59; i++) {
        arr.push({
          value: i < 10 ? `0${i}` : i.toString(),
          name: i < 10 ? `0${i}` : i.toString()
        })
      }
      this.minutesOptions = [...arr]
    },
    changeDateOperator (event) {
      this.paramsConfig.dateoperator = event
      if (event === 17) {
        this.paramsConfig.operator = this.operator = 0
        this.paramsConfig.start_week = this.weekVal
        this.paramsConfig.param_value = `${this.hourVal}:${this.minuteVal}`
      } else {
        this.operator = undefined
      }
      this.paramValue = ''
      this.emitHandle()
    },
    // changeDate (event) {
    //   this.paramsConfig.param_value = event.format('YYYY-MM-DD')
    //   this.emitHandle()
    // },
    changeAbsoluteTime (event) {
      if (event === 8 || event === 9) {
        this.paramValue = null
      }
      if (event !== 10) {
        delete this.paramsConfig.start_date
        delete this.paramsConfig.end_date
      } else {
        this.paramsConfig.start_date = this.startDate
        this.paramsConfig.end_date = this.endDate
      }
      this.paramsConfig.operator = event
      this.emitHandle()
    },
    changeDateRange () {
      this.selectDateRangeModal = true
    },
    selectDateRangeModalCallback (data) {
      if (data.status) {
        this.paramsConfig.start_date = this.startDate = data.result.startDate
        this.paramsConfig.end_date = this.endDate = data.result.endDate
      }
      this.selectDateRangeModal = false
      this.emitHandle()
    },
    changeDate () {
      this.selectDateModal = true
    },
    selectDateModalCallback (data) {
      if (data.status) {
        this.paramsConfig.param_value = this.paramValue = data.result.date
      }
      this.selectDateModal = false
      this.emitHandle()
    },
    // 相对当前时间点
    changeRelativeTimePoint (event) {
      this.paramsConfig.key = event
      const config = this.relativeTimePoint.find((item) => item.key === event)
      this.paramsConfig.param_value = config.value
      this.paramsConfig.granularity = config.granularity
      this.paramsConfig.operator = 10
      delete this.paramsConfig.start_date
      delete this.paramsConfig.end_date
      this.emitHandle()
    },
    // 相对当前时间
    changeRelativeTime (event) {
      this.paramsConfig.operator = event
      if (event === 0) {
        this.paramsConfig.start_week = 0
        this.paramsConfig.param_value = `${this.hourVal}:${this.minuteVal}`
      }
      if (event === 10) {
        this.paramsConfig.start_week = this.startWeekVal
        this.paramsConfig.end_week = this.endWeekVal
        this.paramsConfig.start_value = `${this.startHourVal}:${this.startMinuteVal}`
        this.paramsConfig.end_value = `${this.endHourVal}:${this.endMinuteVal}`
      }
      this.emitHandle()
    },
    changeWeek (event, type) {
      switch (type) {
        case 'week':
          this.paramsConfig.start_week = event
          this.paramsConfig.param_value = `${this.hourVal}:${this.minuteVal}`
          break
        case 'startWeek':
          this.paramsConfig.start_week = event
          this.paramsConfig.start_value = `${this.startHourVal}:${this.startMinuteVal}`
          this.paramsConfig.end_value = `${this.endHourVal}:${this.endMinuteVal}`
          break
        case 'endWeek':
          this.paramsConfig.end_week = event
          this.paramsConfig.start_value = `${this.startHourVal}:${this.startMinuteVal}`
          this.paramsConfig.end_value = `${this.endHourVal}:${this.endMinuteVal}`
          break
      }
      this.emitHandle()
    },
    changeTime (event, type) {
      switch (type) {
        case 'hour':
        case 'minute':
          this.paramsConfig.param_value = `${this.hourVal}:${this.minuteVal}`
          break
        case 'startHour':
        case 'startMinute':
        case 'endHour':
        case 'endMinute':
          this.paramsConfig.start_value = `${this.startHourVal}:${this.startMinuteVal}`
          this.paramsConfig.end_value = `${this.endHourVal}:${this.endMinuteVal}`
          break
      }
      this.emitHandle()
    },
    checkHandle () {
      const res = {
        status: 1,
        msg: ''
      }
      if (this.paramsConfig.is_fill === 0 && isNil(this.paramsConfig.dateoperator)) {
        return res
      }
      if (this.paramsConfig.is_fill === 1 && (this.paramsConfig.dateoperator === undefined || this.paramsConfig.dateoperator === 18)) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}必填参数不能设置为无`
      }
      if (this.paramsConfig.dateoperator === undefined) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}未配置`
      }
      if (this.paramsConfig.dateoperator === 15 && this.paramsConfig.operator === undefined) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}未配置`
      }
      if (this.paramsConfig.dateoperator === 15 && (this.paramsConfig.operator !== undefined && this.paramsConfig.operator <= 5) && !this.paramsConfig.param_value) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}未配置`
      }
      if (this.paramsConfig.dateoperator === 15 && this.paramsConfig.operator === 10 && (!this.paramsConfig.start_date || !this.paramsConfig.end_date)) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}未配置`
      }
      if (this.paramsConfig.dateoperator === 16 && isNil(this.paramsConfig.key)) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}未配置`
      }
      return res
    },
    emitHandle () {
      // { type: 15, name: '绝对时间' },
      // { type: 16, name: '相对当前时间点' },
      // { type: 17, name: '相对当前时间' },
      // { type: 18, name: '无' }

      if (this.paramsConfig.dateoperator === 16 || this.paramsConfig.dateoperator === 17 || this.paramsConfig.dateoperator === 18) {
        delete this.paramsConfig.start_date
        delete this.paramsConfig.end_date
      }
      if (this.paramsConfig.dateoperator === 15 || this.paramsConfig.dateoperator === 16 || this.paramsConfig.dateoperator === 18) {
        delete this.paramsConfig.week_day
        delete this.paramsConfig.start_week
        delete this.paramsConfig.end_week
        delete this.paramsConfig.start_value
        delete this.paramsConfig.end_value
        if (this.paramsConfig.operator === 8 || this.paramsConfig.operator === 9) {
          this.paramsConfig.param_value = ''
        }
      }
      if (this.paramsConfig.dateoperator === 17) {
        if (this.paramsConfig.operator === 0) {
          // delete this.paramsConfig.start_week
          delete this.paramsConfig.end_week
          delete this.paramsConfig.start_value
          delete this.paramsConfig.end_value
        }
        if (this.paramsConfig.operator === 10) {
          delete this.paramsConfig.week_day
          delete this.paramsConfig.param_value
        }
      }
      const resData = this.checkHandle()
      if (!this.paramsConfig.dateoperator) {
        this.paramsConfig.dateoperator = ''
        this.paramsConfig.operator = ''
        this.paramsConfig.param_value = ''
      }
      resData.result = { ...this.paramsConfig }
      this.$emit('change', resData)
    }
  }
}
</script>
<style scoped>

</style>
