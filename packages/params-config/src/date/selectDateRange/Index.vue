<template>
  <a-modal width="50rem" :visible="modalVisible" title="选择日期" @ok="handleOk" @cancel="cancelModal">
    <div>
      <div>
        <p class=" text-type-700 font-semibold mb-2">请选择开始日期</p>
        <dateRange :date="startDateVal" :noLimitBtnShow="false" @changeVal="onChangeStartVal($event)"/>
      </div>
      <div class="mt-4">
        <p class=" text-type-700 font-semibold mb-2">请选择截止日期</p>
        <dateRange :date="endDateVal" :noLimitBtnShow="false" @changeVal="onChangeEndVal($event)"/>
      </div>
    </div>
  </a-modal>
</template>

<script>
import moment from 'moment'
import dateRange from '@hertz/date-range'
export default {
  name: 'SelectDateRange',
  components: {
    dateRange
  },
  props: {
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      startDateVal: null,
      endDateVal: null,
      modalVisible: false
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if (!this.startDate) {
        this.startDateVal = moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00'
        this.endDateVal = moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.startDateVal = this.startDate
        this.endDateVal = this.endDate
      }
      this.modalVisible = true
    },
    onChangeStartVal (event) {
      this.startDateVal = event
    },
    onChangeEndVal (event) {
      this.endDateVal = event
    },
    handleOk () {
      this.$emit('callback', {
        status: 1,
        result: {
          startDate: this.startDateVal,
          endDate: this.endDateVal
        }
      })
      this.modalVisible = false
    },
    cancelModal () {
      this.modalVisible = false
      this.$emit('callback', {
        status: 0,
        result: {}
      })
    }
  }
}
</script>
<style scoped>

</style>
