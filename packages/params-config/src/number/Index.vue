<template>
  <div class="flex">
    <a-select class="w-30" @change="changeOperator" v-if="paramsConfig.use_type === 1" v-model="operator">
      <a-select-option :key="index" :value="item.type" v-for="(item,index) in operatorOptions">{{item.name}}</a-select-option>
    </a-select>
    <div class="flex-auto ml-2 w-0" v-if="operator !== 10">
      <a-input class="w-full" :disabled="operator === 8 || operator === 9" v-model="paramValue" @change="changeVal"/>
    </div>
    <div class="flex-auto ml-2 flex w-0" v-if="operator === 10">
      <a-input class="flex-auto" v-model="startValue" @change="changeStartVal"/>
      <span class="mx-2 leading-8">~</span>
      <a-input class="flex-auto" v-model="endValue" @change="changeEndVal"/>
    </div>
  </div>
</template>
<script>
import { isEmpty, isNil } from 'lodash'
/**
 * @displayName ParamNumberConfig 数值参数配置
 */
export default {
  name: 'ParamNumberConfig',
  props: {
    paramInfo: {
      type: Object,
      default: () => {}
    },
    paramVal: Object
  },
  data () {
    return {
      operatorOptions: [
        { type: 0, name: '等于' },
        { type: 1, name: '不等于' },
        { type: 2, name: '大于' },
        { type: 3, name: '小于' },
        { type: 4, name: '大于等于' },
        { type: 5, name: '小于等于' },
        { type: 10, name: '区间' },
        { type: 8, name: '为空' },
        { type: 9, name: '不为空' }
      ],
      paramValue: '',
      operator: undefined,
      startValue: '',
      endValue: '',
      paramsConfig: {}
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      // "param_var": "a",# 参数变量
      // "param_title": "字段a",# 参数名称
      // "param_value": "222",# 值（用户填写）
      // "param_type": 2,# 参数类型（参考建模）
      // "is_fill": 1,# 是否必填（0|非必填 1|必填）
      // "operator": 0,# 操作符（参考建模）
      // "is_full_parm": 1,#
      // "use_type": 0
      if (isEmpty(this.paramVal)) {
        // eslint-disable-next-line vue/no-mutating-props
        this.paramsConfig = {
          param_title: this.paramInfo.var_title,
          param_var: this.paramInfo.var_name,
          param_value: '',
          is_fill: this.paramInfo.is_fill,
          operator: undefined,
          param_type: this.paramInfo.type,
          is_full_parm: 1,
          use_type: this.paramInfo.use_type
        }
        if (this.paramsConfig.use_type === 0) {
          this.paramsConfig.operator = this.operator = 0
        }
      } else {
        // this.paramsConfig = { ...this.paramVal }
        this.paramsConfig = Object.assign(this.paramInfo, this.paramVal)
        this.operator = this.paramsConfig.operator
        if (this.paramsConfig.use_type === 0) {
          this.paramsConfig.operator = this.operator = 0
        }
        if (this.paramsConfig.operator === 10) {
          this.startValue = this.paramsConfig.start_value
          this.endValue = this.paramsConfig.end_value
        } else {
          this.paramValue = this.paramsConfig.param_value
        }
      }
      this.emitHandle()
    },
    checkHandle () {
      const res = {
        status: 1,
        msg: ''
      }
      // 非必填字段
      if (this.paramsConfig.is_fill === 0 && (isNil(this.paramsConfig.operator) || this.paramsConfig.operator === '') && !this.paramsConfig.param_value) {
        return res
      }
      if (this.paramsConfig.operator !== 8 && this.paramsConfig.operator !== 9 && this.paramsConfig.operator !== 10 && !this.paramsConfig.param_value) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}不能为空`
      }
      if (this.paramsConfig.operator === 10) {
        if (isNil(this.paramsConfig.start_value) || isNil(this.paramsConfig.end_value)) {
          res.starus = 0
          res.msg = `${this.paramsConfig.param_title}不能为空`
        }
        if (this.paramsConfig.start_value > this.paramsConfig.end_value) {
          res.status = 0
          res.msg = `${this.paramsConfig.param_title}截止值小于起始值`
        }
      }
      return res
    },
    emitHandle () {
      const resData = this.checkHandle()
      if (isNil(this.paramsConfig.operator)) {
        this.paramsConfig.operator = ''
        this.paramsConfig.param_value = ''
      }
      resData.result = { ...this.paramsConfig }
      this.$emit('change', resData)
    },
    changeOperator (event) {
      this.paramsConfig.operator = event
      if (event === 10) {
        delete this.paramsConfig.param_value
        this.paramValue = ''
      } else {
        delete this.paramsConfig.start_value
        delete this.paramsConfig.end_value
      }
      if (event === 8 || event === 9) {
        this.paramsConfig.param_value = this.paramValue = ''
      }
      this.emitHandle()
    },
    changeVal () {
      this.paramsConfig.param_value = Number(this.paramValue)
      this.emitHandle()
    },
    changeStartVal () {
      this.paramsConfig.start_value = Number(this.startValue)
      this.emitHandle()
    },
    changeEndVal () {
      this.paramsConfig.end_value = Number(this.endValue)
      this.emitHandle()
    }
  }
}
</script>
<style scoped>

</style>
