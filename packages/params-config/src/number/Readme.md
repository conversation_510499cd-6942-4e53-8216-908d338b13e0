示例

## 基础用法
```vue
<template>
<div class="h-auto p-3 w-1/3">
  <ParamNumberConfig :paramInfo="paramsInfo" :paramVal="paramVal" @change="change" />
  <pre>{{paramVal}}</pre>
</div>
</template>
<script>
export default {
  data() {
    return { 
      paramsInfo: {
        type: 1, // 数值
        desc: "年龄",
        unit: "",
        var_name: "age",
        var_title: "年龄",
        default_val: "18",
        is_fill: 0
      },
      paramVal: {}
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.paramVal = {
        param_title: this.paramsInfo.var_title,
        param_value: 15,
        is_fill: this.paramsInfo.is_fill,
        start_value: 5,
        end_value: 35,
        operator: 10,
        param_type: this.paramsInfo.type,
        is_full_parm: 1,
        use_type: 1
      }
    },
    change (data) {
      console.log('change', data)
      this.paramVal = data.result
    }
  }
}
</script>
```