<template>
  <div class="flex">
    <a-select class="w-30"  v-if="paramsConfig.use_type === 1" @change="changeOperator" v-model="operator">
      <a-select-option :key="index" :value="item.type" v-for="(item,index) in operatorOptions">{{item.name}}</a-select-option>
    </a-select>
    <a-input class="flex-auto ml-2" :disabled="operator === 8 || operator === 9" v-model="paramValue" @change="changeVal"/>
  </div>
</template>
<script>
import { isEmpty, isNil } from 'lodash'
/**
 * @displayName ParamStringConfig 字符串参数配置
 */
export default {
  name: 'ParamStringConfig',
  props: {
    paramInfo: {
      type: Object,
      default: () => {}
    },
    paramVal: Object
  },
  data () {
    return {
      operatorOptions: [
        { type: 0, name: '等于' },
        { type: 1, name: '不等于' },
        { type: 6, name: '包含' },
        { type: 7, name: '不包含' },
        { type: 13, name: '头匹配' },
        { type: 14, name: '尾匹配' },
        { type: 8, name: '为空' },
        { type: 9, name: '不为空' }
      ],
      paramValue: '',
      operator: undefined,
      paramsConfig: {}
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      // "param_var": "a",# 参数变量
      // "param_title": "字段a",# 参数名称
      // "param_value": "222",# 值（用户填写）
      // "param_type": 2,# 参数类型（参考建模）
      // "is_fill": 1,# 是否必填（0|非必填 1|必填）
      // "operator": 0,# 操作符（参考建模）
      // "is_full_parm": 1,#
      // "use_type": 0
      if (isEmpty(this.paramVal)) {
        this.paramsConfig = {
          param_title: this.paramInfo.var_title,
          param_var: this.paramInfo.var_name,
          is_fill: this.paramInfo.is_fill,
          operator: undefined,
          param_type: this.paramInfo.type,
          is_full_parm: 1,
          use_type: this.paramInfo.use_type
        }
        if (this.paramsConfig.use_type === 0) {
          this.operator = this.paramsConfig.operator = 0
        }
      } else {
        this.paramsConfig = { ...this.paramVal }
        if (this.paramsConfig.use_type === 0) {
          this.paramsConfig.operator = this.operator = 0
        }
        this.operator = this.paramsConfig.operator
        this.paramValue = this.paramsConfig.param_value
      }
      this.emitHandle()
    },
    checkHandle () {
      const res = {
        status: 1,
        msg: ''
      }
      // 非必填字段
      if (this.paramsConfig.is_fill === 0 && (isNil(this.paramsConfig.operator) || this.paramsConfig.operator === '') && !this.paramsConfig.param_value) {
        return res
      }
      if (this.paramsConfig.operator !== 8 && this.paramsConfig.operator !== 9 && (!this.paramsConfig.param_value || !this.paramsConfig.param_value.trim())) {
        res.status = 0
        res.msg = `${this.paramsConfig.param_title}不能为空`
      }
      return res
    },
    emitHandle () {
      const resData = this.checkHandle()
      if (isNil(this.paramsConfig.operator)) {
        this.paramsConfig.operator = ''
        this.paramsConfig.param_value = ''
      }
      resData.result = { ...this.paramsConfig }
      this.$emit('change', resData)
    },
    changeOperator (event) {
      this.paramsConfig.operator = event
      if (event === 8 || event === 9) {
        this.paramsConfig.param_value = this.paramValue = ''
      }
      this.emitHandle()
    },
    changeVal () {
      this.paramsConfig.param_value = this.paramValue
      this.emitHandle()
    }
  }
}
</script>
<style scoped>

</style>
