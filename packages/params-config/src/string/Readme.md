示例

## 基础用法
```vue
<template>
<div class="h-auto p-3 w-1/3">
  <ParamStringConfig :paramInfo="paramsInfo" :paramVal="paramVal" @change="change" />
  <pre>{{paramVal}}</pre>
</div>
</template>
<script>
export default {
  data() {
    return { 
      paramsInfo: {
        type: 2, // 字符串
        desc: "省份信息",
        unit: "",
        var_name: "province",
        var_title: "省份",
        default_val: "北京",
        is_fill: 0
      },
      paramVal: {}
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.paramVal = {
        param_title: this.paramsInfo.var_title,
        param_var: '上海',
        is_fill: this.paramsInfo.is_fill,
        operator: 1,
        param_type: this.paramsInfo.type,
        is_full_parm: 1,
        use_type: 1
      }
    },
    change (data) {
      console.log('change', data)
      this.paramVal = data.result
    }
  }
}
</script>
```