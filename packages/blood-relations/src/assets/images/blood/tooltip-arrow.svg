<svg width="16" height="7" viewBox="0 0 16 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<g filter="url(#filter0_ddd)">
<path d="M8 1L2 7H14L8 1Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_ddd" x="-24" y="-9" width="64" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0588235 0 0 0 0 0.133333 0 0 0 0 0.262745 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect2_dropShadow"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0588235 0 0 0 0 0.133333 0 0 0 0 0.262745 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feMorphology radius="6" operator="erode" in="SourceAlpha" result="effect3_dropShadow"/>
<feOffset dy="16"/>
<feGaussianBlur stdDeviation="16"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0588235 0 0 0 0 0.133333 0 0 0 0 0.262745 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="effect2_dropShadow" result="effect3_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="16" height="7" fill="white"/>
</clipPath>
</defs>
</svg>
