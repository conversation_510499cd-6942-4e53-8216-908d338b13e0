<template>
  <div>
    <a-dropdown placement="topLeft" :trigger="['click']" v-model="show">
      <a-button type="primary-text" class="legend-btn"><hz-icon hzName="layer-ctrl"  class="mr-1"></hz-icon>画布图例</a-button>
      <template #overlay>
        <div class="legend-content">
          <hz-icon hzName="close-dense" class="close-icon" className="hz-action-icon" @click="show = false"></hz-icon>
          <div class="font-semibold">画布图例</div>
          <div>
            <p>节点类型</p>
            <ul class="flex flex-wrap">
              <li v-for="item in nodeList" :class="{'w-1/2': mode === 'index', 'w-1/3': mode === 'table'}" :key="item.key">
                <hz-icon :hzName="item.icon" :className="item.iconColor || ''"></hz-icon> {{ item.name }}
              </li>
            </ul>
          </div>
          <div>
            <p>连线类型</p>
            <ul class="type-line">
              <li v-for="item in links" :key="item.key" :class="{'w-full': mode === 'index', 'w-1/2': mode === 'table'}">
                <span v-if="!item.desc">{{ item.name }}</span>
                <span class="tag"><label>{{ item.label }}</label></span>
                <span v-if="item.desc" class="ml-8">{{ item.desc }}</span>
              </li>
            </ul>
          </div>
        </div>
      </template>
    </a-dropdown>
  </div>
</template>
<script>
export default {
  name: 'bloodLegend',
  props: {
    mode: {
      type: String,
      default: 'table'
    }
  },
  data () {
    return {
      show: false,
      nodeList: [],
      nodeDefine: [
        {
          key: 'normal-table',
          name: '普通表',
          icon: 't-normal',
          display: ['index', 'table']
        },
        {
          key: 'online-table',
          name: '在线表',
          icon: 't-online',
          display: ['table']
        },
        {
          key: 'flow-table',
          name: '流式表',
          icon: 't-flow',
          display: ['table']
        },
        {
          key: 'atom-index',
          name: '原子指标',
          icon: 'indicator-bloodline',
          iconColor: 'text-cerulean-900',
          display: ['index']
        },
        {
          key: 'derive-index',
          name: '派生指标',
          icon: 'indicator-bloodline',
          iconColor: 'text-teal-900',
          display: ['index']
        },
        {
          key: 'composite-index',
          name: '复合指标',
          icon: 'indicator-bloodline',
          iconColor: 'text-violet-900',
          display: ['index']
        }
      ],
      links: [],
      linkDefine: [
        {
          key: 'map',
          name: '映射',
          label: '映射',
          display: ['table']
        },
        {
          key: 'model',
          name: '模型',
          label: '模型',
          display: ['table']
        },
        {
          key: 'label',
          name: '标签',
          label: '标签',
          display: ['table']
        },
        {
          key: 'quote',
          name: '引用',
          desc: '上游依赖',
          label: '引用',
          display: ['table', 'index']
        },
        {
          key: 'rule',
          name: '规则',
          desc: '下游数据',
          label: '规则',
          display: ['table', 'index']
        },
      ]
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      this.nodeList = this.nodeDefine.filter(item => item.display.indexOf(this.mode) > -1)
      this.links = this.linkDefine.filter(item => item.display.indexOf(this.mode) > -1)
    }
  }
}
</script>
<style scoped>
.legend-btn {
  @apply bg-mono-a-100 border-1 border-solid border-primary-900 px-2;
}
.legend-btn:hover {
  @apply border-1 border-solid border-primary-900;
}
.legend-content {
  @apply w-64 px-6 py-4 bg-mono-100 relative text-sm
}
.close-icon {
  @apply absolute right-3 top-4 cursor-pointer
}
.legend-content ul .hz-icon {
  @apply -mt-px
}
.legend-content li {
  @apply mb-2;
}
.legend-content p {
  @apply text-type-600 mt-4 mb-2 font-semibold
}
.type-line {
  @apply flex flex-wrap
}
.type-line li {
  @apply mb-2 flex;
}
.type-line span.tag {
  @apply relative inline-block w-8 h-4.5 align-text-bottom ml-4
}
.type-line span.tag label {
  @apply  text-type-700 border-1 border-solid border-type-700 inline-block w-8 left-0 top-0 bg-mono-100 rounded-5.5 text-center text-xs absolute z-6 transform scale-90
}
.type-line span.tag::before {
  content: '';
  @apply w-13 h-0.25 bg-type-700 absolute top-1/2 -left-2.5 z-0
}
</style>
