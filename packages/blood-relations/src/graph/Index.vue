<template>
  <div id="graph-canvas" class="w-full h-full flex relative">
    <div class="absolute right-2 bottom-2 flex">
      <div class="w-30 scale-box">
        <span class="plus" @click="scaleGraph('+')">
          <hz-icon hzName="plus1"/>
        </span>
        <input type="text" disabled :value="scaleValue"/>
        <span class="reduce" @click="scaleGraph('-')">
          <hz-icon hzName="arrow-fair1"/>
        </span>
      </div>
      <bloodLegend class="legend " :mode="mode"></bloodLegend>
    </div>
  </div>
</template>
<script>
import G6 from '@antv/g6'
import { cloneDeep, merge } from 'lodash'
import { defaultConfig as g6DefaultConfig, defaultTooltipConfig } from './service/config.js'
import { registerTableNode, registerEdge, initEvent, registerFieldNode, registerIndexNode } from './service/handle.js'
import { registerTooltip } from './service/tooltip.js'
import bloodLegend from '../legend/Index.vue'
const defaultConfig = cloneDeep(g6DefaultConfig)
let grahpInstance = null
/**
 * <AUTHOR>
 * @displayName bloodGraph 血缘关系组件
 */
export default {
  name: 'bloodGraph',
  components: {
    bloodLegend
  },
  props: {
    mode: {
      type: String,
      default: 'table'
    }
  },
  data () {
    return {
      // 记录节点是请求过数据
      nodeIsRequest: {},
      currentModelX: null,
      currentModelY: null,
      scaleValue: '100%'
    }
  },
  methods: {
    initGraph ({ data, statisticData, nodeType, tooltipConfig }) {
      if (!data) return
      merge(defaultTooltipConfig, tooltipConfig)
      this.destroyGrahp()
      const tooltip = registerTooltip(nodeType)
      if (nodeType === 'table') {
        registerTableNode(statisticData)
      } else if (nodeType === 'index') {
        registerIndexNode(statisticData)
      } else {
        registerFieldNode(statisticData)
      }
      registerEdge(nodeType)
      const elem = document.getElementById('graph-canvas')
      defaultConfig.width = elem.clientWidth
      defaultConfig.height = elem.clientHeight

      defaultConfig.defaultNode.type = `${nodeType}-node`
      grahpInstance = new G6.Graph({
        container: elem,
        ...defaultConfig,
        plugins: [tooltip]
      })
      initEvent(grahpInstance, this.handleCollapse)
      grahpInstance.data(data)
      grahpInstance.render()
    },
    // destroy 用官方文档的方法 和 网上各种方式无法刷新  暂时出此下策
    destroyGrahp () {
      if (grahpInstance) {
        grahpInstance.destroy()
      }
    },
    // 刷新
    refreshAll () {
      if (grahpInstance) {
        grahpInstance.cfg.data.nodes.forEach(item => {
          const el = grahpInstance.findById(item.id)
          el._cfg.model.label = item.field_name
          grahpInstance.refreshItem(el)
          console.log('重绘 G6 refreshAll', el, grahpInstance)
        })
      }
    },
    handleCollapse (e, collapseField) {
      if (collapseField === 'open') {
        this.$emit('nodeClick', e)
        return
      }
      const target = e.target
      const id = target.get('modelId') || e.item.getModel().id
      const item = grahpInstance.findById(id)
      const nodeModel = item.getModel()
      // 当前是折叠状态true 应该展开 当前是展开状态 应该收起
      // 隐藏相邻节点
      // 请求数据
      // const isAdd = false
      if (nodeModel.node_position !== 1 && nodeModel.can_extract && nodeModel[collapseField] && !this.nodeIsRequest[nodeModel.id]) {
        this.nodeIsRequest[nodeModel.id] = true
        this.getData(nodeModel, collapseField)
      } else {
        this.reLayout(id, collapseField)
      }
    },
    reLayout (id, collapseField) {
      const item = grahpInstance.findById(id)
      const nodeModel = item.getModel()
      const position = nodeModel.node_position
      let neighborNodes = []
      if (position === 2 || (position === 1 && collapseField === 'collapsed')) {
        // 只获取当前节点指向的目标节点
        neighborNodes = grahpInstance.getNeighbors(id, 'target')
      } else {
        // 只获取当前节点的源节点
        neighborNodes = grahpInstance.getNeighbors(id, 'source')
      }
      this.connectedNodeIds = []
      this.deepNodes(neighborNodes, nodeModel, nodeModel[collapseField])
      nodeModel[collapseField] = !nodeModel[collapseField]
      grahpInstance.setItemState(item, collapseField, nodeModel[collapseField] ? 1 : 0)
      if (position !== 1 || (position === 1 && !nodeModel[collapseField])) {
        grahpInstance.layout()
        grahpInstance.focusItem(item, true, {
          easing: 'easeLinear',
          duration: 500
        })
      }
    },
    addNodes ({ resultData, parentNode, collapseField }) {
      if (resultData.nodes.length > 0) {
        // 添加节点
        resultData.nodes.forEach(node => {
          console.log('node', node)
          node.x = parentNode.x
          node.y = parentNode.y
          if (node.can_extract) {
            node.collapsed = true
          }
          grahpInstance.addItem('node', node)
        })
        // 添加边
        resultData.edges.forEach(edge => {
          grahpInstance.addItem('edge', edge)
        })
        this.reLayout(parentNode.id, collapseField)
        // isAdd = true;
      }
    },
    getData (data, collapseField) {
      if (data.is_group_node) {
        const item = grahpInstance.findById(data.id)
        let source = null
        let edge = null
        // 获取聚合节点源节点
        if (data.node_position === 2) {
          source = grahpInstance.getNeighbors(data.id, 'source')[0].getModel()
          edge = item.getInEdges()[0].getModel()
        } else {
          source = grahpInstance.getNeighbors(data.id, 'target')[0].getModel()
          edge = item.getOutEdges()[0].getModel()
        }
        data.group_lineage_type = edge.lineage_type
        data.group_tb_id = data.tb_id
        data.tb_id = source.tb_id
        data.tb_type = source.tb_type
      }
      this.$emit('getData', {
        nodeModel: data,
        collapseField: collapseField
      })
    },
    // 节点展开收起
    deepNodes (nodes, nodeModel, isOpen) {
      nodes.forEach(node => {
        const model = node.getModel()
        const id = model.id
        if (isOpen) {
          grahpInstance.showItem(node)
          // node.show();
        } else {
          // node.hide();
          grahpInstance.hideItem(node)
        }
        this.connectedNodeIds.push(id)
        let neighborNodes = []
        if (!model.collapsed) {
          if (model.node_position === 2) {
            neighborNodes = grahpInstance.getNeighbors(id, 'target')
          } else {
            neighborNodes = grahpInstance.getNeighbors(id, 'source')
          }
          if (neighborNodes.length > 0) {
            this.deepNodes(neighborNodes, model, isOpen)
          }
        }
      })
    },
    scaleGraph (type) {
      let value = +this.scaleValue.replace('%', '')
      let scaleVal = value / 100
      if (type === '+') {
        if (scaleVal >= defaultConfig.maxZoom) return
        value = value + 10
        this.scaleValue = `${value}%`
      } else {
        if (scaleVal <= defaultConfig.minZoom) return
        value = value - 10
        this.scaleValue = `${value}%`
      }
      scaleVal = value / 100
      grahpInstance.zoomTo(scaleVal)
    }
  }
}
</script>
<style lang="scss" scoped>
#graph-canvas {
  @apply bg-mono-200;
  width: 100%;
  height: 100%;
}
::v-deep .blood-tooltip,
  ::v-deep .table-blood-tooltip {
    border-radius: 8px;
    @apply bg-mono-100;
    /* box-shadow: $container-c300; */
    position: relative;
    .tooltip-arrow {
      position: absolute;
      left: 16px;
      top: -6px;
    }
    .group-node-hover {
      padding: 8px 16px;
      width: 200px;
      display: inline-block;
    }
    ul {
      width: 340px;
      padding: 12px 16px 8px 16px;
      &.table-RAW {
        li .title {
          width: 60px;
          min-width: 60px;
        }
      }
      &.field-RAW {
        li .title {
          width: 60px;
          min-width: 60px;
        }
      }
      li {
        margin-bottom: 8px;
        line-height: 20px;
        display: flex;
        &.align-center {
          align-items: center;
        }
        .title {
          @apply text-type-600;
          font-weight: 600;
          font-size: 12px;
          line-height: 20px;
          width: 70px;
          margin-right: 8px;
          display: inline-block;
          white-space: nowrap;
          min-width: 70px;
        }
        .content {
          @apply text-type-800;
          word-break: break-all;
        }
        .tag {
          @apply border-primary-900 border-1 border-solid text-primary-900;
          font-size: 10px;
          line-height: 14px;
          display: inline-block;
          padding: 4px 6px;
          border-radius: 6px;
          white-space: nowrap;
          &.type {
            border-radius: 16px;
            line-height: 20px;
            padding: 1px 7px;
            margin-left: 4px;
          }
        }
      }
    }
  }
  .scale-box {
    width: 120px;
    border: 1px rgba(15, 34, 67, 0.11) solid;
    border-radius: 4px;
    display: flex;
    margin-right: 16px;
    .hz-icon {
      width: 32px;
      margin-top: 8px;
    }
    .plus, .reduce {
      height: 32px;
      border: 0px rgba(15, 34, 67, 0.11) solid;
      cursor: pointer;
    }
    .plus {
      border-right-width: 1px;
    }
    .reduce {
      border-left-width: 1px;
    }
    input {
      width: 56px;
      text-align: center;
    }
  }
</style>
