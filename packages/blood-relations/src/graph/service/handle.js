import G6 from '@antv/g6'
import { nodeTypeMap, colors, edgeMap, fieldTypeMap } from './config.js'
// 注册表节点
export function registerTableNode (statisticData) {
  G6.registerNode(
    'table-node',
    {
      draw: (cfg, group) => {
        // eslint-disable-next-line camelcase
        const { id, tb_name, node_position, can_extract, collapsed, upperCollapsed, tb_storage_flag } = cfg
        // eslint-disable-next-line camelcase
        const select = node_position === 1 // 上游(0)|自身(1)|下游(2)
        // eslint-disable-next-line camelcase
        const background = select ? nodeTypeMap[tb_storage_flag].cbg : '#fff'
        const size = select ? 14 : 12
        const color = select ? '#fff' : 'rgba(21, 22, 24, 0.72)'
        // 添加矩形
        const num = tb_name.length > 10 ? 10 : tb_name.length
        let width = select ? num * 14 : num * 12
        width = select ? 154 : 96
        // eslint-disable-next-line camelcase
        const rectConfig = {
          width: width + 48,
          height: select ? 48 : 28,
          lineWidth: 1,
          fill: background,
          radius: 6,
          stroke: select ? nodeTypeMap[tb_storage_flag].borderShadow : colors.line,
          cursor: 'pointer'
        }
        const nodeOrigin = {
          x: -rectConfig.width / 2,
          y: -rectConfig.height / 2
        }
        // 添加描边
        group.addShape('rect', {
          attrs: {
            x: nodeOrigin.x - 2,
            y: nodeOrigin.y - 2,
            width: width + 48 + 4,
            height: (select ? 48 : 28) + 4,
            lineWidth: 4,
            radius: 6
          },
          name: 'table-rect-stroke'
        })
        // 添加矩形
        const rect = group.addShape('rect', {
          attrs: {
            x: nodeOrigin.x,
            y: nodeOrigin.y,
            ...rectConfig
          },
          name: 'table-rect'
        })
        // 添加文本
        group.addShape('text', {
          attrs: {
            fontSize: size,
            x: select ? 46 + nodeOrigin.x : 30 + nodeOrigin.x,
            y: select ? 31 + nodeOrigin.y : 20 + nodeOrigin.y,
            // eslint-disable-next-line camelcase
            text: tb_name.length > 8 ? tb_name.substr(0, 8) + '...' : tb_name,
            fill: color,
            fontWeight: select ? 600 : 400,
            lineHeight: 20,
            cursor: 'pointer'
          },
          name: 'table-name'
        })
        // 添加图片背景区域
        if (select) {
          group.addShape('circle', {
            attrs: {
              x: nodeOrigin.x + 26,
              y: nodeOrigin.y + 24,
              width: 32,
              height: 32,
              r: 16,
              fill: 'rgba(255, 255, 255, 0.32)'
            },
            name: 'icon-circle'
          })
        }
        // 添加图片
        group.addShape('image', {
          attrs: {
            x: select ? 18 + nodeOrigin.x : 10 + nodeOrigin.x,
            y: select ? 16 + nodeOrigin.y : 6 + nodeOrigin.y,
            width: 16,
            height: 16,
            // img: cfg.imgPath
            img: nodeTypeMap[tb_storage_flag].img
            // img: 'http://localhost:8082/img/loading.46217790.png'
          },
          name: 'table-image'
        })
        // 添加展开收起
        // eslint-disable-next-line camelcase
        if (can_extract) {
          const extractBtnConfig = {
            y: -6,
            width: 12,
            height: 12,
            stroke: 'rgba(86, 98, 118, 0.64)',
            cursor: 'pointer',
            fill: '#fff',
            radius: 2
          }
          const extractTextConfig = {
            y: -1,
            textAlign: 'center',
            textBaseline: 'middle',
            text: collapsed ? '+' : '-',
            fontSize: 12,
            cursor: 'pointer',
            fill: 'rgba(86, 98, 118, 0.64)'
          }
          // 上游
          // eslint-disable-next-line camelcase
          if (node_position === 0) {
            group.addShape('rect', {
              attrs: {
                x: -rectConfig.width / 2 - 6,
                ...extractBtnConfig
              },
              name: 'collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: -rectConfig.width / 2,
                ...extractTextConfig
              },
              name: 'collapse-text',
              modelId: id
            })
          }
          // 下游
          // eslint-disable-next-line camelcase
          if ((node_position === 1 && statisticData.direct_downstream_cnt.count > 0) || node_position === 2) {
            group.addShape('rect', {
              attrs: {
                x: rectConfig.width / 2 - 6,
                ...extractBtnConfig
              },
              name: 'collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: rectConfig.width / 2,
                ...extractTextConfig
              },
              name: 'collapse-text',
              modelId: id
            })
          }
          // 当前表的向上展开和向下展开 需要判断一下当前节点的上游节点是否存在
          // eslint-disable-next-line camelcase
          if (node_position === 1 && statisticData.direct_upstream_cnt.count > 0) {
            // 向上
            group.addShape('rect', {
              attrs: {
                x: -rectConfig.width / 2 - 6,
                ...extractBtnConfig
              },
              name: 'up-collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: -rectConfig.width / 2,
                ...extractTextConfig,
                text: upperCollapsed ? '+' : '-'
              },
              name: 'up-collapse-text',
              modelId: id
            })
          }
        }
        return rect
      },
      update (cfg, item) {
        const group = item.getContainer()
        this.updateLinkPoints(cfg, group)
      },
      setState (name, value, item) {
        if (name === 'collapsed') {
          const group = item.getContainer()
          const collapseText = group.find(e => e.get('name') === 'collapse-text')
          if (collapseText) {
            if (!value) {
              collapseText.attr({
                text: '-'
              })
            } else {
              collapseText.attr({
                text: '+'
              })
            }
          }
        }
        if (name === 'upperCollapsed') {
          const group = item.getContainer()
          const collapseText = group.find(e => e.get('name') === 'up-collapse-text')
          if (collapseText) {
            if (!value) {
              collapseText.attr({
                text: '-'
              })
            } else {
              collapseText.attr({
                text: '+'
              })
            }
          }
        }
        if (name === 'hover') {
          const model = item.getModel()
          const select = model.node_position === 1 // 上游(0)|自身(1)|下游(2)
          const group = item.getContainer()
          const shape = group.get('children')[0]
          const borderColor = select ? nodeTypeMap[model.tb_storage_flag].borderShadow : 'rgba(15, 34, 67, 0.07)'
          if (value) {
            shape.attr('stroke', borderColor)
          } else {
            shape.attr('stroke', 'transparent')
          }
        }
      }
    },
    'rect'
  )
}

// 注册字段节点
export function registerFieldNode (statisticData) {
  G6.registerNode(
    'field-node',
    {
      draw: (cfg, group) => {
        // eslint-disable-next-line camelcase
        const { id, tb_name, field_name, field_type, node_position, can_extract, collapsed, upperCollapsed, tb_storage_flag, is_group_node, field_count } = cfg
        // eslint-disable-next-line camelcase
        const select = node_position === 1 // 上游(0)|自身(1)|下游(2)
        const background = select ? nodeTypeMap[tb_storage_flag].fieldCbg : nodeTypeMap[tb_storage_flag].fieldbg
        const size = select ? 14 : 12
        const color = select ? '#fff' : 'rgba(21, 22, 24, 0.72)'
        // 字段信息矩形
        // const fieldNum = field_name.length > 10 ? 10 : field_name.length;
        // const tableNum = tb_name.length > 10 ? 10 : tb_name.length;
        // const num = Math.max(fieldNum, tableNum);
        // let width = select ? num * 14 : num * 12;
        const width = select ? 112 : 96
        const fieldRectConfig = {
          width: width + 48,
          height: select ? 64 : 56,
          lineWidth: 1,
          fill: background,
          radius: 6,
          stroke: select ? nodeTypeMap[tb_storage_flag].color : colors.line,
          cursor: 'pointer'
        }
        const nodeOrigin = {
          x: -fieldRectConfig.width / 2,
          y: -fieldRectConfig.height / 2
        }
        // 添加描边
        group.addShape('rect', {
          attrs: {
            x: nodeOrigin.x - 2,
            y: nodeOrigin.y - 2,
            width: width + 48 + 4,
            height: (select ? 64 : 56) + 4,
            lineWidth: 4,
            radius: 6
          },
          name: 'field-rect-stroke'
        })
        // eslint-disable-next-line camelcase
        const lineDash = is_group_node ? [2] : [0]
        const rect = group.addShape('rect', {
          attrs: {
            x: nodeOrigin.x,
            y: nodeOrigin.y,
            ...fieldRectConfig,
            lineDash
          },
          name: 'field-rect'
        })
        // 字段名
        // eslint-disable-next-line camelcase
        if (is_group_node) {
          group.addShape('text', {
            attrs: {
              fontSize: 12,
              x: 8 + nodeOrigin.x,
              y: 20 + nodeOrigin.y,
              text: '包含字段',
              fill: 'rgba(21, 22, 24, 0.72)',
              fontWeight: 600,
              lineHeight: 20,
              cursor: 'pointer'
            },
            name: 'group-node-title'
          })
          // eslint-disable-next-line camelcase
          let countRectWidth = 18
          // eslint-disable-next-line camelcase
          if (field_count > 10) {
            countRectWidth = 25
          }
          // eslint-disable-next-line camelcase
          if (field_count > 100) {
            countRectWidth = 30
          }
          // eslint-disable-next-line camelcase
          if (field_count > 1000) {
            countRectWidth = 35
          }
          group.addShape('rect', {
            attrs: {
              x: 8 + nodeOrigin.x + 4 + 48,
              y: nodeOrigin.y + 5,
              width: countRectWidth,
              height: 18,
              fill: nodeTypeMap[tb_storage_flag].color,
              radius: 8
            },
            name: 'group-count-rect'
          })
          group.addShape('text', {
            attrs: {
              fontSize: 10,
              x: 8 + nodeOrigin.x + 4 + 48 + 6,
              y: 19 + nodeOrigin.y,
              text: field_count,
              fill: '#fff',
              fontWeight: 600,
              cursor: 'pointer'
            },
            name: 'group-node-count'
          })
        } else {
          group.addShape('text', {
            attrs: {
              fontSize: size,
              x: 30 + nodeOrigin.x,
              y: select ? 25 + nodeOrigin.y : 20 + nodeOrigin.y,
              // eslint-disable-next-line camelcase
              text: field_name.length > 8 ? field_name.substr(0, 8) + '...' : field_name,
              fill: color,
              fontWeight: select ? 600 : 400,
              lineHeight: 20,
              cursor: 'pointer'
            },
            name: 'field-name'
          })
          // 字段icon
          group.addShape('image', {
            attrs: {
              x: 10 + nodeOrigin.x,
              y: select ? 10 + nodeOrigin.y : 6 + nodeOrigin.y,
              width: 16,
              height: 16,
              img: select ? fieldTypeMap[field_type].imgWhite : fieldTypeMap[field_type][tb_storage_flag]
            },
            name: 'field-image'
          })
        }
        // 表信息矩形
        group.addShape('rect', {
          attrs: {
            x: nodeOrigin.x,
            y: select ? nodeOrigin.y + 36 : nodeOrigin.y + 28,
            width: width + 48,
            height: 28,
            fill: '#fff',
            radius: [0, 0, 6, 6]
          },
          name: 'table-rect'
        })
        // 表名
        group.addShape('text', {
          attrs: {
            fontSize: 12,
            x: 30 + nodeOrigin.x,
            y: select ? nodeOrigin.y + 56 : nodeOrigin.y + 48,
            // eslint-disable-next-line camelcase
            text: tb_name.length > 8 ? tb_name.substr(0, 8) + '...' : tb_name,
            fill: 'rgba(21, 22, 24, 0.48)',
            lineHeight: 20,
            cursor: 'pointer'
          },
          name: 'table-name'
        })
        // 表icon
        group.addShape('image', {
          attrs: {
            x: 10 + nodeOrigin.x,
            y: select ? 42 + nodeOrigin.y : 34 + nodeOrigin.y,
            width: 16,
            height: 16,
            img: nodeTypeMap[tb_storage_flag].img
          },
          name: 'table-image'
        })
        // 添加展开收起
        // eslint-disable-next-line camelcase
        if (can_extract && !is_group_node) {
          const extractBtnConfig = {
            y: -6,
            width: 12,
            height: 12,
            stroke: 'rgba(86, 98, 118, 0.64)',
            cursor: 'pointer',
            fill: '#fff',
            radius: 2
          }
          const extractTextConfig = {
            y: -1,
            textAlign: 'center',
            textBaseline: 'middle',
            text: collapsed ? '+' : '-',
            fontSize: 12,
            cursor: 'pointer',
            fill: 'rgba(86, 98, 118, 0.64)'
          }
          // 上游节点
          // eslint-disable-next-line camelcase
          if (node_position === 0) {
            group.addShape('rect', {
              attrs: {
                x: -fieldRectConfig.width / 2 - 6,
                ...extractBtnConfig
              },
              name: 'collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: -fieldRectConfig.width / 2,
                ...extractTextConfig
              },
              name: 'collapse-text',
              modelId: id
            })
          }
          // 下游节点或者当前节点
          // eslint-disable-next-line camelcase
          if ((node_position === 1 && statisticData.direct_downstream_cnt.count > 0) || node_position === 2) {
            group.addShape('rect', {
              attrs: {
                x: fieldRectConfig.width / 2 - 6,
                ...extractBtnConfig
              },
              name: 'collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: fieldRectConfig.width / 2,
                ...extractTextConfig
              },
              name: 'collapse-text',
              modelId: id
            })
          }
          // 当前表的向上展开和向下展开
          // eslint-disable-next-line camelcase
          if (node_position === 1 && statisticData.direct_upstream_cnt.count > 0) {
            // 向上
            group.addShape('rect', {
              attrs: {
                x: -fieldRectConfig.width / 2 - 6,
                ...extractBtnConfig
              },
              name: 'up-collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: -fieldRectConfig.width / 2,
                ...extractTextConfig,
                text: upperCollapsed ? '+' : '-'
              },
              name: 'up-collapse-text',
              modelId: id
            })
          }
        }
        return rect
      },
      update (cfg, item) {
        const group = item.getContainer()
        this.updateLinkPoints(cfg, group)
      },
      setState (name, value, item) {
        if (name === 'collapsed') {
          const group = item.getContainer()
          const collapseText = group.find(e => e.get('name') === 'collapse-text')
          if (collapseText) {
            if (!value) {
              collapseText.attr({
                text: '-'
              })
            } else {
              collapseText.attr({
                text: '+'
              })
            }
          }
        }
        if (name === 'upperCollapsed') {
          const group = item.getContainer()
          const collapseText = group.find(e => e.get('name') === 'up-collapse-text')
          if (collapseText) {
            if (!value) {
              collapseText.attr({
                text: '-'
              })
            } else {
              collapseText.attr({
                text: '+'
              })
            }
          }
        }
        if (name === 'hover') {
          const model = item.getModel()
          const select = model.node_position === 1 // 上游(0)|自身(1)|下游(2)
          const group = item.getContainer()
          const shape = group.get('children')[0]
          const borderColor = select ? nodeTypeMap[model.tb_storage_flag].borderShadow : 'rgba(15, 34, 67, 0.07)'
          if (value) {
            shape.attr('stroke', borderColor)
          } else {
            shape.attr('stroke', 'transparent')
          }
        }
      }
    },
    'rect'
  )
}

function setNodeInfo (cfg) {
  const nodeInfo = { ...cfg }
  if (cfg.tb_id) {
    nodeInfo.nodeType = 'tb'
    nodeInfo.nodeTitle = cfg.tb_name
  } else if (cfg.idx_id) {
    nodeInfo.nodeType = cfg.node_type
    nodeInfo.nodeTitle = cfg.idx_name
  }
  // eslint-disable-next-line camelcase
  const select = nodeInfo.node_position === 1 // 上游(0)|自身(1)|下游(2)
  if (select && cfg.node_type >= 3) {
    nodeInfo.nodeType += '-main'
  }
  return nodeInfo
}
// 注册指标结点
export function registerIndexNode () {
  G6.registerNode(
    'index-node',
    {
      draw: (cfg, group) => {
        // eslint-disable-next-line camelcase
        const { id, node_position, can_extract, collapsed, upperCollapsed } = cfg
        const nodeInfo = setNodeInfo(cfg)
        // eslint-disable-next-line camelcase
        const select = node_position === 1 // 上游(0)|自身(1)|下游(2)
        // eslint-disable-next-line camelcase
        const background = select ? nodeTypeMap[nodeInfo.nodeType].cbg : '#fff'
        const size = 14
        const color = select ? '#fff' : 'rgba(21, 22, 24, 0.72)'
        // 添加矩形
        const num = nodeInfo.nodeTitle.length > 10 ? 10 : nodeInfo.nodeTitle.length
        let width = select ? num * 14 : num * 12
        width = select ? 154 : 150
        // eslint-disable-next-line camelcase
        const rectConfig = {
          width: width + 48,
          height: select ? 54 : 54 - 4,
          lineWidth: 2,
          fill: background,
          radius: 26,
          stroke: !select ? nodeTypeMap[nodeInfo.nodeType].borderColor : 'transparent',
          cursor: 'pointer'
        }
        const nodeOrigin = {
          x: -rectConfig.width / 2,
          y: -rectConfig.height / 2
        }
        if (select) {
          // 添加描边
          group.addShape('rect', {
            attrs: {
              x: nodeOrigin.x - 2,
              y: nodeOrigin.y - 2,
              width: rectConfig.width + 4,
              height: rectConfig.height + 4,
              stroke: nodeTypeMap[nodeInfo.nodeType].strokeColor,
              lineWidth: 4,
              radius: 28
            },
            name: 'table-rect-stroke'
          })
        }
        // 添加矩形
        const rect = group.addShape('rect', {
          attrs: {
            x: nodeOrigin.x,
            y: nodeOrigin.y,
            ...rectConfig
          },
          name: 'table-rect'
        })
        if (!select) {
          // 添加描边
          group.addShape('rect', {
            attrs: {
              x: nodeOrigin.x + 3,
              y: nodeOrigin.y + 3,
              width: rectConfig.width - 6,
              height: rectConfig.height - 6,
              stroke: nodeTypeMap[nodeInfo.nodeType].strokeColor,
              lineWidth: 4,
              radius: 22
            },
            name: 'table-rect-stroke'
          })
        }
        // 添加文本
        group.addShape('text', {
          attrs: {
            fontSize: size,
            x: select ? 49 + nodeOrigin.x : 48 + nodeOrigin.x,
            y: select ? 35 + nodeOrigin.y : 33 + nodeOrigin.y,
            // eslint-disable-next-line camelcase
            text: nodeInfo.nodeTitle.length > 8 ? nodeInfo.nodeTitle.substr(0, 8) + '...' : nodeInfo.nodeTitle,
            fill: color,
            fontWeight: select ? 600 : 400,
            lineHeight: 20,
            cursor: 'pointer'
          },
          name: 'table-name'
        })
        // 添加图片背景区域
        if (select || cfg.node_type >= 3) {
          group.addShape('circle', {
            attrs: {
              x: nodeOrigin.x + 30,
              y: nodeOrigin.y + 27,
              r: 13,
              fill: nodeTypeMap[nodeInfo.nodeType].circleBgColor,
              stroke: nodeTypeMap[nodeInfo.nodeType].circleBorderColor,
              lineWidth: 1
            },
            name: 'icon-circle'
          })
        }
        // 添加图片
        group.addShape('image', {
          attrs: {
            x: 22 + nodeOrigin.x,
            y: 19 + nodeOrigin.y,
            width: 16,
            height: 16,
            // img: cfg.imgPath
            img: nodeTypeMap[nodeInfo.nodeType].img
            // img: 'http://localhost:8082/img/loading.46217790.png'
          },
          name: 'table-image'
        })
        // 添加展开收起
        // eslint-disable-next-line camelcase
        if (can_extract) {
          const extractBtnConfig = {
            y: 0,
            r: 10,
            cursor: 'pointer',
            fill: 'l(90) 0:#6FA4FF 1:#1F71FF'
          }
          const extractTextConfig = {
            y: -1,
            textAlign: 'center',
            textBaseline: 'middle',
            text: collapsed ? '+' : '-',
            fontSize: 14,
            cursor: 'pointer',
            fill: '#fff'
          }
          // 上游
          // eslint-disable-next-line camelcase
          if (node_position === 0) {
            group.addShape('circle', {
              attrs: {
                x: -rectConfig.width / 2,
                ...extractBtnConfig
              },
              name: 'collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: -rectConfig.width / 2,
                ...extractTextConfig
              },
              name: 'collapse-text',
              modelId: id
            })
          }
          // 下游
          // eslint-disable-next-line camelcase
          if ((node_position === 1 && nodeInfo.extract_down) || node_position === 2) {
            group.addShape('circle', {
              attrs: {
                x: rectConfig.width / 2,
                ...extractBtnConfig
              },
              name: 'collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: rectConfig.width / 2,
                ...extractTextConfig
              },
              name: 'collapse-text',
              modelId: id
            })
          }
          // 当前表的向上展开和向下展开 需要判断一下当前节点的上游节点是否存在
          // eslint-disable-next-line camelcase
          if (node_position === 1 && nodeInfo.extract_up) {
            // 向上
            group.addShape('rect', {
              attrs: {
                x: -rectConfig.width / 2 - 6,
                ...extractBtnConfig
              },
              name: 'up-collapse-back',
              modelId: id
            })
            group.addShape('text', {
              attrs: {
                x: -rectConfig.width / 2,
                ...extractTextConfig,
                text: upperCollapsed ? '+' : '-'
              },
              name: 'up-collapse-text',
              modelId: id
            })
          }
        }
        return rect
      },
      update (cfg, item) {
        const group = item.getContainer()
        this.updateLinkPoints(cfg, group)
      },
      setState (name, value, item) {
        if (name === 'collapsed') {
          const group = item.getContainer()
          const collapseText = group.find(e => e.get('name') === 'collapse-text')
          if (collapseText) {
            if (!value) {
              collapseText.attr({
                text: '-'
              })
            } else {
              collapseText.attr({
                text: '+'
              })
            }
          }
        }
        if (name === 'upperCollapsed') {
          const group = item.getContainer()
          const collapseText = group.find(e => e.get('name') === 'up-collapse-text')
          if (collapseText) {
            if (!value) {
              collapseText.attr({
                text: '-'
              })
            } else {
              collapseText.attr({
                text: '+'
              })
            }
          }
        }
        if (name === 'hover') {
          const model = item.getModel()
          const select = model.node_position === 1
          const nodeInfo = setNodeInfo(model)
          const group = item.getContainer()
          const shape = select ? group.get('children')[0] : group.get('children')[1]
          const borderColor = nodeTypeMap[nodeInfo.nodeType].strokeColor
          const borderHoverColor = nodeTypeMap[nodeInfo.nodeType].strokeHoverColor
          if (value) {
            shape.attr('stroke', borderHoverColor)
            if (select) {
              shape.attr('shadowColor', 'rgba(0,0,0,0.2)')
              shape.attr('shadowBlur', 10)
            }
          } else {
            shape.attr('stroke', borderColor)
            if (select) {
              shape.attr('shadowColor', 'transparent')
            }
          }
        }
      }
    },
    'rect'
  )
}

// 注册边
export function registerEdge (nodeType) {
  G6.registerEdge('kinship-edge', {
    itemType: 'edge',
    draw: function draw (cfg, group) {
      // eslint-disable-next-line no-unused-vars, camelcase
      const { startPoint, endPoint, label, lineage_type } = cfg
      let sourceNode = cfg.sourceNode.getModel()
      const targetNode = cfg.targetNode.getModel()
      const targetNodeInfo = setNodeInfo(targetNode.node_position === 1 ? sourceNode : targetNode)
      const nodePosition = sourceNode.node_position
      if (nodePosition === 0) {
        sourceNode = cfg.targetNode.getModel()
      }
      const hgap = Math.abs(endPoint.x - startPoint.x)
      let pathcdis = 58
      if (sourceNode.is_group_node) {
        pathcdis = 0
      }
      let path = [
        ['M', startPoint.x, startPoint.y],
        ['C', startPoint.x + hgap / 4, startPoint.y, endPoint.x - hgap, endPoint.y, endPoint.x - pathcdis, endPoint.y]
      ]
      let relationRectPos = {
        x: startPoint.x + Math.abs(startPoint.x - endPoint.x) / 2 - 18,
        y: endPoint.y - 9
      }
      let lineRectPos = {
        x: startPoint.x + Math.abs(startPoint.x - endPoint.x) / 2 - 18 + 36,
        y: endPoint.y - 1
      }
      let textPos = {
        x: startPoint.x + Math.abs(startPoint.x - endPoint.x) / 2 - 18 + 8,
        y: endPoint.y + 6
      }
      if (nodePosition === 0) {
        path = [
          ['M', startPoint.x + pathcdis, startPoint.y],
          ['C', startPoint.x + pathcdis + hgap / 4, startPoint.y, startPoint.x + pathcdis + hgap / 2, endPoint.y, endPoint.x, endPoint.y]
        ]
        relationRectPos = {
          x: startPoint.x + 22,
          y: startPoint.y - 9
        }
        lineRectPos = {
          x: startPoint.x,
          y: startPoint.y - 1
        }
        textPos = {
          x: startPoint.x + 30,
          y: startPoint.y + 6
        }
      }
      const line = group.addShape('path', {
        attrs: {
          stroke: colors.line,
          path,
          lineWidth: 2,
          endArrow: false
        },
        name: 'edge-path'
      })
      if (!sourceNode.is_group_node) {
        // 添加关系
        group.addShape('rect', {
          attrs: {
            width: 36,
            height: 20,
            lineWidth: 1,
            fill: '#fff',
            radius: 10,
            stroke: edgeMap[lineage_type] ? edgeMap[lineage_type].color : '',
            ...relationRectPos
          }
        })
        // 添加关系右边的小线段
        group.addShape('rect', {
          attrs: {
            width: 22,
            height: 2,
            fill: colors.line,
            ...lineRectPos
          }
        })
        group.addShape('text', {
          attrs: {
            ...textPos,
            text: edgeMap[lineage_type] ? edgeMap[lineage_type].text : '',
            fontSize: 10,
            fill: edgeMap[lineage_type] ? edgeMap[lineage_type].color : ''
          },
          name: 'text-shape-edge'
        })
      }
      if (nodeType === 'index') {
        group.addShape('circle', {
          attrs: {
            x: targetNode.node_position === 1 ? startPoint.x + 4 : endPoint.x - 4,
            y: targetNode.node_position === 1 ? startPoint.y : endPoint.y,
            r: 4,
            width: 6,
            height: 6,
            lineWidth: 2,
            fill: '#fff',
            stroke: nodeTypeMap[targetNodeInfo.nodeType].circleBorderColor
          }
        })
      }
      return line
    }
  })
}

// 初始化事件
export function initEvent (grahpInstance, handleCollapse) {
  grahpInstance.on('collapse-text:click', e => {
    handleCollapse(e, 'collapsed')
    e.stopPropagation()
  })
  grahpInstance.on('collapse-back:click', e => {
    handleCollapse(e, 'collapsed')
    e.stopPropagation()
  })
  grahpInstance.on('up-collapse-text:click', e => {
    handleCollapse(e, 'upperCollapsed')
    e.stopPropagation()
  })
  grahpInstance.on('up-collapse-back:click', e => {
    handleCollapse(e, 'upperCollapsed')
    e.stopPropagation()
  })
  // 点击节点
  grahpInstance.on('node:click', e => {
    const node = e.item.getModel()
    if (node.node_position === 1) return
    if (node.is_group_node) {
      handleCollapse(e, 'collapsed')
      return false
    }
    // TODO 之后需要处理成emit出去当前信息。在各产品里进行处理click逻辑
    // let url = `#/data-warehouse/tb-details/${node.tb_id}/blood-info`
    // if (node.type === 'table-node') {
    //   url += '/table'
    // } else {
    //   url += `/field?field_id=${node.field_id}`
    // }
    // window.open(url, '_blank')
    handleCollapse(node, 'open')
  })
  // 监听鼠标进入节点事件
  grahpInstance.on('node:mouseenter', evt => {
    const node = evt.item
    // 激活该节点的 hover 状态
    grahpInstance.setItemState(node, 'hover', true)
  })
  // 监听鼠标离开节点事件
  grahpInstance.on('node:mouseleave', evt => {
    const node = evt.item
    // 关闭该节点的 hover 状态
    grahpInstance.setItemState(node, 'hover', false)
  })
}
