import G6 from '@antv/g6'
// import { IG6GraphEvent, IAbstractGraph as IGraph } from '@antv/g6-core'
import { modifyCSS } from '@antv/dom-util'
import { nodeTypeMap, statusMap, defaultTooltipConfig } from './config.js'

class BloodTooltip extends G6.Tooltip {
  updatePosition (e) {
    const shouldBegin = this.get('shouldBegin')
    const tooltip = this.get('tooltip')
    if (!shouldBegin(e)) {
      modifyCSS(tooltip, {
        visibility: 'hidden'
      })
      return
    }
    const graph = this.get('graph')
    const width = graph.get('width')
    const height = graph.get('height')

    const offsetX = this.get('offsetX') || 0
    const offsetY = this.get('offsetY') || 0

    // const mousePos = graph.getPointByClient(e.clientX, e.clientY);
    const point = graph.getPointByClient(e.clientX, e.clientY)
    let { x, y } = graph.getCanvasByPoint(point.x, point.y)

    // let x = mousePos.x + offsetX;
    // let y = mousePos.y + offsetY;
    // let x = e.x + offsetX;
    // let y = e.y + offsetY;
    x += offsetX
    y += offsetY

    const bbox = tooltip.getBoundingClientRect()
    if (x + bbox.width > width) {
      x = x - bbox.width - offsetX
    }

    if (y + bbox.height > height) {
      y = y - bbox.height - offsetY - 24
    }

    modifyCSS(tooltip, {
      left: `${x}px`,
      top: `${y}px`,
      visibility: 'visible',
      'z-index': '30'
    })
  }
}

// 数据量转换x亿x万x条
function parseNum (count) {
  if (!count) {
    return 0
  }
  const numArr = []
  let i = 0
  while (count > 0) {
    let unit = ''
    switch (i) {
      case 0:
        unit = '条'
        break
      case 1:
        unit = '万'
        break
      case 2:
        unit = '亿'
        break
    }
    numArr.unshift((count % 10000) + unit)
    count = Math.floor(count / 10000)
    i++
  }
  return numArr.join('')
}

function setTableTooltip (node) {
  let createTimeText = '创建时间'
  if (node.tb_type === 'RAW') {
    createTimeText = '接入时间'
  }
  let tpl = `
  <p class="blood-tooltip-title">${node.tb_name} 
    <span class='tag type' style="border-width:1px; margin-left: 8px; border-style: solid; border-color: ${
      nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].color : ''
    };
    color: ${nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].color : ''};">
      ${nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].text : ''}
    </span>
  </p>
  <ul class="blood-tooltip-body table-${node.tb_type}">`
  if (node.tb_type === 'RAW') {
    tpl += `
      <li>
        <span class='title'>原始名称</span>
        <span class='content'>${node.ori_tb_name || '-'}</span>
      </li>
    `
  }
  tpl += `
    <li>
      <span class='title'>${createTimeText}</span>
      <span class='content'>${node.create_time}</span>
    </li>`
    if (defaultTooltipConfig.dataset_name.show) {
      tpl += `
      <li class='align-center'>
        <span class='title'>所属数据集</span>
        <span class='content'>${node.dataset_name}</span>
      </li>`
    }
    tpl += `
    <li>
      <span class="title">存储类型</span>
      <span class='content'>${node.is_partition_tb === 1 ? '分区' : '非分区'}</span>
    </li>
    `
  if (node.tb_type === 'RAW') {
    tpl += `
        <li>
          <span class='title'>接入方式</span>
          <span class='content'>${node.access_mode || '-'}</span>
        </li>
      `
  }
  tpl += `<li>
      <span class='title'>表ID</span>
      <span class='content'>${node.tb_id}</span>
    </li>
    <li>
      <span class='title'>数据量</span>
      <span class='content'>${parseNum(node.count)}</span>
    </li>
    <li class='align-center'>
      <span class='title'>更新状态</span>
      <span class='content tag'
        style="color: ${statusMap[node.update_status] ? statusMap[node.update_status].color : ''};
        border-color: ${statusMap[node.update_status] ? statusMap[node.update_status].color : ''};">
        ${statusMap[node.update_status] ? statusMap[node.update_status].text : ''}
      </span>
    </li>
    <li>
      <span class='title'>更新时间</span>
      <span class='content'>${node.update_time}</span>
    </li>
  </ul>`
  return tpl
}
function setFieldTooltip (node) {
  const storageType = node.is_partition_tb
  let alignCenter = 'center'
  if (node.tb_name.length > 9) {
    alignCenter = 'flex-start'
  }
  let createTimeText = '创建时间'
  if (node.tb_type === 'RAW') {
    createTimeText = '接入时间'
  }
  let tpl = ''
  if (node.is_group_node) {
    tpl = '<span class="group-node-hover">涉及该表多个字段，此处作聚合展示，点击可查看字段明细</span>'
    return tpl
  }
  tpl = `<ul class="blood-tooltip-body field-${node.tb_type}">
    <li>
      <span class='title'>字段名称</span>
      <span class='content'>${node.field_name}</span>
    </li>`
  if (node.tb_type === 'RAW') {
    tpl += `
      <li>
        <span class='title'>原始名称</span>
        <span class='content'>${node.ori_field_name || '-'}</span>
      </li>
    `
  }
  tpl += `<li>
    <span class='title'>${createTimeText}</span>
    <span class='content'>${node.create_time}</span>
  </li>
  <li style="align-items: ${alignCenter}">
    <span class='title'>所属表</span>
    <span class='content'>
      ${node.tb_name}
      <span class='tag type' style="border: 0; color: ${
        nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].color : ''
      };
      background-color: ${nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].bgColor : ''};">
        ${nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].text : ''}
      </span>
      <span class='tag type' style="border: 0; color: ${
        nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].color : ''
      }; display:${storageType === 3 ? 'unset' : 'none'};
      background-color: ${nodeTypeMap[node.tb_storage_flag] ? nodeTypeMap[node.tb_storage_flag].bgColor : ''};">
        分区表
      </span>
    </span>
  </li>
  <li class='align-center'>
    <span class='title'>所属数据集</span>
    <span class='content'>${node.dataset_name}</span>
  </li>
  <li>
      <span class="title">存储类型</span>
      <span class='content'>${node.is_partition_tb === 1 ? '分区' : '非分区'}</span>
  </li>`
  if (node.tb_type === 'RAW') {
    tpl += `
      <li>
        <span class='title'>接入方式</span>
        <span class='content'>${node.access_mode || '-'}</span>
      </li>`
  }
  tpl += `
    <li class='align-center'>
      <span class='title'>更新状态</span>
      <span class='content tag'
        style="color: ${statusMap[node.update_status] ? statusMap[node.update_status].color : ''};
        border-color: ${statusMap[node.update_status] ? statusMap[node.update_status].color : ''};">
        ${statusMap[node.update_status] ? statusMap[node.update_status].text : ''}
      </span>
    </li>
    <li>
      <span class='title'>更新时间</span>
      <span class='content'>${node.update_time}</span>
    </li>
  </ul>`
  return tpl
}
const tooltipBaseInfo = [
  {
    name: '创建时间',
    key: 'create_time'
  },
  {
    name: '指标口径',
    key: 'jargon'
  },
  {
    name: '指标单位',
    key: 'unit_name'
  },
  {
    name: '指标描述',
    key: 'description'
  },
  {
    name: '计算逻辑',
    key: 'expression'
  },
  {
    name: '更新时间',
    key: 'update_time'
  }
]
function setIndexTooltip (node) {
  const tooltip = tooltipBaseInfo.map((item) => item)
  // 派生指标
  if (node.node_type === 4) {
    const driveInfo = [
      {
        name: '维度',
        key: 'dims'
      },
      {
        name: '时间周期',
        key: 'time_period'
      }
    ]
    tooltip.splice(5, 0, driveInfo[0])
    tooltip.splice(6, 0, driveInfo[1])
  }
  // 复合指标
  if (node.node_type === 5) {
    const compositeInfo = [
      {
        name: '对齐维度',
        key: 'paddings'
      }
    ]
    tooltip.splice(5, 0, compositeInfo[0])
  }
  let tpl = `
    <div class="blood-tooltip-title index-${node.node_type}" style="max-width: 480px">
      <p class="title">${node.idx_name}</p>
      <span class="tag" style="color: ${nodeTypeMap[node.node_type].color};border: 1px solid ${nodeTypeMap[node.node_type].color}">
      ${nodeTypeMap[node.node_type].text}
      </span>
    </div>
    <ul class="blood-tooltip-body index-${node.node_type}" style="max-width: 480px;width: auto">`
  tooltip.forEach((item) => {
    tpl += `
      <li>
        <span class='title'>${item.name}</span>
        <span class='content'>
          ${node[item.key] || '--'}
        </span>
      </li>
    `
  })
  tpl += '</ul>'
  return tpl
}

// 获取tooltip内容
function getTooltipContent (node, type = 'table') {
  if (type === 'table') {
    return setTableTooltip(node)
  } else if (type === 'index') {
    return setIndexTooltip(node)
  } else {
    return setFieldTooltip(node)
  }
}

// 注册tooltip
export function registerTooltip (type) {
  const cssStyles = `
    .blood-tooltip-title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      margin-left: 18px;
      margin-top: 8px;
      padding-left: 12px;
      position: relative;
      text-align:left;
      display: flex;
    }
    .blood-tooltip-title .title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1 1 0%;
      margin-right: 8px;
    }
    .blood-tooltip-title::before {
      content: '';
      position: absolute;
      width: 4px;
      height: 8px;
      left: 0;
      top: 8px;
      background-color: #1F71FF;
      border-radius: 4px;
    }
    .blood-tooltip-title .tag{
      border-radius: 4px;
      font-size: 12px;
      line-height: 20px;
      font-weight: normal;
      padding: 2px 6px;
      margin-right: 8px;
    }
    .blood-tooltip-body {
      font-size: 14px !important;
    }
    .blood-tooltip-body .title {
      font-size: 14px !important;
      font-weight: 600;
      width: 70px;
    }
  `
  const bloldTooltipElemId = 'blood-tooltip'
  const bloldTooltipElem = document.getElementById(bloldTooltipElemId)
  bloldTooltipElem && bloldTooltipElem.remove()
  const styleElem = document.createElement('style')
  styleElem.id = bloldTooltipElemId
  styleElem.setAttribute('type', 'text/css')
  styleElem.innerHTML = cssStyles
  document.head.append(styleElem)
  const tooltip = new BloodTooltip({
    className: type === 'field' ? 'blood-tooltip' : 'table-blood-tooltip',
    offsetX: -36,
    offsetY: 24,
    // 允许出现 tooltip 的 item 类型
    itemTypes: ['node'],
    getContent: e => {
      const node = e.item.getModel()
      let nodeType = node.node_type >= 3 ? 'index' : 'table'
      if (node.field_id) {
        nodeType = 'field'
      }
      return getTooltipContent(node, nodeType)
    },
    shouldBegin: e => {
      let eleArr = ['table-rect', 'table-name', 'table-image']
      if (type === 'field') {
        eleArr = [
          'table-rect',
          'table-name',
          'table-image',
          'field-rect',
          'field-name',
          'field-image',
          'group-node-title',
          'group-count-rect',
          'group-node-count'
        ]
      }
      if (eleArr.indexOf(e.target.get('name')) > -1) {
        return true
      }
      return false
    }
  })
  return tooltip
}
