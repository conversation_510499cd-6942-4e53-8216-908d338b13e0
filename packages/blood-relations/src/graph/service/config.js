import tNormal from '../../assets/images/blood/t-normal.svg'
import tFlow from '../../assets/images/blood/t-flow.svg'
import tOnline from '../../assets/images/blood/t-online.svg'
import tNumber from '../../assets/images/blood/type-number.svg'
import stNumber from '../../assets/images/blood/select/type-number.svg'
import fieldtype0tNumber from '../../assets/images/blood/field-type/0/type-number.svg'
import fieldtype1tNumber from '../../assets/images/blood/field-type/1/type-number.svg'
import fieldtype2tNumber from '../../assets/images/blood/field-type/2/type-number.svg'

import tString from '../../assets/images/blood/type-string.svg'
import stString from '../../assets/images/blood/select/type-string.svg'
import fieldtype0tString from '../../assets/images/blood/field-type/0/type-string.svg'
import fieldtype1tString from '../../assets/images/blood/field-type/1/type-string.svg'
import fieldtype2tString from '../../assets/images/blood/field-type/2/type-string.svg'

import tDate from '../../assets/images/blood/type-date.svg'
import stSdate from '../../assets/images/blood/select/type-date.svg'
import fieldtype0tDate from '../../assets/images/blood/field-type/0/type-date.svg'
import fieldtype1tDate from '../../assets/images/blood/field-type/1/type-date.svg'
import fieldtype2tDate from '../../assets/images/blood/field-type/2/type-date.svg'

import tBlob from '../../assets/images/blood/type-blob.svg'
import stSBlob from '../../assets/images/blood/select/type-blob.svg'
import fieldtype0tBlob from '../../assets/images/blood/field-type/0/type-blob.svg'
import fieldtype1tBlob from '../../assets/images/blood/field-type/1/type-blob.svg'
import fieldtype2tBlob from '../../assets/images/blood/field-type/2/type-blob.svg'
import indexAtom from '../../assets/images/index/atom-index.svg'
import indexDerive from '../../assets/images/index/derive-index.svg'
import indexComposite from '../../assets/images/index/composite-index.svg'
import indexWhite from '../../assets/images/index/white-index.svg'

// const tNormal = require(`${imgUrl}t-normal.svg`).default
export const defaultConfig = {
  // width: $width, // Number，必须，图的宽度
  // height: $height, // Number，必须，图的高度
  fitView: true,
  // animate: true,
  animateCfg: {
    duration: 300 // Number，一次动画的时长
  },
  fitCenter: true,
  maxZoom: 1.5,
  minZoom: 0.3,
  // renderer: 'svg', // 自定义节点图形Shape为dom时需指定图渲染方式renderer为'svg'
  modes: {
    default: ['zoom-canvas', 'drag-canvas', 'activate-relations']
  },
  resizing: {
    enabled: true
  },
  // defaultNode: {
  //   type: this.type === 'table' ? 'table-node' : 'field-node'
  // },
  defaultNode: {
    type: 'table-node'
  },
  defaultEdge: {
    type: 'kinship-edge'
  },
  layout: {
    type: 'dagre', // 层次布局
    rankdir: 'LR',
    nodesep: 16,
    ranksepFunc: d => {
      return 100
    }
  }
}
export const defaultTooltipConfig = {
  dataset_name: {
    show: true
  }
}

// 字段类型 字段类型: INT(0)|DOUBLE(1)|STRING(2)|DATETIME(3)
export const fieldTypeMap = {
  0: {
    img: tNumber,
    imgWhite: stNumber,
    0: fieldtype0tNumber,
    1: fieldtype1tNumber,
    2: fieldtype2tNumber
  },
  1: {
    img: tNumber,
    imgWhite: stNumber,
    0: fieldtype0tNumber,
    1: fieldtype1tNumber,
    2: fieldtype2tNumber
  },
  2: {
    img: tString,
    imgWhite: stString,
    0: fieldtype0tString,
    1: fieldtype1tString,
    2: fieldtype2tString
  },
  3: {
    img: tDate,
    imgWhite: stSdate,
    0: fieldtype0tDate,
    1: fieldtype1tDate,
    2: fieldtype2tDate
  },
  4: {
    img: tBlob,
    imgWhite: stSBlob,
    0: fieldtype0tBlob,
    1: fieldtype1tBlob,
    2: fieldtype2tBlob
  }
}

// 节点类型-大类
export const nodeTypeMap = {
  0: {
    color: '#0099EB',
    text: '普通表',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(0) 0:#A0DDFF 0.4:#6CCEF8 1:#0099EB',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: tNormal,
    borderShadow: 'rgba(0, 153, 235, 0.12)'
  },
  1: {
    color: '#FF9431',
    bgColor: 'rgba(255, 148, 49, 0.12)',
    cbg: 'l(0) 0:#FED134 0.4:#FFB129 1:#FF7C03',
    fieldCbg: 'l(0) 0:#FF7C03 0.4:#FFB129 1:#FED134',
    fieldbg: 'rgba(255, 148, 49, 0.12)',
    text: '在线表',
    hzName: 't-online',
    img: tOnline,
    borderShadow: 'rgba(255, 148, 49, 0.12)'
  },
  2: {
    color: '#9646FF',
    bgColor: 'rgba(150, 70, 255, 0.09)',
    cbg: 'l(0) 0:#F2A0FF 0.4:#C05FFF 1:#9646FF',
    fieldCbg: 'l(0) 0:#9646FF 0.4:#C05FFF 1:#F2A0FF',
    fieldbg: 'rgba(150, 70, 255, 0.09)',
    text: '流式表',
    hzName: 't-flow',
    img: tFlow,
    borderShadow: 'rgba(150, 70, 255, 0.12)'
  },
  tb: {
    color: '#0099EB',
    text: '普通表',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(0) 0:#A0DDFF 0.4:#6CCEF8 1:#0099EB',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: tNormal,
    borderShadow: 'rgba(0, 153, 235, 0.12)',
    borderColor: '#1F71FF',
    strokeColor: '#E0EBFF',
    strokeHoverColor: '#99BDFF',
    circleBorderColor: '#1F71FF'
  },
  3: {
    color: '#0099EB',
    text: '原子指标',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(0) 0:#A0DDFF 0.4:#6CCEF8 1:#0099EB',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: indexAtom,
    borderShadow: 'rgba(0, 153, 235, 0.12)',
    borderColor: '#1F71FF',
    strokeColor: '#E3F5FF',
    strokeHoverColor: '#99DBFF',
    circleBorderColor: '#0099EB'
  },
  '3-main': {
    color: '#0099EB',
    text: '原子指标-主节点',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(50) 0:#14C6FF 1:#0099EB',
    // cbg: 'linear-gradient(171deg, #14C6FF 0%, #0099EB 100%)',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: indexWhite,
    borderShadow: 'rgba(0, 153, 235, 0.12)',
    strokeColor: 'transparent',
    strokeHoverColor: '#fff',
    circleBorderColor: '#ffffff'
  },
  4: {
    color: '#00BBC2',
    text: '派生指标',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(0) 0:#A0DDFF 0.4:#6CCEF8 1:#0099EB',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: indexDerive,
    borderShadow: '#E2FBFC',
    borderColor: '#00BBC2',
    strokeColor: '#E2FBFC',
    strokeHoverColor: '#A3F2F5',
    circleBorderColor: '#00BBC2',
    circleBgColor: 'rgba(0, 187, 194, 0.12)'
  },
  '4-main': {
    color: '#0099EB',
    text: '派生指标-主节点',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(50) 0:#08DFE7 1:#00BBC2',
    // cbg: 'linear-gradient(171deg, #14C6FF 0%, #0099EB 100%)',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: indexWhite,
    borderShadow: 'rgba(0, 153, 235, 0.12)',
    strokeColor: 'transparent',
    strokeHoverColor: '#fff',
    circleBorderColor: '#ffffff'
  },
  5: {
    color: '#9646FF',
    text: '复合指标',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(50) 0:#A0DDFF 1:#0099EB',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: indexComposite,
    borderShadow: 'rgba(0, 153, 235, 0.12)',
    borderColor: '#9646FF',
    strokeColor: '#F1E7FF',
    strokeHoverColor: '#C399FF',
    circleBorderColor: '#9646FF',
    circleBgColor: 'rgba(150, 70, 255, 0.09)'
  },
  '5-main': {
    color: '#0099EB',
    text: '复合指标-主节点',
    bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
    cbg: 'l(50) 0:#BC88FF 1:#9646FF',
    // cbg: 'linear-gradient(171deg, #14C6FF 0%, #0099EB 100%)',
    fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
    fieldbg: 'rgba(0, 153, 235, 0.12)',
    hzName: 't-normal',
    img: indexWhite,
    strokeColor: 'transparent',
    strokeHoverColor: '#fff',
    circleBgColor: 'transparent',
    circleBorderColor: '#fff',
    borderShadow: 'rgba(0, 153, 235, 0.12)'
  }
}

export const colors = {
  line: '#CCCED4'
}

export const edgeMap = {
  MAP: {
    // color: '#9646FF',
    color: 'rgba(21, 22, 24, 0.48)',
    text: '映射'
  },
  MODEL: {
    // color: '#0099EB',
    color: 'rgba(21, 22, 24, 0.48)',
    text: '模型'
  },
  TAG: {
    // color: '#FF9431',
    color: 'rgba(21, 22, 24, 0.48)',
    text: '标签'
  },
  RULE: {
    // color: '#00BBC2',
    color: 'rgba(21, 22, 24, 0.48)',
    text: '规则'
  },
  PUSH: {
    color: 'rgba(21, 22, 24, 0.48)',
    text: '引用'
  },
  ELEMENT_EXTRA: {
    color: 'rgba(21, 22, 24, 0.48)',
    text: '规则'
  }
}

export const tableTypeMap = {
  RAW: {
    color: '#1F71FF',
    text: '原始库'
  },
  RESULT: {
    color: '#1F71FF',
    text: '清洗结果库'
  },
  STANDARD: {
    color: '#1F71FF',
    text: '标准库'
  },
  BENCH: {
    color: '#1F71FF',
    text: '专题标准库'
  },
  TOPIC: {
    color: '#1F71FF',
    text: '主题库'
  },
  ELEMENT: {
    color: '#1F71FF',
    text: '要素库'
  },
  RELATION: {
    color: '#1F71FF',
    text: '关系库'
  },
  TAG: {
    color: '#1F71FF',
    text: '标签库'
  },
  STREAMINGTB: {
    color: '#1F71FF',
    text: '流式库'
  },
  TOPIC_NEW: {
    color: '#1F71FF',
    text: '部标专题库'
  },
  PROCESS: {
    color: '#1F71FF',
    text: '开发结果库'
  }
}

export const statusMap = {
  1: {
    color: '#239545',
    text: '成功'
  },
  3: {
    color: '#1F71FF',
    text: '更新中'
  },
  2: {
    color: '#FF5266',
    text: '失败'
  },
  0: {
    color: '#1F71FF',
    text: '更新中'
  },
  6: {
    color: '#1F71FF',
    text: '更新中'
  },
  7: {
    color: 'rgba(15, 34, 67, 0.36)',
    text: '暂未映射'
  }
}
