示例表级血缘
```vue
<template>
<div class="h-104 bg-mono-300">
  <div class="">统计信息</div>
  <div class="h-102">
    <bloodGraph ref="bloodGraph" @getData="getData"></bloodGraph>
  </div>
  <div>画布图例</div>
</div>
</template>
<script>
const grahpData = {
  "nodes": [
            {
                "count": 2,
                "update_time": "2022-08-23 16:12:27",
                "tb_name": "DS_LIST",
                "node_position": 1,
                "ori_tb_name": "DS_LIST",
                "can_extract": 1,
                "is_partition_tb": null,
                "update_status": 1,
                "node_type": 0,
                "tb_id": "tb_874b7ebce75d460bb66a886ce139db62",
                "create_time": "2019-12-21 04:55:41",
                "tb_storage_flag": 0,
                "tb_type": "RAW",
                "access_mode": null,
                "id": "0f3cfa34e838508ae3ef54930998af70"
            },
            {
                "count": 2,
                "update_time": "2022-08-23 16:12:27",
                "tb_name": "DS_LIST",
                "node_position": 2,
                "ori_tb_name": "DS_LIST",
                "can_extract": 1,
                "is_partition_tb": null,
                "update_status": 1,
                "node_type": 0,
                "tb_id": "tb_874b7ebce75d460bb66a886ce139db62",
                "create_time": "2019-12-21 04:55:41",
                "tb_storage_flag": 0,
                "tb_type": "STANDARD",
                "access_mode": null,
                "id": "fc783be281b4e5c9f139d6a5b1e75ddb"
            }
        ],
        "edges": [
            {
                "source": "0f3cfa34e838508ae3ef54930998af70",
                "target": "fc783be281b4e5c9f139d6a5b1e75ddb",
                "lineage_type": "PUSH"
            }
        ]
}
const statisticData = {
  direct_upstream_cnt: {
    title: '直接上游数', // 直接上游数
    count: 0
  },
  total_upstream_cnt: {
    title: '全部上游数', // 全部上游数
    count: 0
  },
  direct_downstream_cnt: {
    title: '直接下游数', // 直接下游数
    count: 0
  },
  total_downstream_cnt: {
    title: '全部下游数', // 全部下游数
    count: 0
  }
}
export default {
  name: 'blood-relations',
  data () {
    return {
      grahpData: {}
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init () {
      grahpData.nodes.forEach(item => {
        if (item.node_position !== 1 && item.can_extract) {
          item.collapsed = true;
        }
      });
      this.$nextTick(()=> {
        this.$refs.bloodGraph.initGraph(grahpData,statisticData, 'table')
      })
    },
    getData (params) {
      console.log(params)
      const requestData = {"nodes": [{"count": 41, "update_time": "2021-12-22 10:41:47", "tb_name": "cyx_test_sync", "node_position": 2, "ori_tb_name": "cyx_test_sync", "can_extract": 0, "is_partition_tb": null, "update_status": 1, "node_type": 0, "tb_id": "tb_2a70f67f764f46088938739f1ebc18b4", "create_time": "2021-11-15 11:18:33", "tb_storage_flag": 0, "tb_type": "ELEMENT", "access_mode": null, "id": "d279c81727a4d171ff605f184bd5b9af"}, {"count": 65311, "update_time": "2022-05-11 19:35:38", "tb_name": "人", "node_position": 2, "ori_tb_name": "人", "can_extract": 0, "is_partition_tb": null, "update_status": 1, "node_type": 0, "tb_id": "tb_bdd65892de5f481690a470d37748fa87", "create_time": "2021-10-13 17:00:41", "tb_storage_flag": 0, "tb_type": "ELEMENT", "access_mode": null, "id": "52d2916e9fdf9ec688e9e55c0ad73cd7"}, {"count": 4, "update_time": "2022-09-02 14:29:08", "tb_name": "要素0223", "node_position": 2, "ori_tb_name": "要素0223", "can_extract": 0, "is_partition_tb": null, "update_status": 1, "node_type": 0, "tb_id": "tb_e4c894a661ac48b2ae33420107453772", "create_time": "2021-02-23 11:30:44", "tb_storage_flag": 0, "tb_type": "ELEMENT", "access_mode": null, "id": "85b41745bc76d96dae4564fff40e6b5d"}, {"count": 1, "update_time": "2022-02-19 11:04:50", "tb_name": "人像照片库-导入-01", "node_position": 2, "ori_tb_name": "人像照片库-导入-01", "can_extract": 0, "is_partition_tb": null, "update_status": 1, "node_type": 0, "tb_id": "tb_22fd95db1d5b43a0a90076bccd69ea10", "create_time": "2022-02-19 11:00:54", "tb_storage_flag": 0, "tb_type": "BENCH", "access_mode": null, "id": "82c167584ab268ee4f7073260ee99176"}], "edges": [{"source": "fc783be281b4e5c9f139d6a5b1e75ddb", "target": "d279c81727a4d171ff605f184bd5b9af", "lineage_type": "RULE"}, {"source": "fc783be281b4e5c9f139d6a5b1e75ddb", "target": "52d2916e9fdf9ec688e9e55c0ad73cd7", "lineage_type": "RULE"}, {"source": "fc783be281b4e5c9f139d6a5b1e75ddb", "target": "85b41745bc76d96dae4564fff40e6b5d", "lineage_type": "RULE"}, {"source": "fc783be281b4e5c9f139d6a5b1e75ddb", "target": "82c167584ab268ee4f7073260ee99176", "lineage_type": "MAP"}]};
      this.$refs.bloodGraph.addNodes(requestData)
    }
  }
}
</script>
```
