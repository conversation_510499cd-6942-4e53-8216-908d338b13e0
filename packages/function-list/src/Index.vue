<template>
  <div class="function-list">
    <div class="search-wrap">
      <a-input :placeholder="placeholder" v-model="keyword" @change="searchValueChange">
        <template #prefix>
          <hz-icon hzName="search" />
        </template>
        <template #suffix>
          <a-dropdown :trigger="['click']" placement="bottomRight" >
            <hz-icon hzName="triangle-filled-down" />
            <template #overlay>
              <a-menu>
                <a-menu-item v-for="item in classList" :key="item.key" @click="handleClick(item)">{{ item.name }}</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-input>
    </div>
    <ul class="list">
      <li v-for="(item, index) in dataList" :key="index">
        <a-tooltip placement="left" overlayClassName="sql-tooltip">
          <template slot="title">
            <div class="p-4 text-sm">
              <h5 class="text-type-800 font-semibold">{{item.name}}</h5>
              <div class="offset-tip">
                <span class="key">用法</span>
                <p class="text">{{item.usage}}</p>
              </div>
              <div class="offset-tip" v-if="item.desc">
                <span class="key">说明</span>
                <p class="text">{{item.desc}}</p>
              </div>
              <div class="offset-tip" v-if="item.demo">
                <span class="key">示例</span>
                <p class="text">{{item.demo}}</p>
              </div>
            </div>
          </template>
          <p class="flex-auto"  @click="insert(item)">
            <hz-icon hzName="fx" class="-mt-px mr-1" className="text-primary-900" />{{ item.name }}
          </p>
        </a-tooltip>
      </li>
    </ul>
  </div>
</template>
<script>
import { debounce } from 'lodash'
export default {
  name: 'FunctionList',
  props: {
    formulaList: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    formulaList (newVal) {
      this.dataList = newVal[0]
    }
  },
  data () {
    return {
      keyword: '',
      placeholder: '请输入',
      dataList: [],
      classList: [
      {
          name: '全部',
          key: 0
        },
        {
          name: '聚合统计',
          key: 1
        },
        {
          name: '非聚合统计',
          key: 2
        },
        {
          name: '日期和时间',
          key: 3
        },
        {
          name: '字符串',
          key: 4
        },
        {
          name: '逻辑',
          key: 5
        },
        {
          name: '数值',
          key: 6
        }
      ]
    }
  },
  methods: {
    searchValueChange: debounce(function () {
      const list = []
      if (this.keyword) {
        this.formulaList[0].forEach(item => {
          if (item.name.toLowerCase().indexOf(this.keyword.toLowerCase()) > -1) {
            list.push(item)
          }
        })
        this.dataList = [...list]
      } else {
        this.dataList = [...this.formulaList[0]]
      }
    },
    500,
    {
      trailing: true
    }),
    handleClick (data) {
      this.keyword = ''
      this.placeholder = data.name
      this.dataList = this.formulaList[data.key]
    },
    insert (data) {
      this.$emit('clickFunction', data)
    }
  }
}
</script>
<style lang="scss" scoped>
.function-list {
  @apply flex-auto overflow-hidden flex flex-col;
  .search-wrap {
    @apply mx-4 mt-2;
  }
}
.list {
  @apply leading-9 flex-auto overflow-auto px-4 my-2;
  li{
    @apply hover:bg-mono-a-100 cursor-pointer pl-4 flex rounded-md;
    .operator-layer {
      @apply w-4 mr-2 invisible;
    }
  }
  li:hover {
    .operator-layer {
      @apply visible;
    }
  }
}
.offset-tip {
  @apply flex mt-2
}
.key {
  @apply w-10 font-semibold text-type-700 flex-shrink-0
}
.text {
  @apply flex-1 leading-5 text-type-800
}
</style>
