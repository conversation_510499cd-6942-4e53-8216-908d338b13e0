<template>
  <div class="premission-config-container h-full relative">
    <div class="h-8 pl-4 leading-8 absolute top-10">
      <a-checkbox v-model="viewChoice" @change="handelQuery">查看已选</a-checkbox>
    </div>
    <a-tabs :hasLine="true" v-model="activeKey" :tabBarStyle="{paddingLeft: '1rem'}" size="small" :animated="false" @change="onActiveChange">
      <!-- <a-tab-pane key="role" :tab="`分配到角色${selectRoleNum ? '('+selectRoleNum+')' : ''}`" class="h-full">
        <roleConfig ref="roleConfig" :roleList="roleList" :originSelectRoles="originSelectRoles" @role-num="getSelectRoleNum"></roleConfig>
      </a-tab-pane>
      <a-tab-pane key="user" :tab="`分配到用户${selectUserNum ? '('+selectUserNum+')' : ''}`" class="h-full">
        <userConfig ref="userConfig" :enableAllUser="enableAllUser" :userInfo="userInfo" :originSelectUsers="originSelectUsers" @load-data="onLoadUserData" @user-num="getSelectUserNum"></userConfig>
      </a-tab-pane> -->
      <a-tab-pane key="group" :tab="`分配到组织${selectGroupNum ? '('+selectGroupNum+')' : ''}`" class="h-full">
        <groupConfig ref="groupConfig" :groupList="groupList" :originSelectGroups="originSelectGroups" @group-num="getSelectGroupNum"></groupConfig>
      </a-tab-pane>
      <a-input-search v-model="queryText" style="width: 8.5rem;margin-right: 1rem;top: -4px;" slot="tabBarExtraContent"/>
    </a-tabs>
  </div>
</template>
<script>
/** 权限控制
 * <AUTHOR>
 */
import roleConfig from './Role.vue'
import groupConfig from './Group.vue'
import userConfig from './User.vue'
/**
 * @displayName permissionConfig 权限控制
 */
export default {
  name: 'permissionConfig',
  components: {
    roleConfig,
    groupConfig,
    userConfig
  },
  provide () {
    return {
      permission: this
    }
  },
  props: {
    /**
     * 角色数据，结构如下：
     * [{role_id:'**',role_name: '**'}]
     */
    roleList: {
      type: Array,
      default: () => []
    },
    /**
     * 初始选中角色ids
     */
    originSelectRoles: [],
    /**
     * 组织列表，[{group_list:{group_name:'',group_list:{},group_id:''}}]
     */
    groupList: {
      type: Array,
      default: () => []
    },
    /**
     * 初始选中组织ids
     */
    originSelectGroups: [],
    /**
     * 用户数据，结构如下：
     * [{page:'**',page_size: '**',user_list:[],pages_count: '**'}]
     */
    userInfo: {
      type: Object,
      default: () => {}
    },
    /**
     * 初始选中用户ids
     */
    originSelectUsers: {
      type: Array,
      default: () => []
    },
    /**
     * 异步加载组织数据
     */
    loadGroupData: {
      type: Function,
      default: () => undefined
    },
    /**
     * 异步加载用户数据
     */
    loadUserData: {
      type: Function,
      default: () => undefined
    },
    /**
     * 用户是否可以全选
     */
    enableAllUser: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      activeKey: 'group',
      queryText: '',
      viewChoice: false,
      selectRoleNum: 0,
      selectGroupNum: 0,
      selectUserNum: 0,
      groupLoading: true,
      userLoading: false,
      groupHasLoaded: false,
      userHasLoaded: false
    }
  },
  watch: {
    queryText: {
      handler () {
        this.handelQuery()
      }
    },
    originSelectRoles: {
      handler () {
        this.selectGroupNum = this.originSelectGroups.length
        this.selectRoleNum = this.originSelectRoles.length
        this.selectUserNum = this.originSelectUsers.length
        this.$forceUpdate()
      }
    },
    groupLoading: {
      handler (value) {
        if (!value) {
          !this.groupHasLoaded && this.$refs.groupConfig.initTree()
          this.$refs.groupConfig.searchTree()
          this.groupHasLoaded = true
        }
      }
    },
    userLoading: {
      handler (value) {
        if (!value) {
          !this.userHasLoaded && this.$refs.userConfig.initUser()
          this.$refs.userConfig.searchUser()
          this.userHasLoaded = true
        }
      }
    }
  },
  mounted () {
    this.selectGroupNum = this.originSelectGroups.length
    this.selectRoleNum = this.originSelectRoles.length
    this.selectUserNum = this.originSelectUsers.length
  },
  methods: {
    onActiveChange () {
      this.viewChoice = false
      this.handelQuery()
    },
    getSelectRoleNum (num) {
      this.selectRoleNum = num
    },
    getSelectGroupNum (num) {
      this.selectGroupNum = num
    },
    getSelectUserNum (num) {
      this.selectUserNum = num
    },
    handelQuery () {
      switch (this.activeKey) {
        case 'role' :
          this.$nextTick(() => {
            this.$refs.roleConfig.searchRole()
          })
          break
        case 'user' :
          if (this.userInfo.user_list.length === 0) {
            this.onLoadUserData({
              queryText: this.queryText,
              page: 1
            })
            break
          }
          this.$nextTick(() => {
            this.$refs.userConfig.searchUser()
          })
          break
        case 'group' :
          if (this.groupList.length === 0) {
            this.onLoadGroupData()
            break
          }
          this.$nextTick(() => {
            this.$refs.groupConfig.searchTree()
          })
          break
      }
    },
    onLoadUserData (data) {
      this.userLoading = true
      this.loadUserData({
        queryText: data.queryText,
        page: data.page
      }).then(() => {
        this.userLoading = false
      })
    },
    onLoadGroupData () {
      this.groupLoading = true
      this.loadGroupData().then(() => {
        this.groupLoading = false
      })
    },
    /**
     * 获取角色操作后数据
     *
     * @public
     * @returns {object} {add_roles: [],del_roles:[]}
     */
    getRole () {
      return this.$refs.roleConfig
        ? this.$refs.roleConfig.getRole()
        : {
          add_roles: [],
          del_roles: []
        }
    },
    /**
     * 获取用户操作后数据
     *
     * @public
     * @returns {object} {add_users: [],del_users:[]}
     */
    getUser () {
      return this.$refs.userConfig
        ? this.$refs.userConfig.getUser()
        : {
        add_users: [],
        del_users: []
      }
    },
    /**
     * 获取组织操作后数据
     *
     * @public
     * @returns {object} {add_groups: [],del_groups:[]}
     */
    getGroup () {
      return this.$refs.groupConfig
        ? this.$refs.groupConfig.getGroup()
        : {
          add_groups: [],
          del_groups: []
        }
    }
  },
  destroyed () {
  }
}

</script>
<style scoped>
::v-deep .ant-tabs{
  @apply h-full pb-4;
  overflow: inherit;
}
::v-deep .ant-tabs-bar {
  @apply mb-2
}
::v-deep .ant-tabs-content {
  @apply pt-8 h-calc10
}
</style>
