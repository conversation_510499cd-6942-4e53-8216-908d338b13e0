<template>
  <div class="pl-4 h-full overflow-y-auto overflow-x-hidden">
    <a-tree
    checkable
    checkStrictly
    :replaceFields = "replaceFields"
    v-model="checkedKeys"
    :treeData="groupTree"
    :expandedKeys = "expandedKeys"
    :autoExpandParent = "autoExpandParent"
    :selectedKeys="selectedKeys"
    @expand="onExpand"
    @check="onSelect"
    />
    <div v-if="groupTree.length <= 0 && !permission.groupLoading" :style="{'height': '100%','display': 'flex'}">
      <noDataSpace size="sm"/>
    </div>
    <HzLoading :showLoading="permission.groupLoading"></HzLoading>
</div>
</template>

<script>
import { HzLoading, noDataSpace } from '@hertz/base-components'
import { cloneDeep as _cloneDeep, filter as _filter, includes as _includes, indexOf as _indexOf } from 'lodash'
export default {
  name: 'groupConfig',
  props: {
    /**
     * 组织数据，结构如下：
     * [{group_id:'**',group_name: '**',group_list[{group_id:'**',group_name: '**'}]}]
     */
    groupList: {
      type: Array,
      default: () => []
    },
    /**
     * 初始选中组织数据[id1,id2,...]
     */
    originSelectGroups: []
  },
  inject: ['permission'],
  data () {
    return {
      // checkedKeys: [],
      checkedKeys: {
        checked: [],
        halfChecked: []
      },
      replaceFields: {
        children: 'group_list',
        title: 'group_name',
        key: 'group_id'
      },
      expandedKeys: [],
      autoExpandParent: false,
      originTree: [],
      groupTree: [],
      selectedKeys: [],
      oldQueryText: '',
      oldViewChoice: false,
      loading: true
    }
  },
  components: {
    HzLoading,
    noDataSpace
  },
  mounted () {
    this.selectedKeys = [...this.originSelectGroups]
  },
  methods: {
    initTree () {
      this.originTree = _cloneDeep(this.groupList)
      this.groupTree = _cloneDeep(this.groupList)
      let expandedKeys = []
      expandedKeys = this.selectedKeys
      this.checkedKeys.checked = this.selectedKeys
      Object.assign(this, {
        selectedKeys: this.selectedKeys,
        autoExpandParent: true,
        expandedKeys
      })
      this.changeFolderCheckStatus({ treeData: this.groupTree })
      this.$forceUpdate()
    },
    onExpand (keys) {
      this.expandedKeys = keys
      this.autoExpandParent = false
    },
    onSelect (selectedKeys, event) {
      this.selectedKeys = selectedKeys.checked
      this.permission.viewChoice && this.searchTree(true)
      const nodeData = event.node.dataRef
      if (!!nodeData.group_list) { // 分支节点
        this.deepCheckStatus({ treeData: nodeData.group_list, checked: event.checked })
      }
      this.changeFolderCheckStatus({ treeData: this.groupTree })
      this.emitSelectGroupNum()
      this.$forceUpdate()
    },
    searchTree (goNext) {
      if (this.oldQueryText === this.permission.queryText && this.oldViewChoice === this.permission.viewChoice && !goNext) return
      this.oldQueryText = this.permission.queryText
      this.oldViewChoice = this.permission.viewChoice
      const treeNode = {
        group_list: _cloneDeep(this.originTree)
      }
      this.expandedKeys = []
      this.searchGroup(treeNode)
      this.groupTree = treeNode.group_list
      this.checkedKeys.checked = this.selectedKeys
      Object.assign(this, {
        selectedKeys: this.selectedKeys,
        autoExpandParent: true
      })
      this.$forceUpdate()
    },
    searchGroup (tree) {
      tree.group_list = _filter(tree.group_list, treeInst => {
        const matchText = !this.permission.queryText.trim() ? true : _includes(treeInst.group_name, this.permission.queryText)
        const hasSelect = _indexOf(this.selectedKeys, treeInst.group_id) !== -1
        const matchSelf = this.permission.viewChoice ? matchText && hasSelect : matchText
        if (matchSelf) {
          this.expandedKeys.push(treeInst.group_id)
        }
        let matchChild = false
        if (treeInst && tree.group_list) {
          matchChild = this.searchGroup(treeInst)
        } else {
          matchChild = false
        }
        const matchRes = matchSelf || matchChild
        return matchRes
      })
      return tree.group_list.length > 0
    },
    emitSelectGroupNum () {
      this.$emit('group-num', this.selectedKeys.length)
    },
    getGroup () {
      const summaryData = {
        add_groups: [],
        del_groups: []
      }
      const choiceGroupIds = new Set(this.selectedKeys)
      const originalGroupIds = new Set(this.originSelectGroups)
      summaryData.add_groups = [...choiceGroupIds].filter(groupId => !originalGroupIds.has(groupId))
      summaryData.del_groups = [...originalGroupIds].filter(groupId => !choiceGroupIds.has(groupId))
      return summaryData
    },
    // changeCheck (checkedKeys, event) {
    //   const nodeData = event.node.dataRef
    //   if (nodeData.nodeType === 'folder') {
    //     this.deepCheckStatus({ treeData: nodeData.children, checked: event.checked })
    //   }
    //   this.changeFolderCheckStatus({ treeData: this.folderData })
    // },
    deepCheckStatus ({ treeData, checked }) {
      treeData.forEach((item) => {
        if ((item.isLeaf || !item.group_list) && !item.disableCheckbox) {
          if (checked) {
            if (this.checkedKeys.checked.indexOf(item.group_id) === -1) {
              this.checkedKeys.checked.push(item.group_id)
            }
          } else {
            for (let i = this.checkedKeys.checked.length - 1; i >= 0; i--) {
              if (this.checkedKeys.checked[i] === item.group_id) {
                this.checkedKeys.checked.splice(i, 1)
              }
            }
          }
        }
        if (item.group_list && item.group_list.length > 0) {
          this.deepCheckStatus({ treeData: item.group_list, checked: checked })
        }
      })
    },
    changeFolderCheckStatus ({ treeData }) {
      treeData.forEach((item, key, array) => {
        if (item.group_list && item.group_list.length > 0) {
          this.deepTreeData(item)
        }
      })
    },
    deepTreeData (treeNode) {
      // let status = 0 // 0->不选中 1-> 选中 2-> 半选
      let status = 0
      let checkedNum = 0
      let halfCheckedNum = 0
      const checkedIndex = this.checkedKeys.checked.findIndex((item) => item === treeNode.group_id)
      treeNode.group_list.forEach((item, key, array) => {
        if (item.group_list && item.group_list.length > 0) {
          this.deepTreeData(item)
        }
        if (this.checkedKeys.checked.indexOf(item.group_id) > -1) {
          checkedNum++
        }
        if (this.checkedKeys.halfChecked.indexOf(item.group_id) > -1) {
          halfCheckedNum++
        }
      })
      if (checkedNum === treeNode.group_list.length) { // 全选
        status = 1
      } else if (checkedNum > 0 && checkedNum < treeNode.group_list.length) { // 半选
        status = 2
      } else if (halfCheckedNum > 0) {
        status = 2
      }
      if (checkedIndex === -1 && status === 1) {
        this.checkedKeys.checked.push(treeNode.group_id)
      }
      if (checkedIndex > -1 && (status === 0 || status === 2)) {
        this.checkedKeys.checked.splice(checkedIndex, 1)
      }
      const halfCheckedIndex = this.checkedKeys.halfChecked.findIndex((item) => item === treeNode.group_id)
      if (halfCheckedIndex > -1 && (status === 1 || status === 0)) {
        this.checkedKeys.halfChecked.splice(halfCheckedIndex, 1)
      } else if (halfCheckedIndex === -1 && status === 2) {
        this.checkedKeys.halfChecked.push(treeNode.group_id)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
