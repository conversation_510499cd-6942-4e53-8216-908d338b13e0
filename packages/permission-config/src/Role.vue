<template>
  <div class="pl-4 h-full overflow-auto" >
    <ul v-if="roleList.length > 0">
      <template v-for="role in roleList">
        <li :key="role.role_id" :title="role.role_name" class="nowrap cursor-pointer h-8 leading-8 text-type-800" v-if="!role.hide">
          <label class="cursor-pointer">
            <a-checkbox :checked="selectedRoles.has(role.role_id)" @change="onRoleChecked(role)" class="mr-1"></a-checkbox>
            {{role.role_name}}
          </label>
        </li>
      </template>
  </ul>
  <div v-if="roleList.length <= hiddenRoleNum" :style="{'height': '100%','display': 'flex'}">
    <noDataSpace size="sm"/>
  </div>
</div>
</template>

<script>
import { noDataSpace } from '@hertz/base-components'
import { each as _each, includes as _includes } from 'lodash'
export default {
  name: 'roleConfig',
  props: {
    /**
     * 角色数据，结构如下：
     * [{role_id:'**',role_name: '**'}]
     */
    roleList: {
      type: Array,
      default: () => []
    },
    /**
     * 初始选中角色 ['id1','id2',...]
     */
    originSelectRoles: {
      type: Array,
      default: () => []
    }
  },
  components: {
    noDataSpace
  },
  watch: {
    originSelectRoles (val) {
      this.selectedRoles = new Set(val)
      this.$forceUpdate()
    }
  },
  inject: ['permission'],
  data () {
    return {
      selectedRoles: new Set([]),
      hiddenRoleNum: 0,
      oldQueryText: '',
      oldViewChoice: false
    }
  },
  mounted () {
  },
  methods: {
    onRoleChecked (role) {
      this.selectedRoles.has(role.role_id) ? this.selectedRoles.delete(role.role_id) : this.selectedRoles.add(role.role_id)
      role.hide = this.permission.viewChoice ? !this.selectedRoles.has(role.role_id) : role.hide
      this.emitSelectRoleNum()
      this.$forceUpdate()
    },
    searchRole () {
      if (this.oldQueryText === this.permission.queryText && this.oldViewChoice === this.permission.viewChoice) return
      this.oldQueryText = this.permission.queryText
      this.oldViewChoice = this.permission.viewChoice
      this.hiddenRoleNum = 0
      _each(this.roleList, item => {
        item.hide = false
        if (this.permission.queryText && !_includes(item.role_name, this.permission.queryText)) {
          item.hide = true
          this.hiddenRoleNum++
        }
        if (this.permission.viewChoice && !this.selectedRoles.has(item.role_id) && !item.hide) {
          item.hide = true
          this.hiddenRoleNum++
        }
      })
    },
    emitSelectRoleNum () {
      this.$emit('role-num', this.selectedRoles.size)
    },
    getRole () {
      const summaryData = {
        add_roles: [],
        del_roles: []
      }
      const choiceRoleIds = this.selectedRoles
      const originalRoleIds = new Set(this.originSelectRoles)
      summaryData.add_roles = [...choiceRoleIds].filter(userId => !originalRoleIds.has(userId))
      summaryData.del_roles = [...originalRoleIds].filter(userId => !choiceRoleIds.has(userId))
      return summaryData
    }
  }

}
</script>

<style lang="scss" scoped>

</style>
