示例
```vue
<template>
  <div class="h-60"> 
    <permissionConfig :roleList="roleList" :originSelectRoles="[]" :groupList="groupTree" 
    :originSelectGroups="[]" :userInfo="userInfo" :originSelectUsers="[]" :load-group-data="onLoadGroupData" :load-user-data="onLoadUserData"/>
  </div>
 </template>

<script>
const roleList = [{"role_name": "武警", "role_id": "ro_9d80fb40b1a911e99878a0369f94a5b2"},{"role_name": "刑警", "role_id": "ro_11fff604b7f611e9a67da0369f94a5b2"}]
const groupList = [{"group_list": [{"group_list": [{"group_list": [{"group_list": [{"group_name": "dbfhsgg","group_id": "gp_acf156afede4424f9f36a3f64e550f2e"}], "group_name": "v你那边", "group_id": "gp_a5e72dbd64d04d86aac48c20b0be5f16"}, {"group_name": "sdjfjadh", "group_id": "gp_b30174336aee49c0a0215301f7a9f137"}], "group_name": "测试组织包含被删除的人xr", "group_id": "gp_b222a2802d0d11eab7f4a0369f94a5b2"}],'group_name': '子公司'}],'group_name': '全公司'}]

const userList = [{"username": "admin", "name": "admin-超管", "userid": "d0438698030d3e206cf55f8e1005e996"}]

export default {
  data() {
    return { 
      roleList: roleList,
      groupTree: [],
      originalGroupData: groupList,
      originalUserData: userList,
      userInfo: {
        page: 1,
        page_size: 20,
        user_list: [],
        pages_count: 20
      }
    }
  },
  methods: {
    onLoadUserData (data) {
      this.queryText = data.queryText
      this.userInfo.page = data.page
      return new Promise((resolve, reject) => {
        this.userInfo.user_list = userList
        this.userInfo.page = this.userInfo.page + 1
        this.userInfo.pages_count = this.userInfo.page + 2
        resolve()
      })
  },
  onLoadGroupData () {
    return new Promise((resolve, reject) => {
        this.groupTree = this.originalGroupData
        resolve()
      })
    }
  }
}
</script>
```
