<template>
  <div class="pl-4 h-full overflow-auto" ref="ulRef">
    <ul v-if="userList.length > 0" class="scroll-body h-full overflow-x-auto">
      <div class="h-8 leading-8 absolute top-10 mt-8" v-if="enableAllUser">
        <a-checkbox v-model="selectAll" @change="onSelectAllUser" :indeterminate="!!selectedUser.size && selectedUser.size !== userList.length">全选</a-checkbox>
      </div>
      <template v-if="enableAllUser && !permission.viewChoice">
        <VirtualScroll :list="userList" v-slot="{ virtualList }" ref="virtualList">
          <li :key="user.userid" :title="user.username" class="nowrap cursor-pointer h-8 leading-8 text-type-800" v-for="user in virtualList">
            <label class="cursor-pointer">
              <a-checkbox :checked="selectedUser.has(user.userid)" @change="onUserChecked(user)" class="mr-1"></a-checkbox>
              {{user.name}}({{user.username}})
            </label>
          </li>
        </VirtualScroll>
      </template>
      <template v-else>
        <li :key="user.userid" :title="user.username" class="nowrap cursor-pointer h-8 leading-8 text-type-800" v-for="user in userList">
          <label class="cursor-pointer">
            <a-checkbox :checked="selectedUser.has(user.userid)" @change="onUserChecked(user)" class="mr-1"></a-checkbox>
            {{user.name}}({{user.username}})
          </label>
        </li>
      </template>
    </ul>
    <div v-if="userList.length === 0 && !permission.userLoading" :style="{'height': '100%','display': 'flex'}">
      <noDataSpace size="sm"/>
    </div>
    <HzLoading :showLoading="permission.userLoading"></HzLoading>
  </div>
</template>

<script>
import { HzLoading, noDataSpace } from '@hertz/base-components'
import { cloneDeep as _cloneDeep, findIndex as _findIndex, filter as _filter, includes as _includes, map as _map, debounce as _debounce } from 'lodash'
import VirtualScroll from './VirtualScroll.vue'
export default {
  name: 'userConfig',
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    },
    originSelectUsers: {
      type: Array,
      default: () => []
    },
    /**
     * 用户是否可以全选
     */
    enableAllUser: {
      type: Boolean,
      default: false
    }
  },
  inject: ['permission'],
  data () {
    return {
      userList: [],
      selectedUserList: [],
      selectedUser: new Set([]),
      oldQueryText: '',
      oldViewChoice: false,
      selectAll: false
    }
  },
  components: {
    HzLoading,
    noDataSpace,
    VirtualScroll
  },
  watch: {
    userInfo: {
      handler (info) {
        this.userList = _cloneDeep(info.user_list)
      },
      deep: true
    }
  },
  mounted () {
  },
  methods: {
    initUser () {
      this.userList = _cloneDeep(this.userInfo.user_list)
      this.selectedUserList = _cloneDeep(this.originSelectUsers)
      this.selectedUser = new Set([..._map(this.selectedUserList, user => user.userid)])
      this.$nextTick(() => {
        const scrollBox = document.querySelector('.scroll-body')
        const that = this
        scrollBox && scrollBox.addEventListener('scroll', () => {
          const scroolDiff = scrollBox.scrollHeight - scrollBox.scrollTop - scrollBox.clientHeight
          if (scroolDiff <= 0 && that.userInfo.page <= that.userInfo.pages_count && !that.permission.viewChoice) {
            that.serach({
              queryText: that.permission.queryText,
              page: that.userInfo.page
            })
          }
        })
      })
    },
    onUserChecked (user) {
      if (this.selectedUser.has(user.userid)) {
        this.selectedUser.delete(user.userid)
        this.deleteChoice(user)
      } else {
        this.selectedUser.add(user.userid)
        this.addChoice(user)
      }
      this.permission.viewChoice && this.searchUser(true)
      this.$emit('user-num', this.selectedUser.size)
      this.$forceUpdate()
      this.$refs.virtualList && this.$refs.virtualList.$forceUpdate()
    },
    onSelectAllUser (e) {
      // 全选
      const val = e.target.checked
      if (val) {
        this.selectedUser = new Set([..._map(this.userList, user => user.userid)])
        this.selectedUserList = this.userList.map(user => {
          return _cloneDeep({
            name: user.name,
            username: user.username,
            userid: user.userid
          })
        })
      } else {
        this.selectedUser = new Set([])
        this.selectedUserList = []
      }
      this.permission.viewChoice && this.searchUser(true)
      this.$emit('user-num', this.selectedUser.size)
      this.$forceUpdate()
    },
    addChoice (user) {
      const index = _findIndex(this.selectedUserList, (selectedUser) => selectedUser.userid === user.userid)
      if (index <= -1) {
        this.selectedUserList.push({
          name: user.name,
          username: user.username,
          userid: user.userid
        })
      }
    },
    deleteChoice (user) {
      const index = _findIndex(this.selectedUserList, selectedUser => selectedUser.userid === user.userid)
      this.selectedUserList.splice(index, 1)
    },
    searchUser (goNext) {
      if (this.oldQueryText === this.permission.queryText && this.oldViewChoice === this.permission.viewChoice && !goNext) return
      this.oldQueryText = this.permission.queryText
      this.oldViewChoice = this.permission.viewChoice
      if (this.permission.viewChoice) {
        this.userList = _filter(this.selectedUserList, selectedUser => {
          const matchNoEmptyText = _includes(selectedUser.name, this.permission.queryText) || _includes(selectedUser.username, this.permission.queryText)
          const matchText = !this.permission.queryText.trim() ? true : matchNoEmptyText
          return matchText
        })
      } else {
        this.serach({
          queryText: this.permission.queryText,
          page: 1
        })
      }
    },
    serach: _debounce(function (data) {
        this.$emit('load-data', data)
    }, 500),
    getUser () {
      const summaryData = {
        add_users: [],
        del_users: []
      }
      const selectedUserIds = this.selectedUser
      const originalUserIds = new Set(_map(this.originSelectUsers, user => user.userid))
      summaryData.add_users = [...selectedUserIds].filter(userId => !originalUserIds.has(userId))
      summaryData.del_users = [...originalUserIds].filter(userId => !selectedUserIds.has(userId))
      return summaryData
    }
  }

}
</script>

<style lang="scss" scoped>

</style>
