<template>
  <div class="virtual-list h-full pt-8">
    <div class="relative h-full overflow-auto scroll-body">
      <!-- 这里是用于撑开高度，出现滚动条用 -->
      <div class="list-view-phantom absolute left-0 right-0 top-0" ref="clientHeightRef" :style="{ height: list.length * itemHeight + 'px' }"></div>
      <ul class="h-full" ref="contentRef">
        <slot :virtualList="virtualRenderData"></slot>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: 'VirtualScroll',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    itemHeight: {
      type: Number,
      default: 32
    }
  },
  data () {
    return {
      virtualRenderData: [],
      containerHeight: 0,
      bufferCount: 5
    }
  },
  watch: {
    list: {
      handler () {
        this.update()
      }
    }
  },
  mounted () {
    this.containerHeight = this.$el.querySelector('ul').getBoundingClientRect().height
    this.update()
    this.$el.querySelector('.scroll-body').addEventListener('scroll', this.update)
  },
  beforeDestroy () {
    this.$el.querySelector('.scroll-body').removeEventListener('scroll', this.update)
  },
  methods: {
    update (e) {
      const scrollTop = e?.target.scrollTop || 0
      this.$nextTick(() => {
        // 获取当前可展示数量
        const count = Math.ceil(this.containerHeight / this.itemHeight)
        const start = Math.floor(scrollTop / this.itemHeight)
        // 取得可见区域的结束数据索引
        const end = start + count + this.bufferCount
        // 计算出可见区域对应的数据，让 Vue.js 更新
        this.virtualRenderData = this.list.slice(start, end)
        this.$refs.contentRef.style.webkitTransform = `translate3d(0, ${start * this.itemHeight}px, 0)`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.virtual-list {
  .list-view-phantom {
    z-index: -1;
  }
}
</style>
