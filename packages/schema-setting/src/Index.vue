<template>
  <div>
    <div class="schema-head">
      <a-checkbox
        :checked="checkAll"
        :indeterminate="indeterminate"
        @change="onCheckAll($event)"
        :class="{'invisible': searchResEmpty}"
        v-if="useInModal && !radio">
        <span class="font-semibold">全部</span>
      </a-checkbox>
      <div class="text-primary-900 border-b-1 border-solid border-primary-900 px-4" v-else-if="!useInModal">数据表字段</div>
      <div>
        <a-input v-model="keyword" @change="doSearch" placeholder="请输入字段名称" size="small">
          <template #prefix>
            <hz-icon hzName="search" />
          </template>
        </a-input>
      </div>
    </div>
    <div class="schema-bd-wrap">
      <dl v-for="(item,index) in schemaData" :key="index" class="flex leading-6 mb-2" :class="{'hidden': !item.show}">
        <dt :class="radio ? 'w-20' : 'w-25'">
          <a-checkbox :checked="item.selected" :indeterminate="item.indeterminate" @change="onCheckAllChange($event,item)" v-if="!radio">
            <hz-icon :hzName="item.icon" class="mr-1 -mt-1px" className="text-primary-900"/>
            <span>{{item.name}}</span>
          </a-checkbox>
          <template v-else>
            <hz-icon :hzName="item.icon" class="mr-1 -mt-1px" className="text-primary-900"/>
            <span>{{item.name}}</span>
          </template>
        </dt>
        <dd class="flex-1 border-b-1 border-solid border-mono-300 pb-2">
          <ul>
            <li v-for="(it, i) in item.list" :key="i" class="inline-block mr-6" :class="{'hidden': !it.show}">
              <a-checkbox
                class="item-checkbox"
                :checked="it.selected"
                @change="changeChecked($event, it, index)"
                v-if="!radio">
                {{it.title}}
              </a-checkbox>
              <a-radio-group name="radioGroup" v-model="selectedFieldId" @change="onSelectChange(index, i)" v-else>
                <a-radio :value="it.field_id">{{ it.title }}</a-radio>
              </a-radio-group>
            </li>
          </ul>
        </dd>
      </dl>
      <div v-if="useInModal && searchResEmpty">
        <no-data-space class="self-center" />
      </div>
      <div v-if="!useInModal && searchResEmpty" class="text-center py-2 text-type-700">没有符合条件的字段</div>
    </div>
    <div class="text-right schema-ft-wrap" v-if="!useInModal"><a-button type="primary" @click="doSure">确定</a-button></div>
  </div>
</template>
<script>
/**
 * @displayName schemaSetting 设置显示字段
 */
export default {
  name: 'schemaSetting',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    // 是否用于弹窗里用于选择字段
    useInModal: {
      type: Boolean,
      default: false
    },
    // 是否为单选
    radio: {
      type: Boolean,
      default: false
    }
  },
  mounted () {
    this.init()
  },
  data () {
    return {
      keyword: '',
      schemaData: [
        {
          name: '文本',
          icon: 'type-string',
          selected: false,
          show: true,
          list: []
        },
        {
          name: '数值',
          icon: 'type-number',
          selected: false,
          show: true,
          list: []
        },
        {
          name: '日期',
          icon: 'type-date',
          selected: false,
          show: true,
          list: []
        }
      ],
      searchResEmpty: false,
      indeterminate: false,
      checkAll: false,
      selectedFieldId: ''
    }
  },
  methods: {
    init () {
      const selectedInfo = {
        string: 0,
        number: 0,
        date: 0
      }
      this.data.forEach((item) => {
        item.show = true
        if (item.type === 1 || item.type === 0) {
          this.schemaData[1].list.push(item)
          if (item.selected) {
            selectedInfo.number++
          }
        } else if (item.type === 3) {
          this.schemaData[2].list.push(item)
          if (item.selected) {
            selectedInfo.date++
          }
        } else {
          this.schemaData[0].list.push(item)
          if (item.selected) {
            selectedInfo.string++
          }
        }
      })
      this.schemaData[0].show = this.schemaData[0].list.length > 0
      this.schemaData[1].show = this.schemaData[1].list.length > 0
      this.schemaData[2].show = this.schemaData[2].list.length > 0
      this.schemaData.forEach((item, key) => {
        this.setCheckAllChange(key)
      })
      // this.schemaData[0].selected = selectedInfo.string === this.schemaData[0].list.length
      // this.schemaData[1].selected = selectedInfo.number === this.schemaData[1].list.length
      // this.schemaData[2].selected = selectedInfo.date === this.schemaData[2].list.length
    },
    doSearch () {
      const keyword = this.keyword.trim()
      let count = 0
      this.schemaData.forEach((item) => {
        let i = 0
        item.list.forEach((it) => {
          if (keyword && it.title.toLowerCase().indexOf(keyword.toLowerCase()) > -1) {
            i++
            count++
            it.show = true
          } else if (!keyword) {
            i++
            count++
            it.show = true
          } else {
            it.show = false
          }
        })
        if (i === 0) {
          item.show = false
        } else {
          item.show = true
        }
      })
      if (count === 0) {
        this.searchResEmpty = true
      } else {
        this.searchResEmpty = false
      }
      this.schemaData.forEach((item, key) => {
        this.setCheckAllChange(key)
      })
    },
    onSelectChange (index, i) {
      this.schemaData.forEach(cat => {
        cat.list.forEach(item => {
          item.selected = false
        })
      })
      this.schemaData[index].list[i].selected = true
    },
    changeChecked (event, data, index) {
      data.selected = event.target.checked
      this.setCheckAllChange(index)
    },
    setCheckAllChange (index) {
      let i = 0
      let countTotal = 0
      const array = this.schemaData[index]
      array.list.forEach((item) => {
        if (item.show) {
          countTotal++
          item.selected && i++
        }
      })

      if (countTotal !== 0 && i === countTotal) {
        array.selected = true
        array.indeterminate = false
      } else if (i > 0) {
        array.selected = false
        array.indeterminate = true
      } else {
        array.selected = false
        array.indeterminate = false
      }
      this.updateCheckAll()
    },
    onCheckAllChange (event, data) {
      data.selected = event.target.checked
      data.indeterminate = false
      data.list.forEach((item) => {
        if (item.show) {
          item.selected = data.selected
        }
      })
      this.updateCheckAll()
    },
    onCheckAll (event) {
      this.schemaData.forEach(item => {
        this.onCheckAllChange(event, item)
      })
    },
    updateCheckAll () {
      const s = this.schemaData.filter(item => item.show)
      const i = s.filter(item => item.selected)
      if (s.length === i.length) {
        this.checkAll = true
        this.indeterminate = false
      } else if (i.length > 0 || s.filter(item => item.indeterminate).length > 0) {
        this.checkAll = false
        this.indeterminate = true
      } else {
        this.checkAll = false
        this.indeterminate = false
      }
    },
    doSure () {
      const data = {
        status: 1,
        data: {
          fids: []
        }
      }
      this.schemaData.forEach((item) => {
        item.list.forEach((it) => {
          if (it.selected) {
            data.data.fids.push(it.field_id)
          }
        })
      })
      this.$emit('doFilter', data)
    }
  }
}
</script>
<style scoped>
.schema-head {
  @apply flex justify-between border-b-1 border-solid border-mono-300 mb-4 leading-8;
}
::v-deep .item-checkbox .ant-checkbox + span {
  @apply inline-block w-36 whitespace-nowrap overflow-hidden overflow-ellipsis align-top;
}
</style>
