示例
```vue
<template>
  <schemaSetting :data="fieldData" @doFilter="filterSchema"></schemaSetting>
</template>
<script>
const fields = [
    {
        "remark": "",
        "original_name": "name",
        "name": "name",
        "data_type": "string",
        "title": "name",
        "aggregator": "",
        "selected": true,
        "field_id": "fkb068931c",
        "real_type": null,
        "seq_no": 0,
        "uniq_index": 0,
        "fid": "fkb068931c",
        "type": 2,
        "id": 2836,
        "ctime": "2020-01-04 01:43:51"
    },
    {
        "remark": "",
        "original_name": "age",
        "name": "age",
        "data_type": "number",
        "title": "age",
        "aggregator": "",
        "selected": true,
        "field_id": "fk7d637d27",
        "real_type": null,
        "seq_no": 1,
        "uniq_index": 0,
        "fid": "fk7d637d27",
        "type": 1,
        "id": 2837,
        "ctime": "2020-01-04 01:43:51"
    },
    {
        "remark": "",
        "original_name": "age1",
        "name": "age1",
        "data_type": "number",
        "title": "age1",
        "aggregator": "",
        "selected": false,
        "field_id": "fk70e985a7",
        "real_type": null,
        "seq_no": 2,
        "uniq_index": 0,
        "fid": "fk70e985a7",
        "type": 1,
        "id": 2838,
        "ctime": "2020-01-04 01:43:51"
    },
    {
        "remark": "",
        "original_name": "date",
        "name": "date",
        "data_type": "date",
        "title": "date",
        "aggregator": "",
        "selected": true,
        "field_id": "fk5fc73231",
        "real_type": null,
        "seq_no": 3,
        "uniq_index": 0,
        "fid": "fk5fc73231",
        "type": 3,
        "id": 2839,
        "ctime": "2020-01-04 01:43:51"
    }
]
export default {
  data() {
    return { 
      fieldData: fields,
    }
  },
  methods: {
    filterSchema (data) {
      console.log('filterSchema', data)
    }
  }
}
</script>

```
