```vue
<template>
  <div>
    <a-tabs defaultActiveKey="1">
      <a-tab-pane tab="全部" key="1">
        <a-input-search placeholder="请输入进行搜索" style="width: 200px" v-model="serachTextAll" @keyup="onSearch" />
        <fieldSetting :fieldList="fieldListAll" :field_ids="field_ids" :searchText="serachTextAll"/>
      </a-tab-pane>
      <a-tab-pane tab="自定义" key="2">
        <a-input-search placeholder="请输入进行搜索" style="width: 200px" v-model="serachText" @keyup="onSearch" />
        <fieldSetting :fieldList="fieldList" :field_ids="field_ids" :searchText="serachText"/>
      </a-tab-pane>
    </a-tabs>
    
  </div>
</template>
<script>
const fieldList = [
    {
      comment: '',
      name: '人员类别汉字syrklbhz',
      data_type: 'date',
      nick_name: '人员类别汉字syrklbhz',
      formula: '',
      fid: 'fk98682415',
      type: 'dimension',
      param: {}
    },
    {
      comment: '',
      name: '身份证件号码zjhm',
      data_type: 'string',
      nick_name: '身份证件号码zjhm',
      formula: '',
      fid: 'fk0cfe4cf2',
      type: 'dimension',
      param: {}
    },
    {
      comment: '',
      name: '姓名xm',
      data_type: 'string',
      nick_name: '姓名xm',
      formula: '',
      fid: 'fk929de238',
      type: 'dimension',
      param: {}
    },
    {
      comment: '',
      name: '居住地街道jzdjddm',
      data_type: 'number',
      nick_name: '居住地街道jzdjddm',
      formula: '',
      fid: 'fk52b40d8b',
      type: 'measure',
      param: {}
    },
    {
      comment: '',
      name: '居住地街道汉字jzdjdhz',
      data_type: 'string',
      nick_name: '居住地街道汉字jzdjdhz',
      formula: '',
      fid: 'fkd5b490e8',
      type: 'dimension',
      param: {}
    }
  ]
  const fieldListAll = [
    {
      comment: '',
      name: '人员类别汉字syrklbhz',
      data_type: 'date',
      nick_name: '人员类别汉字syrklbhz',
      formula: '',
      fid: 'fk98682415',
      type: 'dimension',
      disabled: true,
      param: {}
    },
    {
      comment: '',
      name: '身份证件号码zjhm',
      data_type: 'string',
      nick_name: '身份证件号码zjhm',
      formula: '',
      fid: 'fk0cfe4cf2',
      type: 'dimension',
      disabled: true,
      param: {}
    },
    {
      comment: '',
      name: '姓名xm',
      data_type: 'string',
      nick_name: '姓名xm',
      formula: '',
      fid: 'fk929de238',
      type: 'dimension',
      disabled: true,
      param: {}
    },
    {
      comment: '',
      name: '居住地街道jzdjddm',
      data_type: 'number',
      nick_name: '居住地街道jzdjddm',
      formula: '',
      fid: 'fk52b40d8b',
      type: 'measure',
      disabled: true,
      param: {}
    },
    {
      comment: '',
      name: '居住地街道汉字jzdjdhz',
      data_type: 'string',
      nick_name: '居住地街道汉字jzdjdhz',
      formula: '',
      fid: 'fkd5b490e8',
      type: 'dimension',
      disabled: true,
      param: {}
    }
  ]
  const field_ids = ['fk0cfe4cf2']
export default{
  data() {
    return {
      fieldList: fieldList,
      fieldListAll: fieldListAll,
      field_ids: field_ids,
      serachText: '',
      serachTextAll: ''
    }
  },
  methods: {
    onSearch(val) {
      // this.serachText = val;
      console.log('searchQuery', this.serachText)
    }
  }
}
</script>
```