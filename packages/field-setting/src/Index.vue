<template>
  <div class='field-setting h-full'>
    <div class="list" v-if="fieldList.length - hiddenNum > 0">
      <ul class='h-8 leading-8'>
        <li>
            <label class='cursor-pointer'>
                <a-checkbox :indeterminate='indeterminate' :checked='isCheckAll' @change='onCheckAllChange' :disabled="fieldList.length - hiddenNum - disabledNum === 0" class='mr-1'></a-checkbox>
                全选
            </label>
        </li>
      </ul>
      <ul class='h-calc18 overflow-auto' >
        <template v-for='(tb, index) in fieldList'>
          <li :key='index' :title='tb.name' class='nowrap cursor-pointer h-8 leading-8 text-type-800' v-if="!tb.hidden">
            <label class='cursor-pointer'>
            <a-checkbox class='mr-1' @change='onChange(tb, $event)' :checked='fid.indexOf(tb.fid) > -1' :disabled="tb.disabled"></a-checkbox>
            <hz-icon :hzName="'type-'+tb.data_type" class="text-primary-900"></hz-icon> {{tb.name}}
            </label>
          </li>
        </template>
      </ul>
    </div>
    <div v-if="fieldList.length - hiddenNum === 0" class="flex h-full">
      <noDataSpace class="self-center" size="sm"/>
    </div>
  </div>
</template>
<script>
import { findIndex as _findIndex, filter as _filter, cloneDeep as _cloneDeep } from 'lodash'
import { hzIcon, noDataSpace } from '@hertz/base-components'
/**
 * @displayName fieldSetting 字段设置
 */
export default {
  components: { hzIcon, noDataSpace },
  name: 'fieldSetting',
  props: {
    /**
     * 字段列表
     */
    fieldList: {
      type: Array,
      default: () => []
    },
    /**
     *  选中的字段ID
     */
    field_ids: {
      type: Array,
      default: () => []
    },
    searchText: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isCheckAll: false,
      indeterminate: false,
      originalFid: _cloneDeep(this.field_ids),
      fid: this.field_ids,
      serachQuery: '',
      checkedShowNum: 0, // 选中并显示的
      checkedNum: 0,
      hiddenNum: 0,
      disabledNum: 0
    }
  },
  created () {
  },
  watch: {
    searchText (val) {
      this.serachQuery = val
      this.searchField()
    },
    field_ids (fieldIds) {
      this.originalFid = _cloneDeep(fieldIds)
      this.fid = fieldIds
      this.init()
    }
  },
  mounted () {
    this.init()
    this.serachQuery = this.searchText
  },
  methods: {
    /**
     * 数据初始化方法，编辑更新数值时传入data
     *
     * @public
     * @version 1.0.0
     */
    init () {
      this.handleList()
      if (this.checkedShowNum === this.fieldList.length && this.checkedShowNum > 0) {
        this.isCheckAll = true
        this.indeterminate = false
      } else if (this.checkedNum === 0) {
        this.isCheckAll = false
        this.indeterminate = false
      } else {
        this.isCheckAll = false
        this.indeterminate = true
      }
    },
    /**
     * 处理数据
     */
    handleList () {
      let num = 0
      let disableNum = 0
      this.fieldList.forEach((item) => {
        const index = _findIndex(this.originalFid, (o) => { return o === item.fid })
        item.checked = index > -1
        if (index > -1) {
          num += 1
        }
        if (item.disabled) {
          disableNum += 1
        }
      })
      this.checkedShowNum = num
      this.checkedNum = num
      this.disabledNum = disableNum
    },
    /**
     * 搜索
     */
    searchField () {
      this.hiddenNum = 0
      this.disabledNum = 0
      this.fieldList.forEach(item => {
        item.hidden = false
        if (this.serachQuery.trim().length > 0) {
          item.hidden = item.name.toLowerCase().indexOf(this.serachQuery.trim().toLowerCase()) < 0
        }
        if (item.hidden) {
          this.hiddenNum += 1
        } else {
          if (item.disabled) {
            this.disabledNum += 1
          }
        }
      })
      this.isCheckAll = this.fid.length === this.getShowField().length && this.getShowField().length > 0
      this.indeterminate = this.fid.length > 0 && this.fid.length < this.getShowField().length
    },
    /**
     * 全选框／半选框切换显示
     */
    onCheckAllChange (e) {
      this.isCheckAll = e.target.checked
      this.indeterminate = false
      // this.fid = []
      const fieldList = this.getShowField()
      fieldList.forEach((item) => {
        if (this.isCheckAll) {
          if (!item.hidden && this.fid.indexOf(item.fid) < 0 && !item.disabled) {
            item.checked = true
            this.fid.push(item.fid)
          }
        } else {
          const index = this.fid.indexOf(item.fid)
          if (!item.hidden && index > -1 && !item.disabled) {
            item.checked = false
            this.fid.splice(index, 1)
          }
        }
      })
    },

    onChange (tb, e) {
      tb.checked = !tb.checked
      const index = _findIndex(this.fid, (o) => { return o === tb.fid })
      if (tb.checked && this.fid.indexOf(tb.fid) < 0) {
        this.fid.push(tb.fid)
      } else if (!tb.checked) {
        this.fid.splice(index, 1)
      }
      console.log(this.fid)
      this.isCheckAll = this.fid.length === this.getShowField().length && this.getShowField().length > 0
      this.indeterminate = this.fid.length > 0 && this.fid.length < this.getShowField().length
    },
    getShowField () {
      let fieldList = []
      fieldList = _filter(this.fieldList, (item) => {
        return !item.hidden
      })
      return fieldList
    }
  }
}
</script>
<style lang="postcss" scoped>
/* .timed-update-container{
  background: red;
  .mb-4{
    margin-bottom: 4px;
  }
} */
</style>
