<template>
  <div class="flex-auto flex flex-col query-data-list">
    <!-- @slot 放置查询 -->
    <slot name="queryList"></slot>
    <div class="flex-1 flex flex-col">
      <!-- @slot 放置表格 -->
      <slot name="tableList" :rowSelection="rowSelection"></slot>
      <div class="flex w-full mt-2 leading-8 bg-mono-100 z-10 py-3 sticky bottom-0">
        <div
          v-show="selected.length"
          class="flex-1 pl-4 batch-btn-wrap"
        >
          <a-checkbox
            :indeterminate="selected.length > 0 && !checkAll"
            :checked="checkAll"
            @change="changeCheckAll"
            class="mr-4"
          ></a-checkbox>
          <!-- @slot 放置批量操作按钮 -->
          <slot name="batchBtn"></slot>
        </div>
        <div class="flex flex-1 items-center justify-end" v-if="(total/pageInfo.page_size > 1 || (showPage && total > 0)) && selected.length === 0">
          <span class="whitespace-nowrap"><span class="text-type-700">共 </span>{{total}}<span class="text-type-700"> 个</span></span>
          <a-pagination
            :total="total"
            @change="onPageChange"
            :current="pageInfo.page_no"
            :pageSize="pageInfo.page_size"
            @showSizeChange="onShowSizeChange"
            showSizeChanger
            showQuickJumper />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * @displayName QueryDataList 查询列表
 */
export default {
  name: 'QueryDataList',
  data () {
    return {
      checkAll: false,
      selected: []
    }
  },
  model: {
    prop: 'selectedList',
    event: 'change'
  },
  props: {
    /**
     * 选中的数据v-model(selectedList)
     * @model
     */
    selectedList: {
      type: Array,
      default: () => []
    },
    /**
     * 数据总量
     */
    total: {
      type: Number,
      default: 0
    },
    /**
     * 分页信息
     */
     pageInfo: {
      type: Object,
      default: () => {
        return {
          page_no: 1,
          page_size: 20
        }
      }
    },
    /**
     * 是否总展示分页信息
     */
    showPage: {
      type: Boolean,
      default: false
    },
    /**
     * 数据列表
     */
    dataList: {
      type: Array,
      default: () => []
    },
    /**
     * 数据列表被选中所取的key
     */
    rowKey: {
      type: String,
      default: ''
    }
  },
  computed: {
    rowSelection () {
      return {
        selectedRowKeys: this.selected,
        onChange: this.onSelectChange,
        columnWidth: '35px',
        getCheckboxProps: record => {
          return ({
            props: {
              disabled: false
            }
          })
        }
      }
    }
  },
  watch: {
    selectedList (val) {
      this.selected = val
      if (this.selected.length === this.dataList.length) {
        this.checkAll = true
      } else {
        this.checkAll = false
      }
    }
  },
  methods: {
    onSelectChange (selected) {
      this.selected = selected
      this.$emit('change', this.selected)
    },
    changeCheckAll (event) {
      if (!event.target.checked) {
        this.selected = []
      } else {
        this.selected = this.dataList.map(item => item[this.rowKey])
      }
      this.$emit('change', this.selected)
      this.checkAll = event.target.checked
    },
    onPageChange (current) {
      /**
       * 翻页
       * @type {Number}
       * @property {Number} current
       */
      this.$emit('onPageChange', current)
    },
    onShowSizeChange (current, pageSize) {
      /**
       * 改变每页显示条目数
       * @type {Number}
       * @property {Number} pageSize
       */
      this.$emit('showSizeChange', pageSize)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
