示例
```vue
<template>
  <QueryDataList
      :pageInfo="pageInfo"
      :total="totalCount"
      :dataList="dataList"
      :rowKey="rowKey"
      v-model="selectedList"
      @showSizeChange="onShowSizeChange"
      @onPageChange="onPageChange"
      >
      <template slot="queryList">
        查询组件
      </template>
      <template slot="tableList" slot-scope="tableListScope">
        表格选中的rowSelection方法可以直接使用插槽里面的tableListScope.rowSelection
      </template>
      <template slot="batchBtn">
        批量btn
      </template>
    </QueryDataList>
</template>
<script>
export default {
  data () {
    return {
      pageInfo: {
        page_no: 1,
        page_size: 20
      },
      totalCount: 0,
      selectedList: [],
      dataList: [],
      rowKey: 'id'
    }
  },
  methods: {
    onShowSizeChange (pageSize) {}
    onPageChange (page_no) {}
  }
}
</script>

```
