<template>
  <div class="h-full w-full">
    <div class="flex place-content-between h-10 border-b-1 border-solid border-mono-a-300">
      <div v-if="operatorBtn" class="flex items-center ml-10">
          <button class="mr-2" @click="undo">
            <hz-icon hzName="undo" className="mr-2 hz-action-icon"/>
          </button>
          <button @click="redo">
            <hz-icon hzName="redo" className="hz-action-icon"/>
          </button>
      </div>
      <div class="flex items-center">
        <div class="operator" v-if="showOperatorSymbol">
          <ul class="flex">
            <li v-for="(item, index) in symbolList" class="px-2 cursor-pointer" @click="insertOperatorSymbol(item)" :key="index">
              <hz-icon :hzName="item.icon" className="hz-action-icon"/>
            </li>
          </ul>
        </div>
        <a-button size="small" type="text" class="mr-2" v-if="formatBtn" @click.stop="formatScript()">
          <hz-icon className="h-4 w-4 mr-1.5 text-type-700" hzName="code-format"/>
          格式化
        </a-button>
        <a-button size="small" type="text" class="mr-2" v-if="checkBtn" @click.stop="checkScript()">
          <hz-icon className="h-4 w-4 mr-1.5 text-type-700" hzName="grammar-check"/>
          语法校验
        </a-button>
        <a-popover placement="bottomRight" trigger="click">
          <template slot="content">
            <codemirror
              class="cm-s-default"
              style="width:30rem;height:14rem"
              :value="codeHelpText"
              :options="cmOptionsTip"
            />
          </template>
          <a-button size="small" type="text" class="mr-2" v-if="helpBtn">
            <hz-icon className="h-4 w-4 mr-1.5 text-type-700" hzName="help-grammar"/>
            语法帮助
          </a-button>
        </a-popover>
      </div>
    </div>
    <codemirror
      ref="refCmEditor"
      class="cm-s-default vue-codemirror h-calc10"
      v-model="script"
      :options="cmReadonlyOptions"
      @input="onChange"
      @focus="onFocus"
    />
  </div>
</template>

<script>
/** 表达式组件
 * <AUTHOR>
 */
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/sql/sql.js'
import 'codemirror/addon/hint/show-hint.js'
import 'codemirror/addon/hint/sql-hint.js'
import 'codemirror/addon/display/placeholder.js'
import { codemirror } from 'vue-codemirror'
const codeTips = `### 语法规范 ###
示例：
普通用法：select \`A\`.\`a\` from \`A\` where \`A\`.\`a\`>'$\{c}' or /? \`A\`.\`a\` = '$\{d}' ?/
高级用法：select \`A\`.\`a\` from \`A\` where $\{c&\`A\`.\`a\`} or /? $\{d&\`A\`.\`a\`} ?/
1. A 为虚拟输入表或者标准表、主题表、关系表；a 为虚拟表中的字段；c 为参数变量；d 为可选参数变量（可选参数不支持逻辑‘or’的拼接）
2. 高级用法中的 & 为占位符
3. 普通用法中：SQL 脚本中通过 $\{参数变量} 的方式引用定义的参数，定义参数格式：'$\{参数变量}'
4. 高级用法中：定义语法格式为 $\{参数变量&字段}，高级用法中该模板不需要使用单引号，& 左侧为参数变量，右侧为字段名称
5. 当过滤条件中用到的定义配置参数为可选参数时，需要以下面这种特殊的格式：/? xxx ?/ 将条件包裹起来
6. 'TEMP'用于创建临时表，TEMP 临时表 ＝ select \`字段1\` from \`数据表A\`
### 规则须知 ###
1. SQL 脚本中用到的虚拟表，虚拟表配置中必须要有且需要一一对应上
2. 虚拟表名称不能和其它表重名
3. SQL 脚本中用到的字段，虚拟表配置中必须要有
4. 在编辑栏左侧可选取所需要的主题表、标准表、关系表；在右侧可以查看和选取已经配置好的虚拟表、字段信息及参数变量信息
5. 字段和表名使用时需要加上\`\`符号
6. 参数配置如果涉及到数值间的比较时，需要以如下格式进行代码编写：aa 为数值类型字段，示例：select \`aa\`,\`bb\` from \`A\` where /? cast(\`aa\` as double )>'$\{num}' ?/
7. 高级用法只支持 $\{参数变量&字段}，不支持UDF以及其他写法
### UDF写法 ###
1. 经纬度转地址函数：location2Address(longtitude, latitude)，longtitude为经度字段，latitude:为纬度字段。示例：location2Address(\`经度\`,\`纬度\`)
2. 地址转经度函数：GAODE_LON(address)，地址转纬度函数：GAODE_LAT(address)， address为地址字段。示例：GAODE_LON(\`家庭住址\`)，GAODE_LAT(\`家庭住址\`)`

/**
 * @displayName code-input 表达式组件
 */
export default {
  name: 'CodeInput',
  components: {
    codemirror
  },
  props: {
    configInfo: {
      type: Object,
      default () {
        return {
          script_type: 'sql',
          script: ''
        }
      }
    },
    placeholder: {
      type: String,
      default: '示例：field is null'
    },
    codeTips: {
      type: String,
      default: codeTips
    },
     /**
     * 是否展示格式化按钮
     */
    formatBtn: {
      type: Boolean,
      default: false
    },
     /**
     * 是否展示前进、后退按钮
     */
     operatorBtn: {
      type: Boolean,
      default: true
    },
    /**
     * 是否展示语法校验按钮
     */
    checkBtn: {
      type: Boolean,
      default: false
    },
    /**
     * 是否展示操作符号
     */
    showOperatorSymbol: {
      type: Boolean,
      default: false
    },
    /**
     * 是否展示语法帮助按钮
     */
    helpBtn: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      codemirrorObj: null,
      cmReadonlyOptions: {},
      cmOptionsTip: {},
      codeHelpText: '',
      cmOptions: {
        tabSize: 4,
        mode: 'text/x-mysql',
        theme: 'base16-dark',
        lineNumbers: true,
        line: true,
        indentWithTabs: true,
        smartIndent: true,
        lineWrapping: true,
        matchBrackets: true,
        autofocus: false,
        placeholder: '示例：field is null',
        extraKeys: { 'Ctrl-Space': 'autocomplete', Tab: 'autocomplete' }
      },
      script: '',
      symbolList: [
        {
          key: '+',
          icon: 'plus1'
        },
        {
          key: '-',
          icon: 'subtract',
          name: 'subtract'
        },
        {
          key: '*',
          icon: 'close',
          name: 'multiplication'
        },
        {
          key: '/',
          icon: 'slash',
          name: 'division'
        },
        {
          key: '(',
          icon: 'l-parenthesis',
          name: 'l-parenthesis'
        },
        {
          key: ')',
          icon: 'r-parenthesis',
          name: 'r-parenthesis'
        }
      ],
      focus: false
    }
  },
  created () {
    this.init()
  },
  mounted () {
    if (this.$refs.refCmEditor) {
      this.codemirrorObj = this.$refs.refCmEditor.codemirror
    }
  },
  methods: {
    init () {
      this.codeHelpText = this.codeTips
      this.initCodemirrorReadonlyOptions()
    },
    initCodemirrorReadonlyOptions () {
      this.cmReadonlyOptions = { ...this.cmOptions }
      // this.cmReadonlyOptions.readOnly = 'nocursor'
      this.cmReadonlyOptions.autofocus = false
      this.cmOptionsTip = { ...this.cmOptions }
      this.cmOptionsTip.line = false
      this.cmOptionsTip.lineNumbers = false
      this.cmOptionsTip.readOnly = 'nocursor'
      this.cmReadonlyOptions.placeholder = this.placeholder
    },
    /**
     * codemirror 插入字段、虚拟表、函数、参数等
     * data 为插入对象, 必须包含 title, 如果是添加表下所有字段需要有 fields,如: {title: 'tabname', fields: [{title: 'fieldName'}]};
     * type 为类型:
     * 字段:field  表名: tbName 表下所有字段: tbFieldAll 参数: param 函数: function
     */
    insertObj (data, type) {
      switch (type) {
        case 'tbFieldAll': { // 此处加大括号，以防eslint 报错: Unexpected lexical declaration in case block
          let text = 'select '
          const filedsName = []
          data.fields.forEach((item) => {
            filedsName.push('`' + item.title + '`')
          })
          text += filedsName.join(',') + ' from '
          text += '`' + data.title + '`'
          this.insertText(text)
          break
        }
        case 'param':
          this.insertText('${' + data.title + '&}', 1)
          break
        case 'function':
          this.insertText(data.title + '()', 1)
          break
        case 'field':
        case 'tbName':
        default:
          this.insertText('`' + data.title + '`')
          break
      }
    },
    /**
     * codemirror 插入字符串
     * text: 需要插入的字符串，必选；
     * position: 可选，光标位置 向前移动几位，int类型，默认为0
     */
    insertText (text, position = 0) {
      if (!this.codemirrorObj) {
        this.codemirrorObj = this.$refs.refCmEditor.codemirror
      }
      this.codemirrorObj.replaceSelection(text)
      this.codemirrorObj.focus()
      if (position) {
        const pos = this.codemirrorObj.getCursor()
        pos.ch = pos.ch - position
        this.codemirrorObj.setCursor(pos)
      }
    },
    insertOperatorSymbol (data) {
      this.insertText(data.key)
    },
    /**
     * 获取表达式 value方法
     *
     * @public
     * @version 1.0.0
     */
    getValue () {
      return this.$refs.refCmEditor.codemirror.getValue().trim()
    },
    /**
     * 设置表达式 value方法
     *
     * @public
     * @version 1.0.0
     */
    setValue (value) {
      return this.$refs.refCmEditor.codemirror.setValue(value)
    },
    /**
     * 清空 codemirror
     */
    clearCodemirror () {
      this.script = ''
    },
    // 重新赋值
    initInserText (text = '') {
      this.script = text
    },
    // 格式化 cm
    formatScript () {
      const value = this.getValue()
      const data = {
        status: 1,
        errMsg: '',
        result: {
          value: value
        }
      }
      if (!value) {
        data.status = 0
        data.errMsg = '请输入表达式'
      }
      this.$emit('formatScript', data)
    },
    /**
     * SQL 语法校验
     * return {
        status: 1,
        errMsg: '',
        result: {
          value: this.getValue()
        }
      }
     */
    async checkScript () {
      const data = {
        status: 1,
        errMsg: '',
        result: {
          value: this.getValue()
        }
      }
      if (!this.checkScriptParam()) {
        data.status = 0
        data.errMsg = '请输入表达式'
      }
      this.$emit('checkScript', data)
    },
    checkScriptParam () {
      const codemirror = this.$refs.refCmEditor.codemirror
      const script = codemirror.getValue()
      if (!script.trim()) {
        this.$message.warning('请输入脚本')
        return false
      }
      return true
    },
    undo () {
      this.$refs.refCmEditor.codemirror.undo()
    },
    redo () {
      this.$refs.refCmEditor.codemirror.redo()
    },
    onChange ($event) {
      if (!this.focus) return
      this.$emit('change', $event)
    },
    onFocus () {
      this.focus = true
    }
  }
}
</script>
<style lang="scss">
  .CodeMirror{
    height: 100% !important;
    @apply bg-transparent;
  }
  .CodeMirror-gutters {
    background-color: unset;
  }
  .border-b-mono-a-300 {
    @apply border-b-1 border-solid border-mono-a-100;
  }
  .CodeMirror-wrap pre.CodeMirror-line-like {
    @apply text-type-700;
  }
</style>
