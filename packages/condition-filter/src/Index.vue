<template>
  <div class="condition-filter-container w-full h-full">
    <div class="condition-head h-8">
      <span class="text-type-700 font-semibold leading-8 inline-block mr-4 h-8 align-top">满足下列</span>
      <div class="condition-select-wrap w-52 inline-block h-8 leading-8">
        <a-select class="block w-full h-8 leading-8" v-model="conditions.where_linker">
          <a-select-option v-for="linker in conditionLinker" :key="linker.value" :value="linker.value">{{linker.name}}</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="filter-list">
      <div v-for="(filter, index) in conditions.condition.filters" :key="index" class="mt-2">
        <ul class="flex">
            <li class="filter-item filter-item-tb mr-4 flex-1" v-if="mode ==='multi'">
              <a-select class="block w-full" v-model="filter.tb_id" :filterOption="filterOption" @change="onChangeTable($event,filter)" show-search placeholder="请选择，支持查询">
                <a-select-option v-for="tb in tableList" :key="tb.tb_name" :value="tb.tb_id">{{tb.tb_name}}</a-select-option>
              </a-select>
            </li>
            <li class="filter-item filter-item-field mr-4 flex-1">
              <a-select class="block w-full" v-model="filter.match_key" :filterOption="filterFieldOption" @change="onChangeField($event,filter)" show-search placeholder="请选择，支持查询">
                <a-select-option v-for="field in fieldMap[filter.tb_id] " :key="field[matchKey || 'field_title']" :value="field[matchKey]">
                  <hz-icon :hzName="'type-'+field.data_type" class="text-primary-900 mr-1"></hz-icon>{{field[matchTitle]}}
                </a-select-option>
              </a-select>
            </li>
            <li class="filter-item filter-item-operator mr-4 flex-1">
              <a-select class="block w-full" v-model="filter.operator" @change="onChangeOperator(filter)" placeholder="请选择">
                <a-select-option v-for="operator in conditionType[filter.data_type] " :key="operator.name" :value="operator.value">{{operator.name}}</a-select-option>
              </a-select>
            </li>
            <li class="filter-item filter-item-value mr-4 flex-1">
                <div v-if="[10, 17, 23].indexOf(filter.operator) === -1">
                  <a-input v-model="filter.value" @blur="changedInputValue()" :disabled="filter.operator == 8 || filter.operator == 9" :allowClear="false"/>
                </div>
                <div v-if="filter.operator === 17" class="flex">
                  <a-input-number class="flex-1" v-model="filter.values[0]" /><span class="ml-2 mr-2 mt-2">~</span><a-input-number class="flex-1" v-model="filter.values[1]" />
                </div>
                <div v-if="filter.operator == 10" class="flex">
                  <a-input readonly  v-model="filter.start_date" class="flex-1" @click="setDateVal(filter,index)" :allowClear="false" v-if="filter.start_date!=null"></a-input>
                  <a-input readonly  v-model="emptyDateText" class="flex-1" @click="setDateVal(filter,index)" :allowClear="false" v-else></a-input>
                  <span class="ml-2 mr-2 mt-2">~</span>
                  <a-input readonly  v-model="filter.end_date" class="flex-1" @click="setDateVal(filter,index)" :allowClear="false" v-if="filter.end_date!=null"></a-input>
                  <a-input readonly  v-model="emptyDateText" class="flex-1" @click="setDateVal(filter,index)" :allowClear="false" v-else></a-input>
                </div>
                <div v-if="filter.operator === 23">
                  <a-select v-model="filter.relativeKey" @change="onChangeRelative($event, filter, index)" placeholder="请选择" class="w-full">
                    <a-select-option v-for="item in relativeTimeOptions" :key="item.key" :value="item.key">{{ item.name }}</a-select-option>
                  </a-select>
                </div>
            </li>
            <div class="inline-block w-12">
              <hz-icon hzName="plus1" v-if="index == 0" @click="handleFilter()" bgShape="square" className="hz-action-icon" class="mt-2 mr-3" />
              <hz-icon hzName="subtract" @click="deleteFilter(index)" bgShape="square" className="hz-action-icon" class="mt-2"/>
            </div>
        </ul>
      </div>
    </div>
    <dateRangeModal ref="dateRangeModal" v-if="showDateModal" @hideModal="showDateModal=false" @saveDate="selectDateRange($event)"/>
  </div>
</template>
<script>
/** 条件组件
 * <AUTHOR>
 */
import { conditionLinker, dataTypeMap, conditionType, seniorStringOperators, seniorNumberOperators, seniorDateOperators, relativeTime } from './data.interface'
import { find as _find, each as _each, filter as _filter, cloneDeep as _cloneDeep } from 'lodash'
import dateRangeModal from './DateRangeModal.vue'
import moment from 'moment'
import { hzIcon } from '@hertz/base-components'
/**
 * @displayName conditionFilter 条件过滤
 */
export default {
  name: 'conditionFilter',
  components: {
    dateRangeModal,
    hzIcon
  },
  props: {
    /**
     * 单表还是多表
     * @values 'multi','single'
     */
    mode: {
      type: String,
      default: 'single'
    },
    /**
     * 表数据，结构如下：
     * [{tb_id:'**',tb_name: '**',fields:[{field_title: '**',field_id:'**',data_type:'**'}]}]
     */
    tableListInfo: {
      type: Array,
      default: () => []
    },
    /**
     * 匹配规则，按字段id或者名字匹配
     * @values 'field_id'，'field_title'
     */
    matchKey: {
      type: String,
      default: 'field_id'
    },
    /**
     * 匹配规则，按字段title
     * @values 'field_id'，'field_title'
     */
    matchTitle: {
      type: String,
      default: 'field_title'
    },

    /**
     * 过滤初始数据，结构如默认值
     * where_linker: and 全部条件，or任意条件，filters结构按例子
     */
    conditionData: {
      type: Object,
      default: () => {
        return {
          where_linker: 'and',
          condition: {
            filters: []
          }
        }
      }
    },
    /**
     * 字符串类型操作符
     * @values 'base', 'senior'
     */
    operatorString: {
      type: String,
      default: 'base'
    },
    /**
     * 数值类型操作符
     * @values 'base', 'senior'
     */
    operatorNumber: {
      type: String,
      default: 'base'
    },
    /**
     * 日期类型操作符
     * @values 'base', 'senior'
     */
    operatorDate: {
      type: String,
      default: 'base'
    }
  },
  data () {
    return {
      conditionLinker: conditionLinker,
      dataTypeMap: dataTypeMap,
      tableList: [],
      fieldMap: {},
      conditionType: {},
      showDateModal: false,
      emptyDateText: '不限',
      currentIndex: 0,
      relativeTimeOptions: [],
      conditions: {
        where_linker: 'and',
        condition: {
          filters: []
        }
      },
      emptyFilterInit: false
    }
  },
  watch: {
    tableListInfo: {
      handler (val) {
        this.tableList = []
        const fieldMap = this.initTableList(val)
        this.fieldMap = fieldMap
        this.conditions = _cloneDeep(this.conditionData)
        this.initFilterData()
      },
      deep: true
    }
  },
  created () {
    this.initOperatorList()
  },
  mounted () {
    this.conditions = this.conditionData
    this.tableList = []
    const fieldMap = this.initTableList(this.tableListInfo)
    this.fieldMap = fieldMap
    this.initFilterData()
  },
  methods: {
    initOperatorList () {
      this.relativeTimeOptions = [...relativeTime]
      this.conditionType = { ...conditionType }
      if (this.operatorString === 'senior') {
        this.conditionType.string = this.conditionType.string.concat(seniorStringOperators)
      }
      if (this.operatorNumber === 'senior') {
        this.conditionType.number = this.conditionType.number.concat(seniorNumberOperators)
        this.conditionType.double = this.conditionType.double.concat(seniorNumberOperators)
        this.conditionType.int = this.conditionType.int.concat(seniorNumberOperators)
      }
      if (this.operatorDate === 'senior') {
        this.conditionType.date = this.conditionType.date.concat(seniorDateOperators)
        this.conditionType.datetime = this.conditionType.datetime.concat(seniorDateOperators)
      }
    },
    initTableList (tables) {
      const fieldMap = {}
      _each(tables, table => {
        const tb = {
          tb_id: table.tb_id || '',
          tb_name: table.tb_name || ''
        }
        this.tableList.push(tb)
        fieldMap[tb.tb_id] = table.fields
      })
      return fieldMap
    },
    // 将输入的内容与显示的内容进行匹配
    filterOption (value, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(value.toLowerCase()) >= 0
    },
    filterFieldOption (value, option) {
      return option.componentOptions.children[1].text.toLowerCase().indexOf(value.toLowerCase()) >= 0
    },
    initFilterData () {
      const filters = this.conditions.condition.filters
      if (filters && filters.length > 0) {
        _each(filters, filterItem => {
          this.$set(filterItem, 'match_key', filterItem[this.matchKey])
          if (filterItem.operator === 23) {
            const relativeItem = relativeTime.find((it) => it.value === Number(filterItem.relative) && it.granularity === filterItem.granularity)
            filterItem.relativeKey = relativeItem.key
            this.$set(filterItem, 'relativeKey', relativeItem.key)
          }
        })
      }
      if (filters.length === 0 || !this.emptyFilterInit) {
        this.emptyFilterInit = true
        this.addEmptyFilter()
      }
      if (this.mode === 'single' && filters.length === 1 && !filters[0].tb_id) {
        filters[0].tb_id = this.tableList[0] ? this.tableList[0].tb_id : undefined
      }
    },
    onChangeTable (tbId, filter) {
      const tb = _filter(this.tableList, (tb) => { return tb.tb_id === tbId })[0]
      filter.tb_name = tb.tb_name
      const fields = this.fieldMap[tbId]
      let field
      if (fields.length > 0) {
        field = fields[0]
      } else {
        // 字段为空以后在考虑
        // filter.data_type = 'string'
        // filter.field_title = ''
      }
      // 相应类型默认选中第一个操作符
      this.initDefaultOperator(filter, field)
    },
    initDefaultOperator (filter, field) {
      const defaultOperator = this.conditionType[field.data_type][0]
      filter.operator = defaultOperator ? defaultOperator.value : 0
      this.onChangeOperator(filter, field)
    },
    onChangeField (matchKey, filter) {
      let field
      if (this.matchKey === 'field_id') {
        field = _find(this.fieldMap[filter.tb_id], { field_id: matchKey })
      } else {
        const param = {}
        param[this.matchKey] = matchKey
        field = _find(this.fieldMap[filter.tb_id], param)
      }
      // 相应类型默认选中第一个操作符
      this.initDefaultOperator(filter, field)
      this.$forceUpdate()
      this.onChanged()
    },
    onChangeOperator (filter, field) {
      if (field) {
        filter.data_type = field.data_type
        filter.field_title = field[this.matchTitle]
        filter.field_id = field.field_id
      }
      filter.operator_name = this.conditionType[filter.data_type].find((item) => item.value === filter.operator).name
      filter.value = ''
      filter.values = [undefined, undefined]
      filter.start_date = filter.data_type === 'date' ? moment().format('YYYY-MM-DD') + ' 00:00:00' : ''
      filter.end_date = filter.data_type === 'date' ? moment().format('YYYY-MM-DD') + ' 23:00:00' : ''
      filter.match_key = filter[this.matchKey]
      filter.granularity = ''
      filter.relativeKey = undefined
      filter.relative = undefined
      this.onChanged()
    },
    onChangeRelative ($event, filter, index) {
      const option = this.relativeTimeOptions.find((item) => item.key === $event)
      filter.granularity = option.granularity
      filter.relative = option.value
      this.$set(this.conditions.condition.filters, index, filter)
      this.onChanged()
    },
    changedInputValue () {
      this.onChanged()
    },
    onChanged () {
      this.$emit('changed', this.getParamData())
    },
    handleFilter () {
      if (!this.checkFilterNull(this.conditions.condition.filters[0], true)) return
      this.addEmptyFilter()
    },
    // 检查过滤条件
    checkFilterNull (filter, showTooltip) {
      const tipContent = '所有筛选项不能为空'
      if (!filter) {
        showTooltip && this.$message.warning(tipContent)
        return false
      }

      if (filter.operator === undefined) {
        showTooltip && this.$message.warning(tipContent)
        return false
      }
      // 为空不为空或者日期范围
      if (filter.operator === 8 || filter.operator === 9) return true

      if (filter.operator === 10 && !filter.start_date && !filter.end_date) {
        showTooltip && this.$message.warning('选项不能都为空')
        return false
      }
      if (filter.operator === 23 && !filter.relative) {
        showTooltip && this.$message.warning('选项不能都为空')
        return false
      }
      if (filter.operator === 17 && (!filter.values[0] || !filter.values[1])) {
        showTooltip && this.$message.warning('选项不能都为空')
        return false
      }

      if (filter.operator < 8 && filter.value === '') {
        showTooltip && this.$message.warning(tipContent)
        return false
      }
      return true
    },
    addEmptyFilter () {
      this.conditions.condition.filters.splice(0, 0, {
        tb_id: this.mode === 'single' ? this.tableList[0] ? this.tableList[0].tb_id : undefined : undefined,
        operator: undefined,
        value: '',
        field_title: '',
        operator_name: '',
        field_id: ''
      })
    },
    // 删除过滤条件
    deleteFilter (index) {
      this.conditions.condition.filters.splice(index, 1)
      if (this.conditions.condition.filters.length === 0) {
        this.addEmptyFilter()
      }
      this.onChanged()
    },
    setDateVal (filter, index) {
      this.showDateModal = true
      this.currentIndex = index
      this.$nextTick(() => {
        this.$refs.dateRangeModal.open({
          start_date: filter.start_date,
          end_date: filter.end_date
        })
      })
    },
    selectDateRange (date) {
      const filters = this.conditions.condition.filters
      filters[this.currentIndex].start_date = date.start_date
      filters[this.currentIndex].end_date = date.end_date
      this.onChanged()
    },
    destroy () {
      this.$destroy(true)
      if (this.$el && this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el)
      }
    },
    checkAllFilter () {
      let success = true
      let currentIndex = -1
      const filters = _cloneDeep(this.conditions.condition.filters)
      for (let index = 0; index < filters.length; index++) {
        const filter = filters[index]
        filter.fid = filter[this.matchKey]
        filter.field_name = filter.field_title
        filter.fid_name = filter.field_title
        filter.where_link = this.conditions.where_linker
        currentIndex++
        success = this.checkFilterNull(filter, currentIndex > 0)
        if (currentIndex === 0 && !success) {
          filters.splice(0, 1)
          index--
          continue
        }
        if (currentIndex > 0 && !success) break
      }
      return {
        success: success,
        conditionData: {
          where_linker: this.conditions.where_linker,
          condition: {
            filters: filters
          }
        }
      }
    },
    /**
     * 获取操作后数据
     *
     * @public
     */
    getParamData () {
      const data = this.checkAllFilter()
      return data
    }
  },
  destroyed () {
    this.destroy()
  }
}

</script>
<style scoped>
</style>
