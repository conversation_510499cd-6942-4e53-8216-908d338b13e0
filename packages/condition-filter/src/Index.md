示例
```vue
<template>
<div>
  <conditionFilter :conditionData="filterData" :tableListInfo="tableListInfo" mode="multi" matchKey="field_title" ref="conditionFilter"/>
  <button @click="getFilterData" class="mt-4 font-semibold w-20 h-8 leading-8 bg-primary-a-100 text-primary-900 cursor-pointer">过滤</button>
</div>
</template>
<script>
const tableListInfo =  [
  {
    tb_id: 'tb_c17f9e981c94492fbe312613f6fc4a52', 
    tb_name: '上网信息', 
    fields:[
      {field_title: "上网终端机器名", field_id: "fka416a311",data_type:'string'},
      {field_title: "户籍地", field_id: "fka416a312",data_type:'string'},
      {field_title: "ID", field_id: "fka416a315",data_type:'string'},
      {field_title: "上网时长", field_id: "fka416a313",data_type:'date'}
    ]
  },
  {
    tb_id: 'tb_c17f9e981c94492fbe312613f6fc4a51', 
    tb_name: '结果表', 
    fields:[
      {field_title: "key1", field_id: "fka416a301",data_type:'string'},
      {field_title: "key2", field_id: "fka416a302",data_type:'string'},
      {field_title: "key3", field_id: "fka416a303",data_type:'date'}
    ]
  }
]

const filterData = {
  where_type:"condition",
  where_linker:"or",
  condition:{
    filters:[
      {
        "type":"string",
        "value":"9",
        "operator":6,
        "start_date":"",
        "end_date":"",
        "field_title":"上网终端机器名",
        "operator_name":"包含",
        "field_id":"fka416a311",
        "data_type":"string",
        "tb_id":"tb_c17f9e981c94492fbe312613f6fc4a52",
        "tb_name":"上网信息"
      }
    ]
  }
}
export default {
  data() {
    return { 
      filterData: filterData,
      tableListInfo: tableListInfo
    }
  },
  methods: {
    getFilterData() {
      console.log(this.$refs.conditionFilter.getParamData())
    }
  }
}
</script>
```

示例2-高级过滤
```vue
<template>
<div>
  <conditionFilter :conditionData="filterData" operatorString="senior" operatorNumber="senior" operatorDate="senior" :tableListInfo="tableListInfo" mode="single" matchKey="field_title" ref="conditionFilter"/>
  <button @click="getFilterData" class="mt-4 font-semibold w-20 h-8 leading-8 bg-primary-a-100 text-primary-900 cursor-pointer">过滤</button>
</div>
</template>
<script>
const tableListInfo =  [
  {
    tb_id: 'tb_c17f9e981c94492fbe312613f6fc4a52', 
    tb_name: '上网信息', 
    fields:[
      {field_title: "上网终端机器名", field_id: "fka416a311",data_type:'string'},
      {field_title: "户籍地", field_id: "fka416a312",data_type:'string'},
      {field_title: "ID", field_id: "fka416a315",data_type:'number'},
      {field_title: "上网时长", field_id: "fka416a313",data_type:'date'}
    ]
  }
]

const filterData = {
  where_type:"condition",
  where_linker:"or",
  condition:{
    filters:[
      {
        "type":"string",
        "value":"9",
        "operator":6,
        "start_date":"",
        "end_date":"",
        "field_title":"上网终端机器名",
        "operator_name":"包含",
        "field_id":"fka416a311",
        "data_type":"string",
        "tb_id":"tb_c17f9e981c94492fbe312613f6fc4a52",
        "tb_name":"上网信息"
      }
    ]
  }
}
export default {
  data() {
    return { 
      filterData: filterData,
      tableListInfo: tableListInfo
    }
  },
  methods: {
    getFilterData() {
      console.log(this.$refs.conditionFilter.getParamData())
    }
  }
}
</script>
```


