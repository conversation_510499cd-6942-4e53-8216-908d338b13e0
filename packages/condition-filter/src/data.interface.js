export const conditionLinker = [
  {
    name: '全部条件',
    value: 'and'
  }, {
    name: '任意条件',
    value: 'or'
  }
]

export const dataTypeMap = {
  1: 'number',
  2: 'string',
  3: 'date',
  4: 'blob'
}
export const stringOperators = [
  { name: '等于', value: 0, selected: true },
  { name: '不等于', value: 1 },
  { name: '包含', value: 6 },
  { name: '不包含', value: 7 },
  { name: '为空', value: 8 },
  { name: '不为空', value: 9 }
]
export const seniorStringOperators = [
  { name: '头匹配', value: 13 },
  { name: '尾匹配', value: 14 },
  { name: '开头非', value: 15 },
  { name: '结尾非', value: 16 }
]
export const numberOperators = [
  { name: '等于', value: 0, selected: true },
  { name: '不等于', value: 1 },
  { name: '大于', value: 2 },
  { name: '小于', value: 3 },
  { name: '大于等于', value: 4 },
  { name: '小于等于', value: 5 },
  { name: '为空', value: 8 },
  { name: '不为空', value: 9 }
]
export const seniorNumberOperators = [
  { name: '介于', value: 17 }
]
export const dateOperators = [
  { name: '选择日期范围', value: 10, selected: true },
  ...numberOperators
]
export const seniorDateOperators = [
  { name: '相对时间', value: 23 }
]
export const relativeTime = [
  { key: 1, value: 3, name: '最近3天', granularity: 'day' },
  { key: 2, value: 7, name: '最近7天', granularity: 'day' },
  { key: 3, value: 1, name: '最近一个月', granularity: 'month' },
  { key: 4, value: 3, name: '最近三个月', granularity: 'month' },
  { key: 5, value: -1, name: '最近半年', granularity: 'year' },
  { key: 6, value: 1, name: '最近一年', granularity: 'year' }
]
export const conditionType = {
  string: [...stringOperators],
  double: [...numberOperators],
  number: [...numberOperators],
  int: [...numberOperators],
  date: [...dateOperators],
  blob: [...stringOperators],
  datetime: [...dateOperators]
}
