<template>
  <a-modal v-model="visible" title="选择日期范围" @cancel="cancel" @ok="ok">
    <ul>
      <li>
        <label class="text-type-600 font-semibold leading-8 h-8 block mb-1">开始日期</label>
        <dateRange :date="start_date" @changeVal="onChangeVal('start_date',$event)"/>
      </li>
      <li class="mt-4">
        <label class="text-type-600 font-semibold leading-8 h-8 block mb-1">结束日期</label>
        <div class="date-value-container">
          <dateRange :date="end_date" @changeVal="onChangeVal('end_date',$event)"/>
        </div>
      </li>
    </ul>
  </a-modal>
</template>
<script>
import dateRange from '@hertz/date-range'
export default {
  name: 'dateRangeModal',
  components: { dateRange },
  data () {
    return {
      visible: false,
      start_date: '',
      end_date: ''
    }
  },
  beforeMount () {},
  created () {},
  mounted () {
  },
  computed: {},
  methods: {
    open (date) {
      this.visible = true
      this.start_date = date.start_date
      this.end_date = date.end_date
    },
    cancel () {
      this.visible = false
      this.$emit('hideModal')
    },
    ok () {
      if (this.start_date === null && this.end_date === null) {
        this.$message.warning('请至少选择一个时间')
        return
      }
      this.visible = false
      this.$emit('hideModal')
      this.$emit('saveDate', { start_date: this.start_date, end_date: this.end_date })
    },
    onChangeVal (type, dateTime) {
      this[type] = dateTime
    }
  }
}

</script>
<style scoped>
</style>
