<template>
  <div class="w-full h-10 flex items-center mb-0.5 hover:bg-mono-a-100 rounded-md px-4 cursor-pointer item">
    <div class="flex items-center flex-1">
      <hz-icon :hzName="itemIcon" class="mr-1"/>
      <span class="flex-1 whitespace-nowrap overflow-hidden overflow-ellipsis text-type-900 text-sm title" :title="title">
        {{ title }}
      </span>
    </div>
    <div class="right">
       <!-- @slot 右侧内容，比如操作按钮 -->
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
/**
 * @displayName ListItemSelect 列表单项选中
 */
export default {
  name: 'ListItemSelect',
  props: {
    /**
     * 项目名称
     */
     title: {
      type: String,
      default: '数据表1'
    },
    /**
     * 项目icon,默认值为普通表icon
     */
     itemIcon: {
      type: String,
      default: 't-normal'
    }
  }
}
</script>

<style scoped>
  .active {
    @apply bg-primary-a-100 font-semibold relative;
  }
  .active .title {
    @apply text-primary-900 font-semibold;
  }
  .active::after {
    @apply absolute left-0 w-0.5 h-2 bg-primary-900 mt-0.5;
    content: '';
    border-radius: 0 3px 3px 0;
  }
  .right {
    @apply hidden;
  }
  .item:hover .right {
    @apply block;
  }

</style>
