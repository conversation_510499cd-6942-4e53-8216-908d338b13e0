<template>
  <div class="vertail-step overflow-y-auto block box-border m-0 p-0 ">
    <div class=" steps-item mt-5 block overflow-visible relative align-top" :class="{'waitting h-7': item.status === 1}"  v-for="(item, index) in steplistReverse" :key="index">
      <div class="steps-item-container outline-none" :class="{'hidden': item.status == 1}">
        <div class="steps-item-tail block absolute top-0 left-3 pt-7" :class="{'hidden': index === steplistReverse.length-1}"></div>
        <div class="steps-item-icon" :class="{'border-0': item.status ==9999}">
          <div class="shadow-border" v-if="item.status == 1"></div>
        </div>
        <div class="steps-item-content inline-block align-top" v-if="item.status !==9999">
          <div class="relative block pl-4 pr-4 text-type-800 w-full pb-0.5 text-xs font-semibold" :class="{'overflow-x-hidden': index == steplistReverse.length-1 }"><span class="w-18.5 inline-block">{{ item.approve_time ? item.approve_time.split(' ')[0] : '' }}</span> <span class="ml-4 absolute -top-0.5">{{ stepStatus[item.status] }}</span></div>
          <div class="text-type-800 inline-flex whitespace-normal leading-5 h-5 pl-4 pr-4"><span class="inline-block w-18.5">{{ item.approve_time ? item.approve_time.split(' ')[1] : ''}}</span><span class="ml-4 w-16 truncate inline-block" :title="item.name"> {{ item.name }} </span> <span class="w-72 ml-6 truncate inline-block" :title="item.comment">{{ item.comment }}</span></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/** 纵向审批流组件
 * <AUTHOR>
 */
/**
 * @displayName stepVertical 纵向审批流
 */
export default {
  name: 'stepVertical',
  props: {
    /**
     * 表数据，结构如下：
     * [steplistReverse:[{status: '**',comment: '**',user_id: '**',name: '**',approve_time: '**'}]]
     */
    steplistReverse: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      stepStatus: ['提交审批', '审批中', '审批通过', '审批驳回', '已撤回']
    }
  }
}
</script>
<style scoped>
.steps-item-content{
  @apply -mt-1.5;
}
.steps-item:last-child {
  @apply flex-none;
}

.steps-item:not(last-child) {
  @apply  mr-4;
}

.steps-item:not(first-child) {
  @apply pl-1;
}
/* 基础圆设置 */
.steps-item-icon{
  @apply border-5 border-solid text-primary-800 w-2.5 h-2.5 rounded-full inline-block align-middle ml-px;
}
.border-0{
  border: 0 !important;
}

/* 竖线 */
.steps-item-tail::after {
  @apply absolute w-0.5 block bg-primary-800 top-3;
  left: -3px;
  height: 180%;
  content: '';
}
.steps-item-tail::after:last-child{
  @apply w-0 hidden;
}
.steps-item-tail:last-child {
  @apply w-0 h-0 hidden;
}

/* 竖线多出距离 */
.waitting::after {
  @apply absolute top-1 block w-0.5 bg-primary-800;
  left: 9px;
  height: 180%;
  content: '';
}

/* 圆设置 */
.shadow-border{
  @apply w-4.5 h-4.5 rounded-full top-px absolute left-px bg-primary-a-300;
}
</style>
