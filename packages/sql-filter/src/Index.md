示例
```vue
<template>
   <sqlFilter
      :tableListInfo="tableListInfo"
      @format="onFormat($event)"
      :sqlFormatInfo="sqlFormatInfo"
      :sql="sql"
      :formulaList="formulaList"
      @translate="onTranslate($event)"/>
</template>
<script>
const tableListInfo =  [
  {
    tb_id: 'tb_c17f9e981c94492fbe312613f6fc4a52', 
    tb_name: '上网信息', 
    fields:[
      {field_title: "上网终端机器名", field_id: "fka416a311",data_type:'string'},
      {field_title: "户籍地", field_id: "fka416a312",data_type:'string'},
      {field_title: "上网时长", field_id: "fka416a313",data_type:'date'}
    ]
  }
]
const formulaList = [
  {
    usage: 'AVG(表达式/数值字段)',
    demo: 'AVG(销售额)，返回\'销售额\'字段对应的所有非空值的平均值',
    name: 'AVG',
    desc: '返回表达式或数值字段所有值的平均值，只适用于数值字段，空值不会计算'
  },
  {
    usage: 'BOL(日期字段, 字段)',
    demo: 'BOL([统计日期], [库存])，返回统计日期中起始日期的库存值',
    name: 'BOL',
    desc: '期初计算，返回日期字段中起始时间的字段值，日期字段的起始时间受筛选器影响。'
  }
]
export default {
  data() {
    return { 
      tableListInfo: tableListInfo,
      sql: [
        {
          "tb_id":"tb_c17f9e981c94492fbe312613f6fc4a52",
          "expression":"上网终端机器名=122"
        }
      ],
      sqlFormatInfo: {
        tb_id: '',
        code: ''
      },
      formulaList: formulaList
    }
  },
  methods: {
    onFormat(info) {
      this.sqlFormatInfo = {
        tb_id: info.tb_id,
        sql: info.sql + Math.random()
      }
    },
    onTranslate(sql) {
      console.log(sql)
    }
  }
}
</script>

```
