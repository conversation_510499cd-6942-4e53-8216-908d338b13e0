<template>
  <a-modal v-model="visible" title="字段设置" @cancel="cancel" @ok="ok" dialogClass="h-modal">
    <selectVisibleField :fieldList="fieldList" :checkedIds="checkedIds" :selectFieldMode="selectFieldMode" ref="selectVisibleField"></selectVisibleField>
  </a-modal>
</template>
<script>
import selectVisibleField from '@hertz/select-visible-field'
export default {
  name: 'selectFieldModal',
  components: {
    selectVisibleField
  },
  data () {
    return {
      visible: false,
      fieldList: [],
      checkedIds: [],
      selectFieldMode: '',
      tbId: ''
    }
  },
  computed: {},
  methods: {
    open (data) {
      this.visible = true
      this.fieldList = data.field_list
      this.checkedIds = data.checked_ids
      this.selectFieldMode = data.select_field_mode
      this.tbId = data.tb_id
    },
    cancel () {
      this.visible = false
      this.$emit('hideModal')
    },
    ok () {
      if (this.selectFieldMode === '2' && this.$refs.selectVisibleField.fieldIds.length === 0) {
        this.$message.warning('请至少选择一个字段')
        return
      }
      this.visible = false
      this.$emit('hideModal')
      this.$emit('saveData', {
        checked_ids: this.$refs.selectVisibleField.fieldIds,
        select_field_mode: this.$refs.selectVisibleField.selectValue,
        tb_id: this.tbId
      })
    }
  }
}

</script>
<style scoped>
::v-deep .h-modal {
  @apply h-80
}
::v-deep .ant-modal-content {
  @apply h-full
}
::v-deep .ant-modal-body {
  @apply h-calc22
}
</style>
