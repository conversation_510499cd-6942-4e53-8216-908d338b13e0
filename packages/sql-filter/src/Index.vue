<template>
  <div class="sql-filter-container">
    <article class="left relative flex flex-col">
      <p class="height-32 text-type-700 font-semibold pl-2">字段名称</p>
      <ul class="flex-1 flex flex-col" :class="{'overflow-hidden': mode === 'single', 'overflow-auto' : mode === 'multi'}">
        <li class="rounded-lg leading-8 flex flex-col" :class="{'flex-1': table.show, 'overflow-hidden': mode  ==='single', 'h-8 flex-shrink-0': !table.show, 'bg-mono-200 hover:bg-mono-200': activeTb === table.tb_id && mode === 'multi', 'mb-2': mode === 'multi'}"
          v-for="(table,tableIndex) in tableList" :key="table.tb_id">
          <p class="nowrap height-32 px-2 cursor-pointer flex" @click="swichTable(table, tableIndex)" v-if="mode === 'multi'">
            <hz-icon :hzName="table.show ? 'triangle' : 'slicenav-arrow'" class="mt-2"/>
            <span class="nowrap flex-1" :title="table.tb_name">{{table.tb_name}}</span>
            <a class="w-10 text-primary-900 cursor-pointer mr-5" @click="openSelectFieldModal($event,table,tableIndex)" v-if="table.select_field_mode">{{table.select_field_mode === '1' ? '全部' : '自定义'}}</a>
          </p>
          <template v-if="table.show">
            <div :class="[mode === 'single' ? 'px-2' : 'px-6']" class="my-1" >
              <a-input placeholder="请输入字段搜索" v-model="table.query_text" @search="searchText(table)" @change="searchText(table)">
                <template #prefix>
                  <hz-icon hzName="search" />
                </template>
              </a-input>
            </div>
            <div class="flex-1" :class="{'overflow-auto': mode === 'single'}">
              <ul class="field-list">
                <template v-for="(field,index) in table.search_fields">
                  <li class="field-item nowrap hover:bg-mono-a-100" :key="index" v-if="field.field_title.indexOf(table.query_text ? table.query_text : '') > -1">
                    <p class="height-32 text-type-800 cursor-pointer flex" :class="[mode === 'single' ? 'pl-2' : 'pl-6']" :title="field.field_title">
                      <hz-icon :hzName="'type-'+field.data_type" class="text-primary-900 mr-1 mt-2"></hz-icon>
                      <span class="block flex-1 nowrap mr-2" :title="field.field_title">{{field.field_title}}</span>
                      <hz-icon hzName="insert-field" class="insert-field-icon mr-4" @click="insert(field.field_title,'field')"/>
                    </p>
                  </li>
                </template>
              </ul>
            </div>
          </template>
        </li>
        <HzLoading :showLoading="loading"></HzLoading>
      </ul>
    </article>
    <article class="center">
      <div class="h-8 leading-8">
        <div class="float-left">
          <button class="mr-1" @click="undo">
            <hz-icon hzName="undo" className="mr-2 hz-action-icon"/>
          </button>
          <button @click="redo">
            <hz-icon hzName="redo" className="hz-action-icon"/>
          </button>
        </div>
        <div class="float-right h-full">
          <button @click="sqlFormat" class="mr-4 text-type-800 hover:bg-mono-200 px-2 rounded-md">
            <hz-icon hzName="code-format" class="mr-1"/>格式化
          </button>
          <button @click="sqlTrans" class="text-type-800 hover:bg-mono-200 px-2 rounded-md" >
            <hz-icon hzName="grammar-check" class="mr-1"/>语法校验
          </button>
        </div>
      </div>
      <template v-for="table in tableList">
        <codemirror
          v-if="table.tb_id === activeTb"
          :ref="table.tb_id"
          :key="table.tb_id"
          v-model="sqlMap[table.tb_id]"
          :options="cmOptions"
          :placeholder="placeholder"
        />
      </template>
    </article>
    <article class="right">
      <span class="text-type-700 font-semibold h-8 leading-8 pl-2">函数</span>
      <ul class="formula-list h-calc8 overflow-auto">
        <li v-for="(formula,index) in formulaList" :key="index" class="formula-item">
          <FormulaInfo :formulaInfo="formula" @insertFormula="insertFormula"/>
        </li>
      </ul>
    </article>
    <selectFieldModal ref="selectFieldModal" v-if="showModal" @hideModal="showModal=false" @saveData="getFieldMode"/>
  </div>
</template>
<script>
// import 'codemirror/mode/sql/sql.js'
// import '@hertz/utils/src/codemirror/x-bdp-sql.js'
import 'codemirror/lib/codemirror.css'
import 'codemirror/addon/display/placeholder.js'
import { codemirror, CodeMirror } from 'vue-codemirror'
import { hzIcon, HzLoading } from '@hertz/base-components'
import { each as _each, includes as _includes, filter as _filter, findIndex as _findIndex } from 'lodash'
import FormulaInfo from './Formula.vue'
import selectFieldModal from './SelectFieldModal.vue'
import { setModeXBdpSql } from '@hertz/utils/src/codemirror/x-bdp-sql.js'
setModeXBdpSql(CodeMirror)
/**
 * @displayName sqlFilter 表达式组件
 */
export default {
  name: 'sqlFilter',
  components: {
    codemirror,
    FormulaInfo,
    hzIcon,
    selectFieldModal,
    HzLoading
  },
  props: {
    /**
     * 单表还是多表
     * @values 'multi','single'
     */
    mode: {
      type: String,
      default: 'single'
    },
    /**
     * 表数据，结构如下：
     * [{tb_id:'**',tb_name: '**',fields:[{field_title: '**',field_id:'**',data_type:'**'}],select_field_mode:'',checked_ids:[]}]
     */
    tableListInfo: {
      type: Array,
      default: () => []
    },
    /**
     * 过滤初始数据
     * * [{tb_id:'**',expression: '**']}]
     */
    sql: {
      type: Array,
      default: () => []
    },
    /**
     * 格式化sql值
     */
    sqlFormatInfo: {
      type: Object,
      default: () => {
        return {
          tb_id: '',
          sql: ''
        }
      }
    },
    /**
     * sql过滤函数列表,格式参考例子
     */
    formulaList: {
      type: Array,
      default: () => []
    },
    /**
     * 异步加载字段，在选择表或者设置显示字段的时候
     * return promise
     */
    loadFields: {
      type: Function,
      default: () => undefined
    },
    /**
     *  显示是否是反撇号anti(``), 还是square([])
     */
    bracket: {
      type: String,
      default: 'anti'
    }
  },
  data () {
    return {
      code: '',
      placeholder: `请输入SQL语句筛选数据,如${this.bracket === 'anti' ? '`' : '['}城市${this.bracket === 'anti' ? '`' : ']'}='北京'and ${this.bracket === 'anti' ? '`' : '['}部门${this.bracket === 'anti' ? '`' : ']'}='销售部'。请注意，需要使用英文输入法。字段、符号之间需加空格。`,
      cmOptions: {
        tabSize: 4,
        styleActiveLine: true,
        lineNumbers: false,
        line: true,
        mode: 'text/x-bdp-sql',
        // mode: 'text/x-mysql',
        autofocus: true,
        lineWrapping: true,
        extraKeys: { 'Ctrl-Space': 'autocomplete' }
      },
      tableList: [],
      sqlMap: {},
      activeTb: '',
      showModal: false,
      loading: false
    }
  },
  watch: {
    sqlFormatInfo: {
      handler (sqlInfo) {
        const cminstance = this.$refs[this.activeTb][0].cminstance
        cminstance.setValue(sqlInfo.sql)
      },
      deep: true
    },
    /** 添加表的时候触发 */
    tableListInfo: {
      handler (tableInfo) {
        this.tableList = []
        this.initTableList(this.tableListInfo)
      },
      deep: true
    },
    sql: {
      handler (val) {
        this.sqlMap = {}
        this.initSqlData()
      },
      deep: true
    }
  },
  mounted () {
    this.tableList = []
    this.sqlMap = {}
    if (this.tableListInfo.length === 0) return
    this.initTableList(this.tableListInfo)
    this.initSqlData()
    this.activeTb = this.tableList.length > 0 ? this.tableList[0].tb_id : ''
  },
  methods: {
    async initTableList (tables) {
      let activeIndex = _findIndex(tables, table => table.tb_id === this.activeTb)
      const tbIds = []
      _each(tables, (table, index) => {
        const tb = {
          tb_id: table.tb_id || '',
          tb_name: table.tb_name || '',
          fields: table.fields || null,
          show: activeIndex <= -1 ? (index === 0) : (this.activeTb === table.tb_id),
          search_fields: table.fields || []
        }
        activeIndex = tb.show ? index : 0
        if (table.select_field_mode) {
          tb.checked_ids = table.checked_ids
          tb.select_field_mode = table.select_field_mode
          _each(table.fields, field => {
            field.name = field.field_title
            field.fid = field.field_id
          })
        }
        tbIds.push(tb.tb_id)
        this.tableList.push(tb)
        this.sqlMap[tb.tb_id] = this.sqlMap[tb.tb_id] ? this.sqlMap[tb.tb_id] : ''
      })
      Object.keys(this.sqlMap).forEach((key) => {
        if (tbIds.indexOf(key) === -1) {
          delete this.sqlMap[key]
        }
      })
      this.activeTb = this.tableList[activeIndex] ? this.tableList[activeIndex].tb_id : ''
      if (tables.length > 0 && !tables[activeIndex].fields) {
        this.onLoadFields(tables[activeIndex].tb_id, activeIndex)
      }
    },
    onLoadFields (tbId, activeIndex) {
      this.loading = true
      return new Promise((resolve, reject) => {
        this.loadFields({
        tbId: tbId
        }).then((data) => {
          this.loading = false
          _each(data, field => {
            field.name = field.field_title
            field.fid = field.field_id
          })
          this.$set(this.tableList[activeIndex], 'fields', data)
          this.$set(this.tableList[activeIndex], 'search_fields', data)
          resolve(data)
        })
      })
    },
    initSqlData () {
      _each(this.sql, tb => {
        this.sqlMap[tb.tb_id] = tb.expression
      })
    },
    swichTable (table, tableIndex) {
      table.show = !table.show
      this.activeTb = table.tb_id
      this.codemirror = this.activeTb
      _each(this.tableList, tb => {
        if (tb.tb_id !== table.tb_id) {
          tb.show = false
        }
      })
      if (table.show && !table.fields) {
        this.onLoadFields(table.tb_id, tableIndex)
      }
    },
    searchText (table) {
      table.searchFields = _filter(table.fields, field => {
        return !table.query_text.trim() ? true : _includes(field.field_title, table.query_text)
      })
    },
    undo () {
      if (this.tableListInfo.length === 0) return
      this.$refs[this.activeTb][0].cminstance.undo()
    },
    redo () {
      if (this.tableListInfo.length === 0) return
      this.$refs[this.activeTb][0].cminstance.redo()
    },
    sqlFormat () {
      if (this.tableListInfo.length === 0) return
      /**
       * sql格式化回调
       * @event format
       * @property {object} {sql:'',tb_id:''}
        */
      this.$emit('format', {
        sql: this.sqlMap[this.activeTb],
        tb_id: this.activeTb
      })
    },
    sqlTrans () {
      if (this.tableListInfo.length === 0) return
      /**
       * 语法校验
       * @event translate
       * @property {object} {sql:'',tb_id:''}
        */
      this.$emit('translate', {
        sql: this.sqlMap[this.activeTb],
        tb_id: this.activeTb
      })
    },
    insertFormula (name) {
      this.insert(name, 'formula')
    },
    // 插入字段、函数
    insert (text, type) {
      if (this.tableListInfo.length === 0) return
      const codemirror = this.$refs[this.activeTb][0].cminstance
      let str = text
      if (type === 'field') {
        let imsertParam
        if (this.bracket === 'anti') {
          imsertParam = '`' + text + '`'
        } else if (this.bracket === 'square') {
          imsertParam = '[' + text + ']'
        } else {
          imsertParam = text
        }
        str = imsertParam
      } else if (type === 'formula') {
        str = text === 'COUNT_DISTINCT' ? 'COUNT(DISTINCT())' : text + '()'
      }
      codemirror.replaceSelection(str)
      if (type === 'formula') {
        const offset = str === 'COUNT(DISTINCT())' ? 2 : 1
        const cur = codemirror.getCursor()
        const ch = cur.ch - offset
        codemirror.setCursor(cur.line, ch)
      }
      codemirror.focus()
    },
    async openSelectFieldModal (e, table, tableIndex) {
      e.stopPropagation()
      if (!table.fields) {
        await this.onLoadFields(table.tb_id, tableIndex).then(fields => {
          table.fields = fields
        })
      }
      this.showModal = true
      this.$nextTick(() => {
        this.$refs.selectFieldModal.open({
          field_list: table.fields,
          checked_ids: table.checked_ids,
          select_field_mode: table.select_field_mode,
          tb_id: table.tb_id
        })
      })
    },
    getFieldMode (table) {
      const index = _findIndex(this.tableList, tb => tb.tb_id === table.tb_id)
      const curTable = this.tableList[index]
      curTable.select_field_mode = table.select_field_mode
      curTable.checked_ids = table.checked_ids
    },
    getSql () {
      const sqls = []
      for (const key in this.sqlMap) {
        if (key && this.sqlMap[key]) {
          sqls.push({
            tb_id: key,
            expression: this.sqlMap[key]
          })
        }
      }
      return sqls
    },
    /**
     *  获取数据
     * @public This is a public method
     * @returns {object} {sqls:[{tb_id:'',expression: ''}],selectFields:[]}
    */
    getParamData () {
      const data = {
        sqls: this.getSql(),
        selectFields: []
      }
      _each(this.tableList, table => {
        if (table.select_field_mode) {
          data.selectFields.push({
            select_field_mode: table.select_field_mode,
            checked_ids: table.checked_ids,
            tb_id: table.tb_id
          })
        }
      })
      return data
    },
    destroy () {
      this.$destroy(true)
      if (this.$el && this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el)
      }
    }
  },
  destroyed () {
    this.destroy()
  }
}

</script>
<style scoped>
.sql-filter-container {
  @apply w-full h-full flex overflow-hidden border-1 border-solid border-mono-a-300 rounded-md
}
.left {
    @apply w-63 shadow-elevation-outline-02
  }
.center {
  @apply  flex-1 px-6 overflow-auto
}
.right {
  @apply w-54 shadow-elevation-outline-02
}
::v-deep .CodeMirror-placeholder {
  color: rgba(21, 22, 24, 0.36) !important
}
.field-item {
  @apply p-0 relative
}
.field-item .insert-field-icon {
  @apply hidden mt-2
}
.field-item:hover .insert-field-icon {
  @apply block
}
.formula-item {
  @apply h-8 leading-8 text-type-800 cursor-pointer hover:bg-mono-a-100
}
.height-32 {
  @apply h-8 leading-8
}
.nowrap {
  @apply overflow-hidden overflow-ellipsis whitespace-nowrap
}
</style>
