<template>
  <a-tooltip placement="left" overlayClassName="sql-tooltip">
    <template slot="title">
      <div class="p-4">
        <h5 class="text-type-800 font-semibold">{{formulaInfo.name}}</h5>
        <div class="offset-tip">
          <span class="key">用法</span>
          <p class="text">{{formulaInfo.usage}}</p>
        </div>
        <div class="offset-tip" v-if="formulaInfo.desc">
          <span class="key">说明</span>
          <p class="text">{{formulaInfo.desc}}</p>
        </div>
        <div class="offset-tip" v-if="formulaInfo.demo">
          <span class="key">示例</span>
          <p class="text">{{formulaInfo.demo}}</p>
        </div>
      </div>
    </template>
    <p class="formula-item-name nowrap px-4" @click="insert(formulaInfo.name,'formula')">
      <hz-icon hzName="fx" class="text-primary-900"/>{{formulaInfo.name}}
    </p>
  </a-tooltip>
</template>
<script>
export default {
  name: 'FormulaInfo',
  components: {
  },
  props: {
    formulaInfo: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
    }
  },
  mounted () {
  },
  methods: {
    insert (name) {
      this.$emit('insertFormula', name)
    },
    destroy () {
      this.$destroy(true)
      if (this.$el && this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el)
      }
    }
  },
  destroyed () {
    this.destroy()
  }
}

</script>
<style scoped>
.offset-tip {
  @apply flex mt-2
}
.key {
  @apply w-6 font-semibold mr-4 text-type-700 flex-shrink-0
}
.text {
  @apply flex-1 leading-5 text-type-800
}
</style>
