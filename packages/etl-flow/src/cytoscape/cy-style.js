import { DefaultChartConfig, NODE_STATUS } from './constants'
import {
  getNodeBackgroundImage,
  getStatusBase64Image,
  getBorderAndOverlayColor,
  getBorderAndOverlayOpacity,
  getLineColor,
  getBorderStyle,
  encodeSvgData
} from './chart-util'

const nodeBgColor = {
  output: '#FF9431',
  blue: '#0099EB',
  green: '#239545',
  yellow: '#F5CD00',
  red: '#FF5266',
  purple: '#9646FF'
}

const bgColorSize = Math.max(8, DefaultChartConfig.nodeSize - 8)

const bgImageSize = Math.max(16, DefaultChartConfig.nodeSize - 16)

export default function getCytoscapeStyle (data) {
  return [
    /* 全局样式 */
    {
      selector: 'core',
      style: {
        // 鼠标点击空白处的背景
        'active-bg-color': '#000',
        'active-bg-opacity': 0,
        'active-bg-size': 20,
        // 框选样式
        'selection-box-color': '#0f0',
        'selection-box-border-color': '#000',
        'selection-box-border-width': 1,
        'selection-box-opacity': 1
      }
    },

    /* 激活的点或边覆盖样式 */
    {
      selector: ':active',
      style: {
        'overlay-color': '#0f0',
        'overlay-padding': 4,
        'overlay-opacity': 0
      }
    },

    /* 主节点样式 */
    {
      selector: 'node.node_main',
      style: {
        width: DefaultChartConfig.nodeSize,
        height: DefaultChartConfig.nodeSize,
        shape: 'round-rectangle',
        // 背景
        'background-color': '#FFFFFF',
        'background-blacken': 0,
        'background-opacity': 1,
        'background-fill': 'solid',
        'background-image': (e) => {
          const data = e.json().data
          let bg1, bg2
          if (data.type.indexOf('output') !== -1 &&
              data.status !== NODE_STATUS.empty
            ) { // 非空的输出结点默认标记颜色
            bg1 = nodeBgColor.output
          } else if (data.markColor) {
            bg1 = nodeBgColor[data.markColor]
          }

          if (bg1) {
            bg1 = encodeSvgData(`<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"52\" height=\"52\" fill=\"none\" viewBox=\"0 0 52 52\"><rect width=\"52\" height=\"52\" fill=\"${bg1}\" rx=\"8\"/></svg>`)
          }

          bg2 = getNodeBackgroundImage(data)
          if (bg2.indexOf('<svg') !== -1) {
            if (bg1) { // 将图片变成白色
              bg2 = bg2.replace(/fill="#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})"/g, 'fill="#fff"')
            }
            bg2 = encodeSvgData(bg2)
          }

          return bg1 ? [bg1, bg2] : [bg2]
        },
        'background-width': (e) => {
          const data = e.json().data
          if (data.type.indexOf('output') !== -1 || data.markColor) {
            return [bgColorSize, bgImageSize]
          }
          return bgImageSize
        },
        'background-height': (e) => {
          const data = e.json().data
          if (data.type.indexOf('output') !== -1 || data.markColor) {
            return [bgColorSize, bgImageSize]
          }
          return bgImageSize
        },
        'background-repeat': 'no-repeat',
        'background-position-x': ['50%', '50%'],
        'background-position-y': ['50%', '50%'],
        // 边框
        'border-width': 2,
        'border-style': (e) => getBorderStyle(e),
        'border-color': (e) => getBorderAndOverlayColor(e),
        'border-opacity': (e) => getBorderAndOverlayOpacity(e),
        'border-radius': 8,
        // 文字
        label: 'data(title)',
        color: 'rgb(15, 34, 67)',
        'text-opacity': 0.72,
        'font-family': 'Microsoft Yahei',
        'font-size': Math.min(18, DefaultChartConfig.nodeFontSize),
        // 'font-style': 'Regular',
        'font-weight': '600',
        'text-wrap': 'ellipsis',
        'text-max-width': '116px',
        // 'line-height': 1.2,
        'text-halign': 'center',
        'text-valign': 'bottom',
        'text-margin-y': 8,
        'min-zoomed-font-size': 10,
        'text-events': 'no',
        'z-index': 1000
      }
    },
    {
      selector: 'node.node_main.active', // hover节点
      style: {
        'overlay-color': (e) => getBorderAndOverlayColor(e),
        'overlay-padding': 4,
        'border-color': (e) => getBorderAndOverlayColor(e, true),
        'border-opacity': 1,
        'overlay-opacity': (e) => getBorderAndOverlayOpacity(e, true)
      }
    },

    /* 状态节点样式 */
    {
      selector: 'node.node_status',
      style: {
        width: DefaultChartConfig.nodeSize > 60 ? 24 : 16,
        height: DefaultChartConfig.nodeSize > 60 ? 24 : 16,
        shape: 'ellipse',
        'background-opacity': 0,
        'background-color': '#F00',
        'background-fit': 'cover',
        'background-image-opacity': 1,
        'background-image': (e) => getStatusBase64Image(e),
        events: 'no',
        'z-index': 1001
      }
    },

    /* 连线节点样式 */
    {
      selector: 'node.eh-handle',
      style: {
        shape: 'ellipse',
        width: 10,
        height: 10,
        'background-color': '#FFF',
        'border-width': 2,
        'border-style': 'solid',
        'border-color': 'rgb(15, 34, 67)',
        'border-opacity': 0.24,
        events: 'no'
      }
    },

    /* 边样式 */
    {
      selector: 'edge',
      style: {
        width: 2,
        'line-color': (e) => getLineColor(e.cy()),
        'line-style': 'solid',
        opacity: 1,
        'source-arrow-color': (e) => getLineColor(e.cy()),
        'source-arrow-shape': 'circle',
        'source-arrow-fill': 'hollow',
        'target-arrow-color': (e) => getLineColor(e.cy()),
        'target-arrow-shape': 'chevron',
        'target-arrow-fill': 'filled',
        'arrow-scale': 1,
        'source-distance-from-node': 6,
        'target-distance-from-node': 6
      }
    },
    {
      selector: 'edge.eh-ghost.eh-ghost-edge', // 画线临时线
      style: {
        'line-color': '#FF9431',
        'source-arrow-color': '#FF9431',
        'target-arrow-color': '#FF9431',
        'source-arrow-scale': 1.8
      }
    },
    {
      selector: 'edge.active', // hover边
      style: {
        'overlay-color': '#2B79FF',
        'overlay-padding': 4,
        'overlay-opacity': 0.15
      }
    },
    {
      selector: 'edge.highlight', // 高亮边
      style: {
        'line-color': '#2B79FF',
        'source-arrow-color': '#2B79FF',
        'target-arrow-color': '#2B79FF',
        opacity: 1,
        'z-index': 1
      }
    },
    {
      selector: 'edge.line_type_straight', // 直线
      style: {
        'curve-style': 'straight'
      }
    },
    {
      selector: 'edge.line_type_polyline', // 折线
      style: {
        'curve-style': 'taxi'
      }
    },
    {
      selector: 'edge.line_type_arc', // 弧线
      style: {
        'curve-style': 'unbundled-bezier',
        'control-point-distances': function (e) {
          const sp = e.source().position()
          const tp = e.target().position()
          const dis = 25
          const thesold = 5 // 坐标差值小于 thesold，将连成直线
          if (sp.x !== tp.x || sp.y !== tp.y) {
            if (
              Math.abs(sp.y - tp.y) <= thesold ||
              Math.abs(sp.x - tp.x) <= thesold
            ) {
              return 0
            } else if (sp.y > tp.y) {
              // 三四象限
              if (sp.x > tp.x) {
                // 第三象限
                return dis
              } else {
                // 第四象限
                return -dis
              }
            } else {
              // 一二象限
              if (sp.x < tp.x) {
                // 第一象限
                return dis
              } else {
                // 第二象限
                return -dis
              }
            }
          }
          return 0
        },
        'control-point-weights': 0.5
      }
    },
    {
      selector: 'edge.line_type_bezier', // 曲线
      style: {
        'curve-style': 'unbundled-bezier',
        'control-point-distances': function (e) {
          const sp = e.source().position()
          const tp = e.target().position()
          const dis = 20
          const thesold = 5
          if (sp.x !== tp.x || sp.y !== tp.y) {
            if (Math.abs(sp.y - tp.y) < thesold) {
              return [0, 0]
            } else {
              return [dis, -dis]
            }
          }
          return [0, 0]
        },
        'control-point-weights': [0.1, 0.8] // [0.25, 0.75] [0.1, 0.8]
      }
    },
    {
      selector: 'edge.line_type_layout', // 一键布局线样式
      style: {
        'curve-style': 'segments',
        'segment-distances': 0
      }
    },
    {
      selector: 'edge.line_style_solid', // 实线边
      style: {
        'line-style': 'solid'
      }
    },
    {
      selector: 'edge.line_style_dot', // 虚线边
      style: {
        'line-style': 'dashed',
        'line-dash-pattern': 'data(dash)'
      }
    }

    /* 其他样式 */
  ]
}
