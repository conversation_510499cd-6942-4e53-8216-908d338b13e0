/* eslint-disable */
(function () {
    'use strict'
    const $$find = function (arr, predicate) {
        if (typeof predicate !== 'function') {
            throw new TypeError('predicate must be a function')
        }
        const length = arr.length >>> 0
        const thisArg = arguments[1]
        let value
        for (let i = 0; i < length; i++) {
            value = arr[i]
            if (predicate.call(thisArg, value, i, arr)) {
                return value
            }
        }
        return undefined
    }
    const LabelElement = (function () {
        function LabelElement (_a, params) {
            const node = _a.node; const _b = _a.position; const position = _b === void 0 ? null : _b; const _c = _a.data; const data = _c === void 0 ? null : _c
            this.updateParams(params)
            this._node = node
            this.initStyles(params.cssClass)
            if (data) {
                this.updateData(data)
            }
            if (position) {
                this.updatePosition(position)
            }
        }
        LabelElement.prototype.updateParams = function (_a) {
            const _b = _a.tpl; const tpl = _b === void 0 ? function () { return '' } : _b; const _c = _a.cssClass; const cssClass = _c === void 0 ? null : _c; const _d = _a.halign; const halign = _d === void 0 ? 'center' : _d; const _e = _a.valign; const valign = _e === void 0 ? 'center' : _e; const _f = _a.halignBox; const halignBox = _f === void 0 ? 'center' : _f; const _g = _a.valignBox; const valignBox = _g === void 0 ? 'center' : _g
            const _align = {
                top: -0.5,
                left: -0.5,
                center: 0,
                right: 0.5,
                bottom: 0.5
            }
            this._align = [
                _align[halign],
                _align[valign],
                100 * (_align[halignBox] - 0.5),
                100 * (_align[valignBox] - 0.5)
            ]
            this.tpl = tpl
            // this.isDebug = !!_a.isDebug;
        }
        LabelElement.prototype.updateData = function (data) {
            try {
                this._node.innerHTML = this.tpl(data)
                this._data = data
            } catch (err) {
                // console.error(err);
            }
        }
        LabelElement.prototype.getNode = function () {
            return this._node
        }
        LabelElement.prototype.updatePosition = function (pos) {
            this._renderPosition(pos)
        }
        LabelElement.prototype.initStyles = function (cssClass) {
            const stl = this._node.style
            stl.position = 'absolute'
            if (cssClass && cssClass.length) {
                this._node.classList.add(cssClass)
            }
        }
        LabelElement.prototype._renderPosition = function (position) {
            const prev = this._position
            const x = position.x + this._align[0] * position.w
            const y = position.y + this._align[1] * position.h
            if (!prev || prev[0] !== x || prev[1] !== y) {
                this._position = [x, y]
                const valRel = 'translate(' + this._align[2] + '%,' + this._align[3] + '%) '
                const valAbs = 'translate(' + x.toFixed(2) + 'px,' + y.toFixed(2) + 'px) '
                const val = valRel + valAbs
                // if (this.isDebug) {
                //     val = 'translateX(-16px)' + val;
                // }
                const stl = this._node.style
                stl.webkitTransform = val
                stl.msTransform = val
                stl.transform = val
            }
        }
        return LabelElement
    }())
    const LabelContainer = (function () {
        function LabelContainer (node) {
            this._node = node
            this._elements = {}
        }
        LabelContainer.prototype.destory = function () {
            this._node.innerHTML = ''
            this._elements = {}
        },
        LabelContainer.prototype.addOrUpdateElem = function (id, param, payload) {
            if (payload === void 0) { payload = {} }
            const cur = this._elements[id]
            if (cur) {
                cur.updateParams(param)
                cur.updateData(payload.data)
                cur.updatePosition(payload.position)
            } else {
                // var tpl = param.tpl === void 0 ? function () { return ""; } : param.tpl;
                // var isCreateEle = !!tpl(payload.data);
                // 只创建有内容的结点，节省性能
                // if (isCreateEle) {
                  const nodeElem = document.createElement('div')
                  this._node.appendChild(nodeElem)
                  this._elements[id] = new LabelElement({
                      node: nodeElem,
                      data: payload.data,
                      position: payload.position
                  }, param)
                // }
            }
        }
        LabelContainer.prototype.removeElemById = function (id) {
            // 一个节点多个标签，id是 node.id + param.query 组合而成
            Object.keys(this._elements).forEach((key) => {
                if (new RegExp(`^${id}`).test(key)) {
                    this._node.removeChild(this._elements[key].getNode())
                    delete this._elements[key]
                }
            })
            // if (this._elements[id]) {
            //     this._node.removeChild(this._elements[id].getNode());
            //     delete this._elements[id];
            // }
        }
        LabelContainer.prototype.updateElemPosition = function (id, position) {
            Object.keys(this._elements).forEach((key) => {
                if (new RegExp(`^${id}`).test(key)) {
                    const ele = this._elements[key]
                    if (ele) {
                        ele.updatePosition(position)
                    }
                }
            })
            // var ele = this._elements[id];
            // if (ele) {
            //     ele.updatePosition(position);
            // }
        }
        LabelContainer.prototype.updatePanZoom = function (_a) {
            const pan = _a.pan; const zoom = _a.zoom
            const val = 'translate(' + pan.x + 'px,' + pan.y + 'px) scale(' + zoom + ')'
            const stl = this._node.style
            const origin = 'top left'
            stl.webkitTransform = val
            stl.msTransform = val
            stl.transform = val
            stl.webkitTransformOrigin = origin
            stl.msTransformOrigin = origin
            stl.transformOrigin = origin
        }
        return LabelContainer
    }())

    const bindThis = function (array, context) {
        array.forEach(
          funcName =>
            typeof context[funcName] === 'function' &&
            (context[funcName] = context[funcName].bind(context))
        )
    }

    function cyNodeHtmlLabel (_cy, params) {
        this._cy = _cy
        this._params = (!params || typeof params !== 'object') ? [] : params
        this._lc = this.createLabelContainer()

        bindThis(['addCyHandler', 'layoutstopHandler', 'removeCyHandler', 'updateDataOrStyleCyHandler', 'wrapCyHandler', 'moveCyHandler'], this)

        this._cy.one('render', (e) => {
            this.createNodesCyHandler(e)
            this.wrapCyHandler(e)
        })
        this._cy.on('add', 'node.node_main', this.addCyHandler)
        this._cy.on('layoutstop', this.layoutstopHandler)
        this._cy.on('remove', 'node.node_main', this.removeCyHandler)
        // 不监听 data 变化，提高性能
        // this._cy.on("data", "node.node_main", this.updateDataOrStyleCyHandler);
        // _cy.on("style", "node.node_main", this.updateDataOrStyleCyHandler);
        this._cy.on('pan zoom', this.wrapCyHandler)
        this._cy.on('position', 'node.node_main', this.moveCyHandler)
    }

    cyNodeHtmlLabel.prototype = {
        constructor: cyNodeHtmlLabel,
        update: function (params) {
            this._params = params
            this._lc.destory()
            this.createNodesCyHandler({ cy: this._cy })
            this.wrapCyHandler({ cy: this._cy })
        },
        updateData: function (target) {
            if (!target) {
                // target 不存在，更新所有
                this.createNodesCyHandler({ cy: this._cy })
                this.wrapCyHandler({ cy: this._cy })
            } else {
                this.updateDataOrStyleCyHandler({ target: target })
            }
        },
        createLabelContainer: function () {
            const _cyContainer = this._cy.container()
            const _titlesContainer = document.createElement('div')
            const _cyCanvas = _cyContainer.querySelector('canvas')
            const cur = _cyContainer.querySelector("[class^='cy-node-html']")
            if (cur) {
                _cyCanvas.parentNode.removeChild(cur)
            }
            const stl = _titlesContainer.style
            stl.position = 'absolute'
            stl['z-index'] = 10
            stl.width = '500px'
            // stl['pointer-events'] = 'none';
            stl.margin = '0px'
            stl.padding = '0px'
            stl.border = '0px'
            stl.outline = '0px'
            stl.outline = '0px'
            _cyCanvas.parentNode.appendChild(_titlesContainer);
            // 阻止事件冒泡到画布
            ['click', 'mouseover', 'mousemove', 'mouseout', 'mousedown', 'mouseup'].forEach(eventName => {
                _titlesContainer.addEventListener(eventName, e => {
                    e.stopPropagation()
                }, false)
            })
            return new LabelContainer(_titlesContainer)
        },
        getLabelContainer: function () {
            return this._lc && this._lc._node
        },
        createNodesCyHandler: function (_a) {
            const cy = _a.cy
            const self = this
            this._params.forEach(function (x) {
                cy.elements(x.query).forEach(function (d) {
                    if (d.isNode()) {
                        self._lc.addOrUpdateElem(d.id() + x.query, x, {
                            position: self.getNodePosition(d),
                            data: d.data()
                        })
                    }
                })
            })
        },
        addCyHandler: function (ev) {
            const target = ev.target
            const param = $$find(this._params.slice().reverse(), function (x) { return target.is(x.query) })
            if (param) {
                this._lc.addOrUpdateElem(target.id() + param.query, param, {
                    position: this.getNodePosition(target),
                    data: target.data()
                })
            }
        },
        layoutstopHandler: function (_a) {
            const cy = _a.cy
            const self = this
            this._params.forEach(function (x) {
                cy.elements(x.query).forEach(function (d) {
                    if (d.isNode()) {
                        self._lc.updateElemPosition(d.id(), self.getNodePosition(d))
                    }
                })
            })
        },
        removeCyHandler: function (ev) {
            this._lc.removeElemById(ev.target.id())
        },
        moveCyHandler: function (ev) {
            this._lc.updateElemPosition(ev.target.id(), this.getNodePosition(ev.target))
        },
        updateDataOrStyleCyHandler: function (ev) {
            // setTimeout(function () {
                const target = ev.target
                const param = $$find(this._params.slice().reverse(), function (x) { return target.is(x.query) })
                if (param) {
                    this._lc.addOrUpdateElem(target.id() + param.query, param, {
                        position: this.getNodePosition(target),
                        data: target.data()
                    })
                } else {
                    this._lc.removeElemById(target.id())
                }
            // }, 0);
        },
        wrapCyHandler: function (_a) {
            const cy = _a.cy
            this._lc.updatePanZoom({
                pan: cy.pan(),
                zoom: cy.zoom()
            })
        },
        getNodePosition: function (node) {
            return {
                w: node.width(),
                h: node.height(),
                x: node.position('x'),
                y: node.position('y')
            }
        }
    }

    const register = function (cy) {
        if (!cy) {
            return
        }
        cy('core', 'nodeHtmlLabel', function (optArr) {
            return new cyNodeHtmlLabel(this, optArr)
        })
    }
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = function (cy) {
            register(cy)
        }
    } else {
        if (typeof define !== 'undefined' && define.amd) {
            define('cytoscape-nodeHtmlLabel', function () {
                return register
            })
        }
    }
    if (typeof cytoscape !== 'undefined') {
        register(cytoscape)
    }
}())
// # sourceMappingURL=cytoscape-node-html-label.js.map
