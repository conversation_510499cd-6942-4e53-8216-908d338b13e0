/* eslint-disable */
(function webpackUniversalModuleDefinition (root, factory) {
	if (typeof exports === 'object' && typeof module === 'object') { module.exports = factory() } else if (typeof define === 'function' && define.amd) { define([], factory) } else if (typeof exports === 'object') { exports.cytoscapeEdgehandles = factory() } else { root.cytoscapeEdgehandles = factory() }
})(this, function () {
return /******/ (function (modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	const installedModules = {}
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__ (moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if (installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports
/******/ 		}

/******/ 		// Create a new module (and put it into the cache)
/******/ 		const module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		}
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__)
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules
/******/
/******/ 	// identity function for calling harmony imports with the correct context
/******/ 	__webpack_require__.i = function (value) { return value }
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function (exports, name, getter) {
/******/ 		if (!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, {
/******/ 				configurable: false,
/******/ 				enumerable: true,
/******/ 				get: getter
/******/ 			})
/******/ 		}
/******/ 	}
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function (module) {
/******/ 		const getter = module && module.__esModule
/******/ 			? function getDefault () { return module.default }
/******/ 			: function getModuleExports () { return module }
/******/ 		__webpack_require__.d(getter, 'a', getter)
/******/ 		return getter
/******/ 	}
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function (object, property) { return Object.prototype.hasOwnProperty.call(object, property) }
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = ''
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 29)
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony default export */ __webpack_exports__.a = function (node, x0, y0, x1, y1) {
  this.node = node
  this.x0 = x0
  this.y0 = y0
  this.x1 = x1
  this.y1 = y1
}
/***/ },
/* 1 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

// Simple, internal Object.assign() polyfill for options objects etc.

module.exports = Object.assign != null
? Object.assign.bind(Object)
: function (tgt) {
  for (var _len = arguments.length, srcs = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    srcs[_key - 1] = arguments[_key]
  }

  srcs.filter(function (src) {
    return src != null
  }).forEach(function (src) {
    Object.keys(src).forEach(function (k) {
      return tgt[k] = src[k]
    })
  })

  return tgt
}
/***/ },
/* 2 */
/***/ function (module, exports, __webpack_require__) {
/* WEBPACK VAR INJECTION */(function (global) { /**
 * lodash (Custom Build) <https://lodash.com/>
 * Build: `lodash modularize exports="npm" -o ./`
 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */
/** Used as the `TypeError` message for "Functions" methods. */
const FUNC_ERROR_TEXT = 'Expected a function'

/** Used as references for various `Number` constants. */
const NAN = 0 / 0

/** `Object#toString` result references. */
const symbolTag = '[object Symbol]'

/** Used to match leading and trailing whitespace. */
const reTrim = /^\s+|\s+$/g

/** Used to detect bad signed hexadecimal string values. */
const reIsBadHex = /^[-+]0x[0-9a-f]+$/i

/** Used to detect binary string values. */
const reIsBinary = /^0b[01]+$/i

/** Used to detect octal string values. */
const reIsOctal = /^0o[0-7]+$/i

/** Built-in method references without a dependency on `root`. */
const freeParseInt = parseInt

/** Detect free variable `global` from Node.js. */
const freeGlobal = typeof global === 'object' && global && global.Object === Object && global

/** Detect free variable `self`. */
const freeSelf = typeof self === 'object' && self && self.Object === Object && self

/** Used as a reference to the global object. */
const root = freeGlobal || freeSelf || Function('return this')()

/** Used for built-in method references. */
const objectProto = Object.prototype

/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */
const objectToString = objectProto.toString

/* Built-in method references for those with the same name as other `lodash` methods. */
const nativeMax = Math.max
    const nativeMin = Math.min

/**
 * Gets the timestamp of the number of milliseconds that have elapsed since
 * the Unix epoch (1 January 1970 00:00:00 UTC).
 *
 * @static
 * @memberOf _
 * @since 2.4.0
 * @category Date
 * @returns {number} Returns the timestamp.
 * @example
 *
 * _.defer(function(stamp) {
 *   console.log(_.now() - stamp);
 * }, _.now());
 * // => Logs the number of milliseconds it took for the deferred invocation.
 */
const now = function () {
  return root.Date.now()
}

/**
 * Creates a debounced function that delays invoking `func` until after `wait`
 * milliseconds have elapsed since the last time the debounced function was
 * invoked. The debounced function comes with a `cancel` method to cancel
 * delayed `func` invocations and a `flush` method to immediately invoke them.
 * Provide `options` to indicate whether `func` should be invoked on the
 * leading and/or trailing edge of the `wait` timeout. The `func` is invoked
 * with the last arguments provided to the debounced function. Subsequent
 * calls to the debounced function return the result of the last `func`
 * invocation.
 *
 * **Note:** If `leading` and `trailing` options are `true`, `func` is
 * invoked on the trailing edge of the timeout only if the debounced function
 * is invoked more than once during the `wait` timeout.
 *
 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
 *
 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
 * for details over the differences between `_.debounce` and `_.throttle`.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Function
 * @param {Function} func The function to debounce.
 * @param {number} [wait=0] The number of milliseconds to delay.
 * @param {Object} [options={}] The options object.
 * @param {boolean} [options.leading=false]
 *  Specify invoking on the leading edge of the timeout.
 * @param {number} [options.maxWait]
 *  The maximum time `func` is allowed to be delayed before it's invoked.
 * @param {boolean} [options.trailing=true]
 *  Specify invoking on the trailing edge of the timeout.
 * @returns {Function} Returns the new debounced function.
 * @example
 *
 * // Avoid costly calculations while the window size is in flux.
 * jQuery(window).on('resize', _.debounce(calculateLayout, 150));
 *
 * // Invoke `sendMail` when clicked, debouncing subsequent calls.
 * jQuery(element).on('click', _.debounce(sendMail, 300, {
 *   'leading': true,
 *   'trailing': false
 * }));
 *
 * // Ensure `batchLog` is invoked once after 1 second of debounced calls.
 * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });
 * var source = new EventSource('/stream');
 * jQuery(source).on('message', debounced);
 *
 * // Cancel the trailing debounced invocation.
 * jQuery(window).on('popstate', debounced.cancel);
 */
function debounce (func, wait, options) {
  let lastArgs
      let lastThis
      let maxWait
      let result
      let timerId
      let lastCallTime
      let lastInvokeTime = 0
      let leading = false
      let maxing = false
      let trailing = true

  if (typeof func !== 'function') {
    throw new TypeError(FUNC_ERROR_TEXT)
  }
  wait = toNumber(wait) || 0
  if (isObject(options)) {
    leading = !!options.leading
    maxing = 'maxWait' in options
    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait
    trailing = 'trailing' in options ? !!options.trailing : trailing
  }

  function invokeFunc (time) {
    const args = lastArgs
        const thisArg = lastThis

    lastArgs = lastThis = undefined
    lastInvokeTime = time
    result = func.apply(thisArg, args)
    return result
  }

  function leadingEdge (time) {
    // Reset any `maxWait` timer.
    lastInvokeTime = time
    // Start the timer for the trailing edge.
    timerId = setTimeout(timerExpired, wait)
    // Invoke the leading edge.
    return leading ? invokeFunc(time) : result
  }

  function remainingWait (time) {
    const timeSinceLastCall = time - lastCallTime
        const timeSinceLastInvoke = time - lastInvokeTime
        const result = wait - timeSinceLastCall

    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result
  }

  function shouldInvoke (time) {
    const timeSinceLastCall = time - lastCallTime
        const timeSinceLastInvoke = time - lastInvokeTime

    // Either this is the first call, activity has stopped and we're at the
    // trailing edge, the system time has gone backwards and we're treating
    // it as the trailing edge, or we've hit the `maxWait` limit.
    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait))
  }

  function timerExpired () {
    const time = now()
    if (shouldInvoke(time)) {
      return trailingEdge(time)
    }
    // Restart the timer.
    timerId = setTimeout(timerExpired, remainingWait(time))
  }

  function trailingEdge (time) {
    timerId = undefined

    // Only invoke if we have `lastArgs` which means `func` has been
    // debounced at least once.
    if (trailing && lastArgs) {
      return invokeFunc(time)
    }
    lastArgs = lastThis = undefined
    return result
  }

  function cancel () {
    if (timerId !== undefined) {
      clearTimeout(timerId)
    }
    lastInvokeTime = 0
    lastArgs = lastCallTime = lastThis = timerId = undefined
  }

  function flush () {
    return timerId === undefined ? result : trailingEdge(now())
  }

  function debounced () {
    const time = now()
        const isInvoking = shouldInvoke(time)

    lastArgs = arguments
    lastThis = this
    lastCallTime = time

    if (isInvoking) {
      if (timerId === undefined) {
        return leadingEdge(lastCallTime)
      }
      if (maxing) {
        // Handle invocations in a tight loop.
        timerId = setTimeout(timerExpired, wait)
        return invokeFunc(lastCallTime)
      }
    }
    if (timerId === undefined) {
      timerId = setTimeout(timerExpired, wait)
    }
    return result
  }
  debounced.cancel = cancel
  debounced.flush = flush
  return debounced
}

/**
 * Creates a throttled function that only invokes `func` at most once per
 * every `wait` milliseconds. The throttled function comes with a `cancel`
 * method to cancel delayed `func` invocations and a `flush` method to
 * immediately invoke them. Provide `options` to indicate whether `func`
 * should be invoked on the leading and/or trailing edge of the `wait`
 * timeout. The `func` is invoked with the last arguments provided to the
 * throttled function. Subsequent calls to the throttled function return the
 * result of the last `func` invocation.
 *
 * **Note:** If `leading` and `trailing` options are `true`, `func` is
 * invoked on the trailing edge of the timeout only if the throttled function
 * is invoked more than once during the `wait` timeout.
 *
 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
 *
 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
 * for details over the differences between `_.throttle` and `_.debounce`.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Function
 * @param {Function} func The function to throttle.
 * @param {number} [wait=0] The number of milliseconds to throttle invocations to.
 * @param {Object} [options={}] The options object.
 * @param {boolean} [options.leading=true]
 *  Specify invoking on the leading edge of the timeout.
 * @param {boolean} [options.trailing=true]
 *  Specify invoking on the trailing edge of the timeout.
 * @returns {Function} Returns the new throttled function.
 * @example
 *
 * // Avoid excessively updating the position while scrolling.
 * jQuery(window).on('scroll', _.throttle(updatePosition, 100));
 *
 * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.
 * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });
 * jQuery(element).on('click', throttled);
 *
 * // Cancel the trailing throttled invocation.
 * jQuery(window).on('popstate', throttled.cancel);
 */
function throttle (func, wait, options) {
  let leading = true
      let trailing = true

  if (typeof func !== 'function') {
    throw new TypeError(FUNC_ERROR_TEXT)
  }
  if (isObject(options)) {
    leading = 'leading' in options ? !!options.leading : leading
    trailing = 'trailing' in options ? !!options.trailing : trailing
  }
  return debounce(func, wait, {
    leading: leading,
    maxWait: wait,
    trailing: trailing
  })
}

/**
 * Checks if `value` is the
 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
 * @example
 *
 * _.isObject({});
 * // => true
 *
 * _.isObject([1, 2, 3]);
 * // => true
 *
 * _.isObject(_.noop);
 * // => true
 *
 * _.isObject(null);
 * // => false
 */
function isObject (value) {
  const type = typeof value
  return !!value && (type == 'object' || type == 'function')
}

/**
 * Checks if `value` is object-like. A value is object-like if it's not `null`
 * and has a `typeof` result of "object".
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
 * @example
 *
 * _.isObjectLike({});
 * // => true
 *
 * _.isObjectLike([1, 2, 3]);
 * // => true
 *
 * _.isObjectLike(_.noop);
 * // => false
 *
 * _.isObjectLike(null);
 * // => false
 */
function isObjectLike (value) {
  return !!value && typeof value === 'object'
}

/**
 * Checks if `value` is classified as a `Symbol` primitive or object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
 * @example
 *
 * _.isSymbol(Symbol.iterator);
 * // => true
 *
 * _.isSymbol('abc');
 * // => false
 */
function isSymbol (value) {
  return typeof value === 'symbol' ||
    (isObjectLike(value) && objectToString.call(value) == symbolTag)
}

/**
 * Converts `value` to a number.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to process.
 * @returns {number} Returns the number.
 * @example
 *
 * _.toNumber(3.2);
 * // => 3.2
 *
 * _.toNumber(Number.MIN_VALUE);
 * // => 5e-324
 *
 * _.toNumber(Infinity);
 * // => Infinity
 *
 * _.toNumber('3.2');
 * // => 3.2
 */
function toNumber (value) {
  if (typeof value === 'number') {
    return value
  }
  if (isSymbol(value)) {
    return NAN
  }
  if (isObject(value)) {
    const other = typeof value.valueOf === 'function' ? value.valueOf() : value
    value = isObject(other) ? (other + '') : other
  }
  if (typeof value !== 'string') {
    return value === 0 ? value : +value
  }
  value = value.replace(reTrim, '')
  const isBinary = reIsBinary.test(value)
  return (isBinary || reIsOctal.test(value))
    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)
    : (reIsBadHex.test(value) ? NAN : +value)
}

module.exports = throttle
/* WEBPACK VAR INJECTION */ }.call(exports, __webpack_require__(3)))
/***/ },
/* 3 */
/***/ function (module, exports) {
let g

// This works in non-strict mode
g = (function () {
	return this
})()

try {
	// This works if eval is allowed (see CSP)
	g = g || Function('return this')() || (1, eval)('this')
} catch (e) {
	// This works if the window reference is available
	if (typeof window === 'object') { g = window }
}

// g can still be undefined, but nothing to do about it...
// We return undefined, instead of nothing here, so it's
// easier to handle this case. if(!global) { ...}

module.exports = g
/***/ },
/* 4 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

const Edgehandles = __webpack_require__(27)
const assign = __webpack_require__(1)

module.exports = function (options) {
  const cy = this

  return new Edgehandles(assign({ cy: cy }, options))
}
/***/ },
/* 5 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony export (immutable) */ __webpack_exports__.b = addAll
/* harmony default export */ __webpack_exports__.a = function (d) {
  const x = +this._x.call(null, d)
      const y = +this._y.call(null, d)
  return add(this.cover(x, y), x, y, d)
}

function add (tree, x, y, d) {
  if (isNaN(x) || isNaN(y)) return tree // ignore invalid points

  let parent
      let node = tree._root
      const leaf = { data: d }
      let x0 = tree._x0
      let y0 = tree._y0
      let x1 = tree._x1
      let y1 = tree._y1
      let xm
      let ym
      let xp
      let yp
      let right
      let bottom
      let i
      let j

  // If the tree is empty, initialize the root as a leaf.
  if (!node) return tree._root = leaf, tree

  // Find the existing leaf for the new point, or add it.
  while (node.length) {
    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm
    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym
    if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree
  }

  // Is the new point is exactly coincident with the existing point?
  xp = +tree._x.call(null, node.data)
  yp = +tree._y.call(null, node.data)
  if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree

  // Otherwise, split the leaf node until the old and new point are separated.
  do {
    parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4)
    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm
    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym
  } while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | (xp >= xm)))
  return parent[j] = node, parent[i] = leaf, tree
}

function addAll (data) {
  let d; let i; const n = data.length
      let x
      let y
      const xz = new Array(n)
      const yz = new Array(n)
      let x0 = Infinity
      let y0 = Infinity
      let x1 = -Infinity
      let y1 = -Infinity

  // Compute the points and their extent.
  for (i = 0; i < n; ++i) {
    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue
    xz[i] = x
    yz[i] = y
    if (x < x0) x0 = x
    if (x > x1) x1 = x
    if (y < y0) y0 = y
    if (y > y1) y1 = y
  }

  // If there were no (valid) points, abort.
  if (x0 > x1 || y0 > y1) return this

  // Expand the tree to cover the new points.
  this.cover(x0, y0).cover(x1, y1)

  // Add the new points.
  for (i = 0; i < n; ++i) {
    add(this, xz[i], yz[i], data[i])
  }

  return this
}
/***/ },
/* 6 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony default export */ __webpack_exports__.a = function (x, y) {
  if (isNaN(x = +x) || isNaN(y = +y)) return this // ignore invalid points

  let x0 = this._x0
      let y0 = this._y0
      let x1 = this._x1
      let y1 = this._y1

  // If the quadtree has no extent, initialize them.
  // Integer extent are necessary so that if we later double the extent,
  // the existing quadrant boundaries don’t change due to floating point error!
  if (isNaN(x0)) {
    x1 = (x0 = Math.floor(x)) + 1
    y1 = (y0 = Math.floor(y)) + 1
  }

  // Otherwise, double repeatedly to cover.
  else {
    let z = x1 - x0
        let node = this._root
        let parent
        let i

    while (x0 > x || x >= x1 || y0 > y || y >= y1) {
      i = (y < y0) << 1 | (x < x0)
      parent = new Array(4), parent[i] = node, node = parent, z *= 2
      switch (i) {
        case 0: x1 = x0 + z, y1 = y0 + z; break
        case 1: x0 = x1 - z, y1 = y0 + z; break
        case 2: x1 = x0 + z, y0 = y1 - z; break
        case 3: x0 = x1 - z, y0 = y1 - z; break
      }
    }

    if (this._root && this._root.length) this._root = node
  }

  this._x0 = x0
  this._y0 = y0
  this._x1 = x1
  this._y1 = y1
  return this
}
/***/ },
/* 7 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony default export */ __webpack_exports__.a = function () {
  const data = []
  this.visit(function (node) {
    if (!node.length) do data.push(node.data); while (node = node.next)
  })
  return data
}
/***/ },
/* 8 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony default export */ __webpack_exports__.a = function (_) {
  return arguments.length
      ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1])
      : isNaN(this._x0) ? undefined : [[this._x0, this._y0], [this._x1, this._y1]]
}
/***/ },
/* 9 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_0__quad_js__ = __webpack_require__(0)

/* harmony default export */ __webpack_exports__.a = function (x, y, radius) {
  let data
      let x0 = this._x0
      let y0 = this._y0
      let x1
      let y1
      let x2
      let y2
      let x3 = this._x1
      let y3 = this._y1
      const quads = []
      let node = this._root
      let q
      let i

  if (node) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](node, x0, y0, x3, y3))
  if (radius == null) radius = Infinity
  else {
    x0 = x - radius, y0 = y - radius
    x3 = x + radius, y3 = y + radius
    radius *= radius
  }

  while (q = quads.pop()) {
    // Stop searching if this quadrant can’t contain a closer node.
    if (!(node = q.node) ||
        (x1 = q.x0) > x3 ||
        (y1 = q.y0) > y3 ||
        (x2 = q.x1) < x0 ||
        (y2 = q.y1) < y0) continue

    // Bisect the current quadrant.
    if (node.length) {
      const xm = (x1 + x2) / 2
          const ym = (y1 + y2) / 2

      quads.push(
        new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](node[3], xm, ym, x2, y2),
        new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](node[2], x1, ym, xm, y2),
        new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](node[1], xm, y1, x2, ym),
        new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](node[0], x1, y1, xm, ym)
      )

      // Visit the closest quadrant first.
      if (i = (y >= ym) << 1 | (x >= xm)) {
        q = quads[quads.length - 1]
        quads[quads.length - 1] = quads[quads.length - 1 - i]
        quads[quads.length - 1 - i] = q
      }
    }

    // Visit this point. (Visiting coincident points isn’t necessary!)
    else {
      const dx = x - +this._x.call(null, node.data)
          const dy = y - +this._y.call(null, node.data)
          const d2 = dx * dx + dy * dy
      if (d2 < radius) {
        const d = Math.sqrt(radius = d2)
        x0 = x - d, y0 = y - d
        x3 = x + d, y3 = y + d
        data = node.data
      }
    }
  }

  return data
}
/***/ },
/* 10 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
Object.defineProperty(__webpack_exports__, '__esModule', { value: true })
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_0__quadtree_js__ = __webpack_require__(11)
/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, 'quadtree', function () { return __WEBPACK_IMPORTED_MODULE_0__quadtree_js__.a })
/***/ },
/* 11 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony export (immutable) */ __webpack_exports__.a = quadtree
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_0__add_js__ = __webpack_require__(5)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_1__cover_js__ = __webpack_require__(6)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_2__data_js__ = __webpack_require__(7)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_3__extent_js__ = __webpack_require__(8)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_4__find_js__ = __webpack_require__(9)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_5__remove_js__ = __webpack_require__(12)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_6__root_js__ = __webpack_require__(13)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_7__size_js__ = __webpack_require__(14)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_8__visit_js__ = __webpack_require__(15)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_9__visitAfter_js__ = __webpack_require__(16)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_10__x_js__ = __webpack_require__(17)
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_11__y_js__ = __webpack_require__(18)

function quadtree (nodes, x, y) {
  const tree = new Quadtree(x == null ? __WEBPACK_IMPORTED_MODULE_10__x_js__['a' /* defaultX */] : x, y == null ? __WEBPACK_IMPORTED_MODULE_11__y_js__['a' /* defaultY */] : y, NaN, NaN, NaN, NaN)
  return nodes == null ? tree : tree.addAll(nodes)
}

function Quadtree (x, y, x0, y0, x1, y1) {
  this._x = x
  this._y = y
  this._x0 = x0
  this._y0 = y0
  this._x1 = x1
  this._y1 = y1
  this._root = undefined
}

function leaf_copy (leaf) {
  const copy = { data: leaf.data }; let next = copy
  while (leaf = leaf.next) next = next.next = { data: leaf.data }
  return copy
}

const treeProto = quadtree.prototype = Quadtree.prototype

treeProto.copy = function () {
  const copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1)
      let node = this._root
      let nodes
      let child

  if (!node) return copy

  if (!node.length) return copy._root = leaf_copy(node), copy

  nodes = [{ source: node, target: copy._root = new Array(4) }]
  while (node = nodes.pop()) {
    for (let i = 0; i < 4; ++i) {
      if (child = node.source[i]) {
        if (child.length) nodes.push({ source: child, target: node.target[i] = new Array(4) })
        else node.target[i] = leaf_copy(child)
      }
    }
  }

  return copy
}

treeProto.add = __WEBPACK_IMPORTED_MODULE_0__add_js__['a' /* default */]
treeProto.addAll = __WEBPACK_IMPORTED_MODULE_0__add_js__['b' /* addAll */]
treeProto.cover = __WEBPACK_IMPORTED_MODULE_1__cover_js__['a' /* default */]
treeProto.data = __WEBPACK_IMPORTED_MODULE_2__data_js__['a' /* default */]
treeProto.extent = __WEBPACK_IMPORTED_MODULE_3__extent_js__['a' /* default */]
treeProto.find = __WEBPACK_IMPORTED_MODULE_4__find_js__['a' /* default */]
treeProto.remove = __WEBPACK_IMPORTED_MODULE_5__remove_js__['a' /* default */]
treeProto.removeAll = __WEBPACK_IMPORTED_MODULE_5__remove_js__['b' /* removeAll */]
treeProto.root = __WEBPACK_IMPORTED_MODULE_6__root_js__['a' /* default */]
treeProto.size = __WEBPACK_IMPORTED_MODULE_7__size_js__['a' /* default */]
treeProto.visit = __WEBPACK_IMPORTED_MODULE_8__visit_js__['a' /* default */]
treeProto.visitAfter = __WEBPACK_IMPORTED_MODULE_9__visitAfter_js__['a' /* default */]
treeProto.x = __WEBPACK_IMPORTED_MODULE_10__x_js__['b' /* default */]
treeProto.y = __WEBPACK_IMPORTED_MODULE_11__y_js__['b' /* default */]
/***/ },
/* 12 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony export (immutable) */ __webpack_exports__.b = removeAll
/* harmony default export */ __webpack_exports__.a = function (d) {
  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this // ignore invalid points

  let parent
      let node = this._root
      let retainer
      let previous
      let next
      let x0 = this._x0
      let y0 = this._y0
      let x1 = this._x1
      let y1 = this._y1
      let x
      let y
      let xm
      let ym
      let right
      let bottom
      let i
      let j

  // If the tree is empty, initialize the root as a leaf.
  if (!node) return this

  // Find the leaf node for the point.
  // While descending, also retain the deepest parent with a non-removed sibling.
  if (node.length) {
 while (true) {
    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm
    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym
    if (!(parent = node, node = node[i = bottom << 1 | right])) return this
    if (!node.length) break
    if (parent[(i + 1) & 3] || parent[(i + 2) & 3] || parent[(i + 3) & 3]) retainer = parent, j = i
  }
}

  // Find the point to remove.
  while (node.data !== d) if (!(previous = node, node = node.next)) return this
  if (next = node.next) delete node.next

  // If there are multiple coincident points, remove just the point.
  if (previous) return (next ? previous.next = next : delete previous.next), this

  // If this is the root point, remove it.
  if (!parent) return this._root = next, this

  // Remove this leaf.
  next ? parent[i] = next : delete parent[i]

  // If the parent now contains exactly one leaf, collapse superfluous parents.
  if ((node = parent[0] || parent[1] || parent[2] || parent[3]) &&
      node === (parent[3] || parent[2] || parent[1] || parent[0]) &&
      !node.length) {
    if (retainer) retainer[j] = node
    else this._root = node
  }

  return this
}

function removeAll (data) {
  for (let i = 0, n = data.length; i < n; ++i) this.remove(data[i])
  return this
}
/***/ },
/* 13 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony default export */ __webpack_exports__.a = function () {
  return this._root
}
/***/ },
/* 14 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony default export */ __webpack_exports__.a = function () {
  let size = 0
  this.visit(function (node) {
    if (!node.length) do ++size; while (node = node.next)
  })
  return size
}
/***/ },
/* 15 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_0__quad_js__ = __webpack_require__(0)

/* harmony default export */ __webpack_exports__.a = function (callback) {
  const quads = []; let q; let node = this._root; let child; let x0; let y0; let x1; let y1
  if (node) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](node, this._x0, this._y0, this._x1, this._y1))
  while (q = quads.pop()) {
    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {
      const xm = (x0 + x1) / 2; const ym = (y0 + y1) / 2
      if (child = node[3]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, xm, ym, x1, y1))
      if (child = node[2]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, x0, ym, xm, y1))
      if (child = node[1]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, xm, y0, x1, ym))
      if (child = node[0]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, x0, y0, xm, ym))
    }
  }
  return this
}
/***/ },
/* 16 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony import */ const __WEBPACK_IMPORTED_MODULE_0__quad_js__ = __webpack_require__(0)

/* harmony default export */ __webpack_exports__.a = function (callback) {
  const quads = []; const next = []; let q
  if (this._root) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](this._root, this._x0, this._y0, this._x1, this._y1))
  while (q = quads.pop()) {
    const node = q.node
    if (node.length) {
      var child; const x0 = q.x0; const y0 = q.y0; const x1 = q.x1; const y1 = q.y1; const xm = (x0 + x1) / 2; const ym = (y0 + y1) / 2
      if (child = node[0]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, x0, y0, xm, ym))
      if (child = node[1]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, xm, y0, x1, ym))
      if (child = node[2]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, x0, ym, xm, y1))
      if (child = node[3]) quads.push(new __WEBPACK_IMPORTED_MODULE_0__quad_js__['a' /* default */](child, xm, ym, x1, y1))
    }
    next.push(q)
  }
  while (q = next.pop()) {
    callback(q.node, q.x0, q.y0, q.x1, q.y1)
  }
  return this
}
/***/ },
/* 17 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony export (immutable) */ __webpack_exports__.a = defaultX
function defaultX (d) {
  return d[0]
}

/* harmony default export */ __webpack_exports__.b = function (_) {
  return arguments.length ? (this._x = _, this) : this._x
}
/***/ },
/* 18 */
/***/ function (module, __webpack_exports__, __webpack_require__) {
'use strict'
/* harmony export (immutable) */ __webpack_exports__.a = defaultY
function defaultY (d) {
  return d[1]
}

/* harmony default export */ __webpack_exports__.b = function (_) {
  return arguments.length ? (this._y = _, this) : this._y
}
/***/ },
/* 19 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

function disableGestures () {
  this.saveGestureState()

  this.cy.zoomingEnabled(false).panningEnabled(false).boxSelectionEnabled(false)

  if (this.options.disableBrowserGestures) {
    const wlOpts = this.windowListenerOptions

    window.addEventListener('touchstart', this.preventDefault, wlOpts)
    window.addEventListener('touchmove', this.preventDefault, wlOpts)
    window.addEventListener('wheel', this.preventDefault, wlOpts)
  }

  return this
}

function resetGestures () {
  this.cy.zoomingEnabled(this.lastZoomingEnabled).panningEnabled(this.lastPanningEnabled).boxSelectionEnabled(this.lastBoxSelectionEnabled)

  if (this.options.disableBrowserGestures) {
    const wlOpts = this.windowListenerOptions

    window.removeEventListener('touchstart', this.preventDefault, wlOpts)
    window.removeEventListener('touchmove', this.preventDefault, wlOpts)
    window.removeEventListener('wheel', this.preventDefault, wlOpts)
  }

  return this
}

function saveGestureState () {
  const cy = this.cy

  this.lastPanningEnabled = cy.panningEnabled()
  this.lastZoomingEnabled = cy.zoomingEnabled()
  this.lastBoxSelectionEnabled = cy.boxSelectionEnabled()

  return this
}

module.exports = { disableGestures: disableGestures, resetGestures: resetGestures, saveGestureState: saveGestureState }
/***/ },
/* 20 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

const throttle = __webpack_require__(2)
function addCytoscapeListeners () {
  const _this = this

  const cy = this.cy
      const options = this.options

  // grabbing nodes

  this.addListener(cy, 'drag', function () {
    return _this.grabbingNode = true
  })
  this.addListener(cy, 'free', function () {
    return _this.grabbingNode = false
  })

  // show handle on hover
  // this.addListener( cy, 'mouseover', 'node', e => {
  //   this.show( e.target );
  // } );

  // hide handle on tap handle
  // this.addListener( cy, 'tap', 'node', e => {
  //   let node = e.target;

  //   if( !node.same( this.handleNode ) ){
  //     this.show( node );
  //   }
  // } );

  // hide handle when source node moved
  // this.addListener( cy, 'position', 'node', e => {
  //   if( e.target.same( this.sourceNode ) ){
  //     this.hide();
  //   }
  // } );

  this.addListener(cy, 'mousemove', throttle(function (e) {
    const isShowHandlerNode = (e.target === cy || e.target.isEdge())
    if ((e.target === cy && !this.grabbingNode && !this.active) || (e.target.isNode && !e.target.isNode())) {
      cy.container().style.cursor = 'pointer'
    }
    if (!isShowHandlerNode || this.active || this.grabbingNode) {
      this.hide()
      return
    }
    const node = this.getClickNode(e)
    if (node) {
      if (!this.canStartOn(node) || this.drawMode && !options.handleInDrawMode) {
        this.hide()
        return
      }
      this.show(node, e.position)
      cy.container().style.cursor = 'default'
    } else {
      this.hide()
    }
  }.bind(this), options.hoverDelay))

  // start on tapstart handle
  // start on tapstart node (draw mode)
  // toggle on source node
  // 鼠标点下触发，click
  this.addListener(cy, 'tapstart', function (e) {
    const node = e.target

    // if( node.same( this.handleNode ) ){
    //   this.start( this.sourceNode );
    // } else if( this.drawMode ){
    //   this.start( node );
    // } else if( node.same( this.sourceNode ) ){
    //   this.hide();
    // }

    if (_this.handleShown() && _this.handleNode.length && _this.sourceNode.length) {
      _this.hide()
      _this.start(_this.sourceNode)
    }
    // if (node === cy) {
    //   // 点击空白
    //   let clickNode = this.getClickNode(e);
    //   if (clickNode) {
    //     if( !this.canStartOn(clickNode) || ( this.drawMode && !options.handleInDrawMode ) ){ return; }
    //     this.start(clickNode);
    //     // console.log('clickNode', clickNode);
    //     // this.show(clickNode, e.position);
    //     // if (this.sourceNode) {
    //     //   // this.hide();
    //     //   this.start(this.sourceNode);
    //     // }
    //     // this.start(clickNode);
    //   }
    // }
  })

  // update line on drag
  // 鼠标移动触发，mousemove
  this.addListener(cy, 'tapdrag', function (e) {
    _this.update(e.position)
  })

  // hover over preview
  // this.addListener( cy, 'tapdragover', 'node', e => {
  //   if( options.snap ){
  //     // then ignore events like mouseover
  //   } else {
  //     this.preview( e.target );
  //   }
  // } );

  // hover out unpreview
  // this.addListener( cy, 'tapdragout', 'node', e => {
  //   if( options.snap ){
  //     // then keep the preview
  //   } else {
  //     this.unpreview( e.target );
  //   }
  // } );

  // stop gesture on tapend
  // 每次鼠标抬起触发，mouseup
  this.addListener(cy, 'tapend', function (e) {
    if (_this.active && e.target !== _this.cy && e.target.isNode()) {
      _this.targetNode = e.target
    }
    _this.stop()
  })

  // hide handle if source node is removed
  this.addListener(cy, 'remove', function (e) {
    if (e.target.same(_this.sourceNode)) {
      _this.hide()
    }
  })

  return this
}

module.exports = { addCytoscapeListeners: addCytoscapeListeners }
/***/ },
/* 21 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

/* eslint-disable no-unused-vars */
const defaults = {
  preview: false, // whether to show added edges preview before releasing selection
  hoverDelay: 150, // time spent hovering over a target node before it is considered selected
  handleNodes: 'node', // selector/filter function for whether edges can be made from a given node
  snap: false, // when enabled, the edge can be drawn by just moving close to a target node (can be confusing on compound graphs)
  snapThreshold: 50, // the target node must be less than or equal to this many pixels away from the cursor/finger
  snapFrequency: 15, // the number of times per second (Hz) that snap checks done (lower is less expensive)
  noEdgeEventsInDraw: false, // set events:no to edges during draws, prevents mouseouts on compounds
  disableBrowserGestures: true, // during an edge drawing gesture, disable browser gestures such as two-finger trackpad swipe and pinch-to-zoom
  handlePosition: function handlePosition (node) {
    return 'middle top' // sets the position of the handle in the format of "X-AXIS Y-AXIS" such as "left top", "middle top"
  },
  handleInDrawMode: false, // whether to show the handle in draw mode
  edgeType: function edgeType (sourceNode, targetNode) {
    // can return 'flat' for flat edges between nodes or 'node' for intermediate node between them
    // returning null/undefined means an edge can't be added between the two nodes
    return 'flat'
  },
  loopAllowed: function loopAllowed (node) {
    // for the specified node, return whether edges from itself to itself are allowed
    return false
  },
  nodeLoopOffset: -50, // offset for edgeType: 'node' loops
  nodeParams: function nodeParams (sourceNode, targetNode) {
    // for edges between the specified source and target
    // return element object to be passed to cy.add() for intermediary node
    return {}
  },
  edgeParams: function edgeParams (sourceNode, targetNode, i) {
    // for edges between the specified source and target
    // return element object to be passed to cy.add() for edge
    // NB: i indicates edge index in case of edgeType: 'node'
    return {}
  },
  ghostEdgeParams: function ghostEdgeParams () {
    // return element object to be passed to cy.add() for the ghost edge
    // (default classes are always added for you)
    return {}
  },
  show: function show (sourceNode) {
    // fired when handle is shown
  },
  hide: function hide (sourceNode) {
    // fired when the handle is hidden
  },
  start: function start (sourceNode) {
    // fired when edgehandles interaction starts (drag on handle)
  },
  complete: function complete (sourceNode, targetNode, addedEles) {
    // fired when edgehandles is done and elements are added
  },
  stop: function stop (sourceNode) {
    // fired when edgehandles interaction is stopped (either complete with added edges or incomplete)
  },
  cancel: function cancel (sourceNode, cancelledTargets) {
    // fired when edgehandles are cancelled (incomplete gesture)
  },
  hoverover: function hoverover (sourceNode, targetNode) {
    // fired when a target is hovered
  },
  hoverout: function hoverout (sourceNode, targetNode) {
    // fired when a target isn't hovered anymore
  },
  previewon: function previewon (sourceNode, targetNode, previewEles) {
    // fired when preview is shown
  },
  previewoff: function previewoff (sourceNode, targetNode, previewEles) {
    // fired when preview is hidden
  },
  drawon: function drawon () {
    // fired when draw mode enabled
  },
  drawoff: function drawoff () {
    // fired when draw mode disabled
  }
}
/* eslint-enable */

module.exports = defaults
/***/ },
/* 22 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

function toggleDrawMode (bool) {
  const cy = this.cy
      const options = this.options

  this.drawMode = bool != null ? bool : !this.drawMode

  if (this.drawMode) {
    this.prevUngrabifyState = cy.autoungrabify()

    cy.autoungrabify(true)

    if (!options.handleInDrawMode && this.handleShown()) {
      this.hide()
    }

    this.emit('drawon')
  } else {
    cy.autoungrabify(this.prevUngrabifyState)

    this.emit('drawoff')
  }

  return this
}

function enableDrawMode () {
  return this.toggleDrawMode(true)
}

function disableDrawMode () {
  return this.toggleDrawMode(false)
}

module.exports = { toggleDrawMode: toggleDrawMode, enableDrawMode: enableDrawMode, disableDrawMode: disableDrawMode }
/***/ },
/* 23 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

const _typeof = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? function (obj) { return typeof obj } : function (obj) { return obj && typeof Symbol === 'function' && obj.constructor === Symbol && obj !== Symbol.prototype ? 'symbol' : typeof obj }

const assign = __webpack_require__(1)
const isString = function isString (x) {
  return (typeof x === 'undefined' ? 'undefined' : _typeof(x)) === _typeof('')
}
const isArray = function isArray (x) {
  return (typeof x === 'undefined' ? 'undefined' : _typeof(x)) === _typeof([]) && x.length != null
}

function getEleJson (overrides, params, addedClasses) {
  const json = {}

  // basic values
  assign(json, params, overrides)

  // make sure params can specify data but that overrides take precedence
  assign(json.data, params.data, overrides.data)

  if (isString(params.classes)) {
    json.classes = params.classes + ' ' + addedClasses
  } else if (isArray(params.classes)) {
    json.classes = params.classes.join(' ') + ' ' + addedClasses
  } else {
    json.classes = addedClasses
  }

  return json
}

function makeEdges () {
  const preview = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false
  const cy = this.cy
      const options = this.options
      const presumptiveTargets = this.presumptiveTargets
      const previewEles = this.previewEles
      const active = this.active

  const source = this.sourceNode
  const target = this.targetNode
  const classes = preview ? 'eh-preview' : ''
  let added = cy.collection()
  const edgeType = options.edgeType(source, target)

  // can't make edges outside of regular gesture lifecycle
  if (!active) {
    return
  }

  // must have a non-empty edge type
  if (!edgeType) {
    return
  }

  // can't make preview if disabled
  if (preview && !options.preview) {
    return
  }

  // detect cancel
  if (!target || target.size() === 0) {
    previewEles.remove()

    this.emit('cancel', this.mp(), source, presumptiveTargets)

    return
  }

  // just remove preview class if we already have the edges
  if (!preview && options.preview) {
    previewEles.removeClass('eh-preview').style('events', '')

    this.emit('complete', this.mp(), source, target, previewEles)

    return
  }

  const p1 = source.position()
  const p2 = target.position()

  let p = void 0
  if (source.same(target)) {
    p = {
      x: p1.x + options.nodeLoopOffset,
      y: p1.y + options.nodeLoopOffset
    }
  } else {
    p = {
      x: (p1.x + p2.x) / 2,
      y: (p1.y + p2.y) / 2
    }
  }

  if (edgeType === 'node') {
    const interNode = cy.add(getEleJson({
      group: 'nodes',
      position: p
    }, options.nodeParams(source, target), classes))

    const source2inter = cy.add(getEleJson({
      group: 'edges',
      data: {
        source: source.id(),
        target: interNode.id()
      }
    }, options.edgeParams(source, target, 0), classes))

    const inter2target = cy.add(getEleJson({
      group: 'edges',
      data: {
        source: interNode.id(),
        target: target.id()
      }
    }, options.edgeParams(source, target, 1), classes))

    added = added.merge(interNode).merge(source2inter).merge(inter2target)
  } else { // flat
    // classes += ' ';
    // classes += `line_type_${cy.data('currentLineType')}`;
    // let source2target = cy.add(
    //   getEleJson(
    //     {
    //       group: 'edges',
    //       data: {
    //         source: source.id(),
    //         target: target.id()
    //       }
    //     },
    //     options.edgeParams( source, target, 0 ),
    //     classes
    //   )
    // );

    // added = added.merge( source2target );
  }

  if (preview) {
    this.previewEles = added

    added.style('events', 'no')
  } else {
    added.style('events', '')

    this.emit('complete', this.mp(), source, target, added)
  }

  return this
}

function makePreview () {
  this.makeEdges(true)

  return this
}

function previewShown () {
  return this.previewEles.nonempty() && this.previewEles.inside()
}

function removePreview () {
  if (this.previewShown()) {
    this.previewEles.remove()
  }

  return this
}

function handleShown () {
  return this.handleNode.nonempty() && this.handleNode.inside()
}

function removeHandle () {
  if (this.handleShown()) {
    this.handleNode.remove()
  }

  return this
}

function setHandleFor (node, position) {
  const _this = this

  const options = this.options
      const cy = this.cy
  /*
  let handlePosition = typeof options.handlePosition === typeof '' ? () => options.handlePosition : options.handlePosition;
   let p = node.position();
  let h = node.outerHeight();
  let w = node.outerWidth();
   // store how much we should move the handle from origin(p.x, p.y)
  let moveX = 0;
  let moveY = 0;
   // grab axes
  let axes = handlePosition( node ).toLowerCase().split(/\s+/);
  let axisX = axes[0];
  let axisY = axes[1];
   // based on handlePosition move left/right/top/bottom. Middle/middle will just be normal
  if( axisX === 'left' ){
    moveX = -(w / 2);
  } else if( axisX === 'right' ){
    moveX = w / 2;
  } if( axisY === 'top' ){
    moveY = -(h / 2);
  } else if( axisY === 'bottom' ){
    moveY = h / 2;
  }
   // set handle x and y based on adjusted positions
  let hx = this.hx = p.x + moveX;
  let hy = this.hy = p.y + moveY;
  let pos = { x: hx, y: hy };
  */

  const pos = position
  if (this.handleShown()) {
    this.handleNode.position(pos)
  } else {
    cy.batch(function () {
      _this.handleNode = cy.add({
        classes: 'eh-handle',
        position: pos,
        grabbable: false,
        selectable: false
      })

      _this.handleNode.style('z-index', 9007199254740991)
    })
  }

  return this
}

function updateEdge () {
  const _this2 = this

  const sourceNode = this.sourceNode
      let ghostNode = this.ghostNode
      const cy = this.cy
      const mx = this.mx
      const my = this.my
      const options = this.options

  const x = mx
  const y = my
  let ghostEdge = void 0
      let ghostEles = void 0

  // can't draw a line without having the starting node
  if (!sourceNode) {
    return
  }

  if (!ghostNode || ghostNode.length === 0 || ghostNode.removed()) {
    ghostEles = this.ghostEles = cy.collection()

    cy.batch(function () {
      ghostNode = _this2.ghostNode = cy.add({
        group: 'nodes',
        classes: 'eh-ghost eh-ghost-node',
        position: {
          x: 0,
          y: 0
        }
      })

      ghostNode.style({
        'background-color': 'blue',
        width: 0.0001,
        height: 0.0001,
        opacity: 0,
        events: 'no'
      })

      const ghostEdgeParams = options.ghostEdgeParams()

      ghostEdge = cy.add(assign({}, ghostEdgeParams, {
        group: 'edges',
        data: assign({}, ghostEdgeParams.data, {
          source: sourceNode.id(),
          target: ghostNode.id()
        }),
        // classes:  'eh-ghost eh-ghost-edge' + ' ' + `line_type_${cy.data('currentLineType')}`
        classes: ghostEdgeParams.classes ? ghostEdgeParams.classes + ' eh-ghost eh-ghost-edge' : 'eh-ghost eh-ghost-edge'
      }))

      ghostEdge.style({
        events: 'no'
      })
    })

    ghostEles.merge(ghostNode).merge(ghostEdge)
  }

  ghostNode.position({ x: x, y: y })

  return this
}

module.exports = {
  makeEdges: makeEdges,
makePreview: makePreview,
removePreview: removePreview,
previewShown: previewShown,
  updateEdge: updateEdge,
  handleShown: handleShown,
setHandleFor: setHandleFor,
removeHandle: removeHandle
}
/***/ },
/* 24 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

function disableEdgeEvents () {
  if (this.options.noEdgeEventsInDraw) {
    this.cy.edges().style('events', 'no')
  }

  return this
}

function enableEdgeEvents () {
  if (this.options.noEdgeEventsInDraw) {
    this.cy.edges().style('events', '')
  }

  return this
}

module.exports = { disableEdgeEvents: disableEdgeEvents, enableEdgeEvents: enableEdgeEvents }
/***/ },
/* 25 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

function enable () {
  this.enabled = true

  this.emit('enable')

  return this
}

function disable () {
  this.enabled = false

  this.emit('disable')

  return this
}

module.exports = { enable: enable, disable: disable }
/***/ },
/* 26 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

const memoize = __webpack_require__(30)
const sqrt2 = Math.sqrt(2)
const quadtree = __webpack_require__(10).quadtree

function canStartOn (node) {
  const options = this.options
      const previewEles = this.previewEles
      const ghostEles = this.ghostEles
      const handleNode = this.handleNode

  const isPreview = function isPreview (el) {
    return previewEles.anySame(el)
  }
  const isGhost = function isGhost (el) {
    return ghostEles.anySame(el)
  }
  const userFilter = function userFilter (el) {
    return el.filter(options.handleNodes).length > 0
  }
  const isHandle = function isHandle (el) {
    return handleNode.same(el)
  }
  const isTemp = function isTemp (el) {
    return isPreview(el) || isHandle(el) || isGhost(el)
  }

  const enabled = this.enabled
      const active = this.active
      const grabbingNode = this.grabbingNode

  return enabled && !active && !grabbingNode && (node == null || !isTemp(node) && userFilter(node))
}

function canStartDrawModeOn (node) {
  return this.canStartOn(node) && this.drawMode
}

function canStartNonDrawModeOn (node) {
  return this.canStartOn(node) && !this.drawMode
}

function show (node, position) {
  const options = this.options
      const drawMode = this.drawMode

  if (!this.canStartOn(node) || drawMode && !options.handleInDrawMode) {
    return
  }

  this.sourceNode = node

  this.setHandleFor(node, position)

  this.emit('show', this.hp(), this.sourceNode)

  return this
}

function hide () {
  this.removeHandle()

  this.emit('hide', this.hp(), this.sourceNode)

  return this
}

function start (node) {
  if (!this.canStartOn(node)) {
    return
  }

  this.active = true

  this.sourceNode = node
  this.sourceNode.addClass('eh-source')

  this.disableGestures()
  this.disableEdgeEvents()

  this.emit('start', this.hp(), node)
}

function update (pos) {
  if (!this.active) {
    return
  }

  const p = pos

  this.mx = p.x
  this.my = p.y

  this.updateEdge()
  // this.throttledSnap();

  return this
}

function getClickNode (e) {
  const options = this.options
      const cy = this.cy

  const pos = e.position
  const extent = cy.extent()
  const points = cy.$(options.handleNodes).map(function (e) {
    return {
      position: e.position(),
      id: e.id()
    }
  })
  const qtree = quadtree().x(function (d) {
    return d.position.x
  }).y(function (d) {
    return d.position.y
  }).extent([extent.x1, extent.y1], [extent.x2, extent.y2]).addAll(points)

  const res = qtree.find(pos.x, pos.y, options.snapThreshold)
  if (res) {
    return cy.$id(res.id)
  }
}

function snap () {
  if (!this.active || !this.options.snap) {
    return false
  }

  const cy = this.cy
  const tgt = this.targetNode
  const threshold = this.options.snapThreshold
  const mousePos = this.mp()
  const handleNode = this.handleNode
      const previewEles = this.previewEles
      const ghostNode = this.ghostNode

  const radius = function radius (n) {
    return sqrt2 * Math.max(n.outerWidth(), n.outerHeight()) / 2
  } // worst-case enclosure of bb by circle
  const sqDist = function sqDist (x1, y1, x2, y2) {
    const dx = x2 - x1; const dy = y2 - y1; return dx * dx + dy * dy
  }
  const sqDistByPt = function sqDistByPt (p1, p2) {
    return sqDist(p1.x, p1.y, p2.x, p2.y)
  }
  const nodeSqDist = function nodeSqDist (n) {
    return sqDistByPt(n.position(), mousePos)
  }

  const sqThreshold = function sqThreshold (n) {
    const r = radius(n); const t = r + threshold; return t * t
  }
  const isWithinTheshold = function isWithinTheshold (n) {
    return nodeSqDist(n) <= sqThreshold(n)
  }

  const bbSqDist = function bbSqDist (n) {
    const p = n.position()
    const halfW = n.outerWidth() / 2
    const halfH = n.outerHeight() / 2

    // node and mouse positions, line is formed from node to mouse
    const nx = p.x
    const ny = p.y
    const mx = mousePos.x
    const my = mousePos.y

    // bounding box
    const x1 = nx - halfW
    const x2 = nx + halfW
    const y1 = ny - halfH
    const y2 = ny + halfH

    const insideXBounds = x1 <= mx && mx <= x2
    const insideYBounds = y1 <= my && my <= y2

    if (insideXBounds && insideYBounds) {
      // inside box
      return 0
    } else if (insideXBounds) {
      // perpendicular distance to box, top or bottom
      const dy1 = my - y1
      const dy2 = my - y2

      return Math.min(dy1 * dy1, dy2 * dy2)
    } else if (insideYBounds) {
      // perpendicular distance to box, left or right
      const dx1 = mx - x1
      const dx2 = mx - x2

      return Math.min(dx1 * dx1, dx2 * dx2)
    } else if (mx < x1 && my < y1) {
      // top-left corner distance
      return sqDist(mx, my, x1, y1)
    } else if (mx > x2 && my < y1) {
      // top-right corner distance
      return sqDist(mx, my, x2, y1)
    } else if (mx < x1 && my > y2) {
      // bottom-left corner distance
      return sqDist(mx, my, x1, y2)
    } else {
      // bottom-right corner distance
      return sqDist(mx, my, x2, y2)
    }
  }

  const cmpBbSqDist = function cmpBbSqDist (n1, n2) {
    return bbSqDist(n1) - bbSqDist(n2)
  }

  const cmp = cmpBbSqDist

  const allowHoverDelay = false

  const mouseIsInside = function mouseIsInside (n) {
    const mp = mousePos
    const w = n.outerWidth()
    const halfW = w / 2
    const h = n.outerHeight()
    const halfH = h / 2
    const p = n.position()
    const x1 = p.x - halfW
    const x2 = p.x + halfW
    const y1 = p.y - halfH
    const y2 = p.y + halfH

    return x1 <= mp.x && mp.x <= x2 && y1 <= mp.y && mp.y <= y2
  }

  const isEhEle = function isEhEle (n) {
    return n.same(handleNode) || n.same(previewEles) || n.same(ghostNode)
  }

  const nodesByDist = cy.nodes(function (n) {
    return !isEhEle(n) && isWithinTheshold(n)
  }).sort(cmp)
  let snapped = false

  if (tgt.nonempty() && !isWithinTheshold(tgt)) {
    this.unpreview(tgt)
  }

  for (let i = 0; i < nodesByDist.length; i++) {
    const n = nodesByDist[i]

    // skip a parent node when the mouse is inside it
    if (n.isParent() && mouseIsInside(n)) {
      continue
    }

    // skip a child node when the mouse is not inside the parent
    if (n.isChild() && !mouseIsInside(n.parent())) {
      continue
    }

    if (n.same(tgt) || this.preview(n, allowHoverDelay)) {
      snapped = true
      break
    }
  }

  return snapped
}

function preview (target) {
  const _this = this

  const allowHoverDelay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true
  const options = this.options
      const sourceNode = this.sourceNode
      const ghostNode = this.ghostNode
      const ghostEles = this.ghostEles
      const presumptiveTargets = this.presumptiveTargets
      const previewEles = this.previewEles
      const active = this.active

  const source = sourceNode
  const isLoop = target.same(source)
  const loopAllowed = options.loopAllowed(target)
  const isGhost = target.same(ghostNode)
  const noEdge = !options.edgeType(source, target)
  const isHandle = target.same(this.handleNode)
  const isExistingTgt = target.same(this.targetNode)

  if (!active || isHandle || isGhost || noEdge || isExistingTgt || isLoop && !loopAllowed
  // || (target.isParent())
  ) {
      return false
    }

  if (this.targetNode.nonempty()) {
    this.unpreview(this.targetNode)
  }

  clearTimeout(this.previewTimeout)

  const applyPreview = function applyPreview () {
    _this.targetNode = target

    presumptiveTargets.merge(target)

    target.addClass('eh-presumptive-target')
    target.addClass('eh-target')

    _this.emit('hoverover', _this.mp(), source, target)

    if (options.preview) {
      target.addClass('eh-preview')

      ghostEles.addClass('eh-preview-active')
      sourceNode.addClass('eh-preview-active')
      target.addClass('eh-preview-active')

      _this.makePreview()

      _this.emit('previewon', _this.mp(), source, target, previewEles)
    }
  }

  if (allowHoverDelay && options.hoverDelay > 0) {
    this.previewTimeout = setTimeout(applyPreview, options.hoverDelay)
  } else {
    applyPreview()
  }

  return true
}

function unpreview (target) {
  if (!this.active || target.same(this.handleNode)) {
    return
  }

  const previewTimeout = this.previewTimeout
      const sourceNode = this.sourceNode
      const previewEles = this.previewEles
      const ghostEles = this.ghostEles
      const cy = this.cy

  clearTimeout(previewTimeout)
  this.previewTimeout = null

  const source = sourceNode

  target.removeClass('eh-preview eh-target eh-presumptive-target eh-preview-active')
  ghostEles.removeClass('eh-preview-active')
  sourceNode.removeClass('eh-preview-active')

  this.targetNode = cy.collection()

  this.removePreview(source, target)

  this.emit('hoverout', this.mp(), source, target)
  this.emit('previewoff', this.mp(), source, target, previewEles)

  return this
}

function stop () {
  if (!this.active) {
    return
  }
  // presumptiveTargets 表示推定的节点， ghostEles 表示临时的边和点
  const sourceNode = this.sourceNode
      const targetNode = this.targetNode
      const ghostEles = this.ghostEles
      const presumptiveTargets = this.presumptiveTargets

  clearTimeout(this.previewTimeout)

  sourceNode.removeClass('eh-source')
  targetNode.removeClass('eh-target eh-preview eh-hover')
  presumptiveTargets.removeClass('eh-presumptive-target')

  this.makeEdges()

  this.removeHandle()

  ghostEles.remove()

  this.clearCollections()

  this.resetGestures()
  this.enableEdgeEvents()

  this.active = false

  this.emit('stop', this.mp(), sourceNode)

  return this
}

module.exports = {
  show: show,
hide: hide,
start: start,
update: update,
preview: preview,
unpreview: unpreview,
stop: stop,
snap: snap,
  canStartOn: canStartOn,
canStartDrawModeOn: canStartDrawModeOn,
canStartNonDrawModeOn: canStartNonDrawModeOn,
getClickNode: getClickNode
}
/***/ },
/* 27 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

const defaults = __webpack_require__(21)
const assign = __webpack_require__(1)
const throttle = __webpack_require__(2)

const cyGesturesToggle = __webpack_require__(19)
const cyListeners = __webpack_require__(20)
const drawMode = __webpack_require__(22)
const drawing = __webpack_require__(23)
const enabling = __webpack_require__(25)
const gestureLifecycle = __webpack_require__(26)
const listeners = __webpack_require__(28)
const edgeEvents = __webpack_require__(24)

function Edgehandles (options) {
  const cy = options.cy

  this.cy = cy
  this.listeners = []

  // edgehandles gesture state
  this.enabled = true
  this.drawMode = false
  this.active = false
  this.grabbingNode = false

  // edgehandles elements
  this.handleNode = cy.collection()
  this.clearCollections()

  // handle
  this.hx = 0
  this.hy = 0
  this.hr = 0

  // mouse position
  this.mx = 0
  this.my = 0

  this.options = assign({}, defaults, options)

  this.saveGestureState()
  this.addListeners()

  this.throttledSnap = throttle(this.snap.bind(this), 1000 / options.snapFrequency)

  this.preventDefault = function (e) {
    return e.preventDefault()
  }

  let supportsPassive = false
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get: function get () {
        supportsPassive = true
      }
    })

    window.addEventListener('test', null, opts)
  } catch (err) {}

  if (supportsPassive) {
    this.windowListenerOptions = { capture: true, passive: false }
  } else {
    this.windowListenerOptions = true
  }
}

const proto = Edgehandles.prototype = {}
const extend = function extend (obj) {
  return assign(proto, obj)
}

proto.destroy = function () {
  this.removeListeners()
}

proto.setOptions = function (options) {
  assign(this.options, options)
}

proto.mp = function () {
  return { x: this.mx, y: this.my }
}

proto.hp = function () {
  return { x: this.hx, y: this.hy }
}

proto.clearCollections = function () {
  const cy = this.cy

  this.previewEles = cy.collection()
  this.ghostEles = cy.collection()
  this.ghostNode = cy.collection()
  this.sourceNode = cy.collection()
  this.targetNode = cy.collection()
  this.presumptiveTargets = cy.collection()
};

[cyGesturesToggle, cyListeners, drawMode, drawing, enabling, gestureLifecycle, listeners, edgeEvents].forEach(extend)

module.exports = Edgehandles
/***/ },
/* 28 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

const _typeof = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? function (obj) { return typeof obj } : function (obj) { return obj && typeof Symbol === 'function' && obj.constructor === Symbol && obj !== Symbol.prototype ? 'symbol' : typeof obj }

function addListeners () {
  const _this = this

  this.addCytoscapeListeners()

  this.addListener(this.cy, 'destroy', function () {
    return _this.destroy()
  })

  return this
}

function removeListeners () {
  for (let i = this.listeners.length - 1; i >= 0; i--) {
    const l = this.listeners[i]

    this.removeListener(l.target, l.event, l.selector, l.callback, l.options)
  }

  return this
}

function getListener (target, event, selector, callback, options) {
  if ((typeof selector === 'undefined' ? 'undefined' : _typeof(selector)) !== _typeof('')) {
    callback = selector
    options = callback
    selector = null
  }

  if (options == null) {
    options = false
  }

  return { target: target, event: event, selector: selector, callback: callback, options: options }
}

function isDom (target) {
  return target instanceof Element
}

function addListener (target, event, selector, callback, options) {
  const l = getListener(target, event, selector, callback, options)

  this.listeners.push(l)

  if (isDom(l.target)) {
    l.target.addEventListener(l.event, l.callback, l.options)
  } else {
    if (l.selector) {
      l.target.addListener(l.event, l.selector, l.callback, l.options)
    } else {
      l.target.addListener(l.event, l.callback, l.options)
    }
  }

  return this
}

function removeListener (target, event, selector, callback, options) {
  const l = getListener(target, event, selector, callback, options)

  for (let i = this.listeners.length - 1; i >= 0; i--) {
    const l2 = this.listeners[i]

    if (l.target === l2.target && l.event === l2.event && (l.selector == null || l.selector === l2.selector) && (l.callback == null || l.callback === l2.callback)) {
      this.listeners.splice(i, 1)

      if (isDom(l.target)) {
        l.target.removeEventListener(l.event, l.callback, l.options)
      } else {
        if (l.selector) {
          l.target.removeListener(l.event, l.selector, l.callback, l.options)
        } else {
          l.target.removeListener(l.event, l.callback, l.options)
        }
      }

      break
    }
  }

  return this
}

function emit (type, position) {
  const options = this.options
      const cy = this.cy

  for (var _len = arguments.length, args = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {
    args[_key - 2] = arguments[_key]
  }

  cy.emit({ type: 'eh' + type, position: position }, args)

  const handler = options[type]

  if (handler != null) {
    handler.apply(undefined, args)
  }

  return this
}

module.exports = { addListener: addListener, addListeners: addListeners, removeListener: removeListener, removeListeners: removeListeners, emit: emit }
/***/ },
/* 29 */
/***/ function (module, exports, __webpack_require__) {
'use strict'

const impl = __webpack_require__(4)

// registers the extension on a cytoscape lib ref
const register = function register (cytoscape) {
  if (!cytoscape) {
    return
  } // can't register if cytoscape unspecified

  cytoscape('core', 'edgehandles', impl) // register with cytoscape.js
}

if (typeof cytoscape !== 'undefined') {
  // expose to global cytoscape (i.e. window.cytoscape)
  register(cytoscape) // eslint-disable-line no-undef
}

module.exports = register
/***/ },
/* 30 */
/***/ function (module, exports, __webpack_require__) {
/* WEBPACK VAR INJECTION */(function (global) { /**
 * lodash (Custom Build) <https://lodash.com/>
 * Build: `lodash modularize exports="npm" -o ./`
 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */
/** Used as the `TypeError` message for "Functions" methods. */
const FUNC_ERROR_TEXT = 'Expected a function'

/** Used to stand-in for `undefined` hash values. */
const HASH_UNDEFINED = '__lodash_hash_undefined__'

/** `Object#toString` result references. */
const funcTag = '[object Function]'
    const genTag = '[object GeneratorFunction]'

/**
 * Used to match `RegExp`
 * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).
 */
const reRegExpChar = /[\\^$.*+?()[\]{}|]/g

/** Used to detect host constructors (Safari). */
const reIsHostCtor = /^\[object .+?Constructor\]$/

/** Detect free variable `global` from Node.js. */
const freeGlobal = typeof global === 'object' && global && global.Object === Object && global

/** Detect free variable `self`. */
const freeSelf = typeof self === 'object' && self && self.Object === Object && self

/** Used as a reference to the global object. */
const root = freeGlobal || freeSelf || Function('return this')()

/**
 * Gets the value at `key` of `object`.
 *
 * @private
 * @param {Object} [object] The object to query.
 * @param {string} key The key of the property to get.
 * @returns {*} Returns the property value.
 */
function getValue (object, key) {
  return object == null ? undefined : object[key]
}

/**
 * Checks if `value` is a host object in IE < 9.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a host object, else `false`.
 */
function isHostObject (value) {
  // Many host objects are `Object` objects that can coerce to strings
  // despite having improperly defined `toString` methods.
  let result = false
  if (value != null && typeof value.toString !== 'function') {
    try {
      result = !!(value + '')
    } catch (e) {}
  }
  return result
}

/** Used for built-in method references. */
const arrayProto = Array.prototype
    const funcProto = Function.prototype
    const objectProto = Object.prototype

/** Used to detect overreaching core-js shims. */
const coreJsData = root['__core-js_shared__']

/** Used to detect methods masquerading as native. */
const maskSrcKey = (function () {
  const uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '')
  return uid ? ('Symbol(src)_1.' + uid) : ''
}())

/** Used to resolve the decompiled source of functions. */
const funcToString = funcProto.toString

/** Used to check objects for own properties. */
const hasOwnProperty = objectProto.hasOwnProperty

/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */
const objectToString = objectProto.toString

/** Used to detect if a method is native. */
const reIsNative = RegExp('^' +
  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\$&')
  .replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, '$1.*?') + '$'
)

/** Built-in value references. */
const splice = arrayProto.splice

/* Built-in method references that are verified to be native. */
const Map = getNative(root, 'Map')
    const nativeCreate = getNative(Object, 'create')

/**
 * Creates a hash object.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */
function Hash (entries) {
  let index = -1
      const length = entries ? entries.length : 0

  this.clear()
  while (++index < length) {
    const entry = entries[index]
    this.set(entry[0], entry[1])
  }
}

/**
 * Removes all key-value entries from the hash.
 *
 * @private
 * @name clear
 * @memberOf Hash
 */
function hashClear () {
  this.__data__ = nativeCreate ? nativeCreate(null) : {}
}

/**
 * Removes `key` and its value from the hash.
 *
 * @private
 * @name delete
 * @memberOf Hash
 * @param {Object} hash The hash to modify.
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */
function hashDelete (key) {
  return this.has(key) && delete this.__data__[key]
}

/**
 * Gets the hash value for `key`.
 *
 * @private
 * @name get
 * @memberOf Hash
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */
function hashGet (key) {
  const data = this.__data__
  if (nativeCreate) {
    const result = data[key]
    return result === HASH_UNDEFINED ? undefined : result
  }
  return hasOwnProperty.call(data, key) ? data[key] : undefined
}

/**
 * Checks if a hash value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf Hash
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function hashHas (key) {
  const data = this.__data__
  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key)
}

/**
 * Sets the hash `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf Hash
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the hash instance.
 */
function hashSet (key, value) {
  const data = this.__data__
  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value
  return this
}

// Add methods to `Hash`.
Hash.prototype.clear = hashClear
Hash.prototype.delete = hashDelete
Hash.prototype.get = hashGet
Hash.prototype.has = hashHas
Hash.prototype.set = hashSet

/**
 * Creates an list cache object.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */
function ListCache (entries) {
  let index = -1
      const length = entries ? entries.length : 0

  this.clear()
  while (++index < length) {
    const entry = entries[index]
    this.set(entry[0], entry[1])
  }
}

/**
 * Removes all key-value entries from the list cache.
 *
 * @private
 * @name clear
 * @memberOf ListCache
 */
function listCacheClear () {
  this.__data__ = []
}

/**
 * Removes `key` and its value from the list cache.
 *
 * @private
 * @name delete
 * @memberOf ListCache
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */
function listCacheDelete (key) {
  const data = this.__data__
      const index = assocIndexOf(data, key)

  if (index < 0) {
    return false
  }
  const lastIndex = data.length - 1
  if (index == lastIndex) {
    data.pop()
  } else {
    splice.call(data, index, 1)
  }
  return true
}

/**
 * Gets the list cache value for `key`.
 *
 * @private
 * @name get
 * @memberOf ListCache
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */
function listCacheGet (key) {
  const data = this.__data__
      const index = assocIndexOf(data, key)

  return index < 0 ? undefined : data[index][1]
}

/**
 * Checks if a list cache value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf ListCache
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function listCacheHas (key) {
  return assocIndexOf(this.__data__, key) > -1
}

/**
 * Sets the list cache `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf ListCache
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the list cache instance.
 */
function listCacheSet (key, value) {
  const data = this.__data__
      const index = assocIndexOf(data, key)

  if (index < 0) {
    data.push([key, value])
  } else {
    data[index][1] = value
  }
  return this
}

// Add methods to `ListCache`.
ListCache.prototype.clear = listCacheClear
ListCache.prototype.delete = listCacheDelete
ListCache.prototype.get = listCacheGet
ListCache.prototype.has = listCacheHas
ListCache.prototype.set = listCacheSet

/**
 * Creates a map cache object to store key-value pairs.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */
function MapCache (entries) {
  let index = -1
      const length = entries ? entries.length : 0

  this.clear()
  while (++index < length) {
    const entry = entries[index]
    this.set(entry[0], entry[1])
  }
}

/**
 * Removes all key-value entries from the map.
 *
 * @private
 * @name clear
 * @memberOf MapCache
 */
function mapCacheClear () {
  this.__data__ = {
    hash: new Hash(),
    map: new (Map || ListCache)(),
    string: new Hash()
  }
}

/**
 * Removes `key` and its value from the map.
 *
 * @private
 * @name delete
 * @memberOf MapCache
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */
function mapCacheDelete (key) {
  return getMapData(this, key).delete(key)
}

/**
 * Gets the map value for `key`.
 *
 * @private
 * @name get
 * @memberOf MapCache
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */
function mapCacheGet (key) {
  return getMapData(this, key).get(key)
}

/**
 * Checks if a map value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf MapCache
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function mapCacheHas (key) {
  return getMapData(this, key).has(key)
}

/**
 * Sets the map `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf MapCache
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the map cache instance.
 */
function mapCacheSet (key, value) {
  getMapData(this, key).set(key, value)
  return this
}

// Add methods to `MapCache`.
MapCache.prototype.clear = mapCacheClear
MapCache.prototype.delete = mapCacheDelete
MapCache.prototype.get = mapCacheGet
MapCache.prototype.has = mapCacheHas
MapCache.prototype.set = mapCacheSet

/**
 * Gets the index at which the `key` is found in `array` of key-value pairs.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} key The key to search for.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */
function assocIndexOf (array, key) {
  let length = array.length
  while (length--) {
    if (eq(array[length][0], key)) {
      return length
    }
  }
  return -1
}

/**
 * The base implementation of `_.isNative` without bad shim checks.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a native function,
 *  else `false`.
 */
function baseIsNative (value) {
  if (!isObject(value) || isMasked(value)) {
    return false
  }
  const pattern = (isFunction(value) || isHostObject(value)) ? reIsNative : reIsHostCtor
  return pattern.test(toSource(value))
}

/**
 * Gets the data for `map`.
 *
 * @private
 * @param {Object} map The map to query.
 * @param {string} key The reference key.
 * @returns {*} Returns the map data.
 */
function getMapData (map, key) {
  const data = map.__data__
  return isKeyable(key)
    ? data[typeof key === 'string' ? 'string' : 'hash']
    : data.map
}

/**
 * Gets the native function at `key` of `object`.
 *
 * @private
 * @param {Object} object The object to query.
 * @param {string} key The key of the method to get.
 * @returns {*} Returns the function if it's native, else `undefined`.
 */
function getNative (object, key) {
  const value = getValue(object, key)
  return baseIsNative(value) ? value : undefined
}

/**
 * Checks if `value` is suitable for use as unique object key.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is suitable, else `false`.
 */
function isKeyable (value) {
  const type = typeof value
  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')
    ? (value !== '__proto__')
    : (value === null)
}

/**
 * Checks if `func` has its source masked.
 *
 * @private
 * @param {Function} func The function to check.
 * @returns {boolean} Returns `true` if `func` is masked, else `false`.
 */
function isMasked (func) {
  return !!maskSrcKey && (maskSrcKey in func)
}

/**
 * Converts `func` to its source code.
 *
 * @private
 * @param {Function} func The function to process.
 * @returns {string} Returns the source code.
 */
function toSource (func) {
  if (func != null) {
    try {
      return funcToString.call(func)
    } catch (e) {}
    try {
      return (func + '')
    } catch (e) {}
  }
  return ''
}

/**
 * Creates a function that memoizes the result of `func`. If `resolver` is
 * provided, it determines the cache key for storing the result based on the
 * arguments provided to the memoized function. By default, the first argument
 * provided to the memoized function is used as the map cache key. The `func`
 * is invoked with the `this` binding of the memoized function.
 *
 * **Note:** The cache is exposed as the `cache` property on the memoized
 * function. Its creation may be customized by replacing the `_.memoize.Cache`
 * constructor with one whose instances implement the
 * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)
 * method interface of `delete`, `get`, `has`, and `set`.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Function
 * @param {Function} func The function to have its output memoized.
 * @param {Function} [resolver] The function to resolve the cache key.
 * @returns {Function} Returns the new memoized function.
 * @example
 *
 * var object = { 'a': 1, 'b': 2 };
 * var other = { 'c': 3, 'd': 4 };
 *
 * var values = _.memoize(_.values);
 * values(object);
 * // => [1, 2]
 *
 * values(other);
 * // => [3, 4]
 *
 * object.a = 2;
 * values(object);
 * // => [1, 2]
 *
 * // Modify the result cache.
 * values.cache.set(object, ['a', 'b']);
 * values(object);
 * // => ['a', 'b']
 *
 * // Replace `_.memoize.Cache`.
 * _.memoize.Cache = WeakMap;
 */
function memoize (func, resolver) {
  if (typeof func !== 'function' || (resolver && typeof resolver !== 'function')) {
    throw new TypeError(FUNC_ERROR_TEXT)
  }
  var memoized = function () {
    const args = arguments
        const key = resolver ? resolver.apply(this, args) : args[0]
        const cache = memoized.cache

    if (cache.has(key)) {
      return cache.get(key)
    }
    const result = func.apply(this, args)
    memoized.cache = cache.set(key, result)
    return result
  }
  memoized.cache = new (memoize.Cache || MapCache)()
  return memoized
}

// Assign cache to `_.memoize`.
memoize.Cache = MapCache

/**
 * Performs a
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * comparison between two values to determine if they are equivalent.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * var object = { 'a': 1 };
 * var other = { 'a': 1 };
 *
 * _.eq(object, object);
 * // => true
 *
 * _.eq(object, other);
 * // => false
 *
 * _.eq('a', 'a');
 * // => true
 *
 * _.eq('a', Object('a'));
 * // => false
 *
 * _.eq(NaN, NaN);
 * // => true
 */
function eq (value, other) {
  return value === other || (value !== value && other !== other)
}

/**
 * Checks if `value` is classified as a `Function` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a function, else `false`.
 * @example
 *
 * _.isFunction(_);
 * // => true
 *
 * _.isFunction(/abc/);
 * // => false
 */
function isFunction (value) {
  // The use of `Object#toString` avoids issues with the `typeof` operator
  // in Safari 8-9 which returns 'object' for typed array and other constructors.
  const tag = isObject(value) ? objectToString.call(value) : ''
  return tag == funcTag || tag == genTag
}

/**
 * Checks if `value` is the
 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
 * @example
 *
 * _.isObject({});
 * // => true
 *
 * _.isObject([1, 2, 3]);
 * // => true
 *
 * _.isObject(_.noop);
 * // => true
 *
 * _.isObject(null);
 * // => false
 */
function isObject (value) {
  const type = typeof value
  return !!value && (type == 'object' || type == 'function')
}

module.exports = memoize
/* WEBPACK VAR INJECTION */ }.call(exports, __webpack_require__(3)))
/***/ }
/******/ ])
})
