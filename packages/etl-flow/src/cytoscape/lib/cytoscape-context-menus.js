/* eslint-disable */
(function () {
 'use strict'

  const $ = typeof jQuery === typeof undefined ? null : jQuery

  const register = function (cytoscape, $) {
    if (!cytoscape) { return } // can't register if cytoscape unspecified

    const defaults = {
      // List of initial menu items
      menuItems: [
        /*
        {
          id: 'remove',
          content: 'remove',
          tooltipText: 'remove',
          selector: 'node, edge',
          onClickFunction: function () {
            console.log('remove element');
          },
          hasTrailingDivider: true
        },
        {
          id: 'hide',
          content: 'hide',
          tooltipText: 'remove',
          selector: 'node, edge',
          onClickFunction: function () {
            console.log('hide element');
          },
          disabled: true
        } */
      ],
      // css classes that menu items will have
      menuItemClasses: [
        // add class names to this list
      ],
      // css classes that context menu will have
      contextMenuClasses: [
        // add class names to this list
      ]
    }

    let eventCyTapStart // The event to be binded on tap start

    // To initialize with options.
    cytoscape('core', 'contextMenus', function (opts) {
      const cy = this

      // Initilize scratch pad
      if (!cy.scratch('cycontextmenus')) {
        cy.scratch('cycontextmenus', {})
      }

      let options = getScratchProp('options')
      let $cxtMenu = getScratchProp('cxtMenu')
      const menuItemCSSClass = 'cy-context-menus-cxt-menuitem'
      const dividerCSSClass = 'cy-context-menus-divider'

      setScratchProp('enabled', true)

      // Merge default options with the ones coming from parameter
      function extend (defaults, options) {
        const obj = {}

        for (var i in defaults) {
          obj[i] = defaults[i]
        }

        for (var i in options) {
          obj[i] = options[i]
        }

        return obj
      }

      function getScratchProp (propname) {
        return cy.scratch('cycontextmenus')[propname]
      }

      function setScratchProp (propname, value) {
        cy.scratch('cycontextmenus')[propname] = value
      }

      function preventDefaultContextTap () {
        $('.cy-context-menus-cxt-menu').contextmenu(function () {
            return false
        })
      }

      // Get string representation of css classes
      function getMenuItemClassStr (classes, hasTrailingDivider) {
        let str = getClassStr(classes)

        str += ' ' + menuItemCSSClass

        if (hasTrailingDivider) {
          str += ' ' + dividerCSSClass
        }

        return str
      }

      // Get string representation of css classes
      function getClassStr (classes) {
        let str = ''

        for (let i = 0; i < classes.length; i++) {
          const className = classes[i]
          str += className
          if (i !== classes.length - 1) {
            str += ' '
          }
        }

        return str
      }

      function displayComponent ($component) {
        const id = $component.attr('id')
        if (id === 'markTo') {
          $component.css('display', 'flex')
        } else {
          $component.css('display', 'block')
        }
      }

      function hideComponent ($component) {
        $component.css('display', 'none')
      }

      function hideMenuItemComponents () {
        $cxtMenu.children().css('display', 'none')
      }

      function bindOnClickFunction ($component, onClickFcn) {
        let callOnClickFcn
        const id = $component.attr('id')

        $component.on('click', callOnClickFcn = function (e) {
          const color = $(e.target).data('color')
          if (id === 'markTo') {
            if (color) {
              $(e.target).toggleClass('select').siblings('.mark-color-item').removeClass('select')
              onClickFcn(getScratchProp('currentCyEvent'), color)
            }
          } else {
            onClickFcn(getScratchProp('currentCyEvent'))
          }
        })

        $component.data('call-on-click-function', callOnClickFcn)
      }

      function bindCyCxttap ($component, selector, coreAsWell) {
        const id = $component.attr('id')
        function _cxtfcn (event) {
          if (id === 'markTo') {
            const node = event.target
            if (node && node.isNode && node.isNode()) {
              const markColor = node.data('markColor')
              if (markColor) {
                $component.find(`.mark-color-item[data-color="${markColor}"]`).eq(0).addClass('select').siblings('.mark-color-item').removeClass('select')
              } else {
                $component.find('.mark-color-item').removeClass('select')
              }
            }
          }
          setScratchProp('currentCyEvent', event)
          adjustCxtMenu(event) // adjust the position of context menu
          if ($component.data('show')) {
            // Now we have a visible element display context menu if it is not visible
            if (!$cxtMenu.is(':visible')) {
              displayComponent($cxtMenu)
            }
            // anyVisibleChild indicates if there is any visible child of context menu if not do not show the context menu
            setScratchProp('anyVisibleChild', true)// there is visible child
            displayComponent($component) // display the component
          }

          // If there is no visible element hide the context menu as well(If it is visible)
          if (!getScratchProp('anyVisibleChild') && $cxtMenu.is(':visible')) {
            hideComponent($cxtMenu)
          }
        }

        let cxtfcn
        let cxtCoreFcn

        if (coreAsWell) {
          cy.on('cxttap', cxtCoreFcn = function (event) {
            if (!getScratchProp('enabled')) return
            const target = event.target || event.cyTarget
            if (target != cy) {
              return
            }

            _cxtfcn(event)
          })
        }

        if (selector) {
          cy.on('cxttap', selector, cxtfcn = function (event) {
            if (!getScratchProp('enabled')) return
            _cxtfcn(event)
          })
        }

        // Bind the event to menu item to be able to remove it back
        $component.data('cy-context-menus-cxtfcn', cxtfcn)
        $component.data('cy-context-menus-cxtcorefcn', cxtCoreFcn)
      }

      function bindCyEvents () {
        cy.on('tapstart', eventCyTapStart = function () {
          hideComponent($cxtMenu)
          setScratchProp('cxtMenuPosition', undefined)
          setScratchProp('currentCyEvent', undefined)
        })
      }

      function performBindings ($component, onClickFcn, selector, coreAsWell) {
        bindOnClickFunction($component, onClickFcn)
        bindCyCxttap($component, selector, coreAsWell)
      }

      // Adjusts context menu if necessary
      function adjustCxtMenu (event) {
        const currentCxtMenuPosition = getScratchProp('cxtMenuPosition')
        const cyPos = event.position || event.cyPosition

        if (currentCxtMenuPosition != cyPos) {
          hideMenuItemComponents()
          setScratchProp('anyVisibleChild', false)// we hide all children there is no visible child remaining
          setScratchProp('cxtMenuPosition', cyPos)

          const containerPos = $(cy.container()).offset()
          const renderedPos = event.renderedPosition || event.cyRenderedPosition

          const borderThickness = parseInt($(cy.container()).css('border-width').replace('px', ''))
          if (borderThickness > 0) {
            containerPos.top += borderThickness
            containerPos.left += borderThickness
          }

          // var left = containerPos.left + renderedPos.x;
          // var top = containerPos.top + renderedPos.y;
          // $cxtMenu.css('left', left);
          // $cxtMenu.css('top', top);

          const containerHeight = $(cy.container()).innerHeight()
          const containerWidth = $(cy.container()).innerWidth()

          const horizontalSplit = containerHeight / 2
          const verticalSplit = containerWidth / 2
          const windowHeight = $(window).height()
          const windowWidth = $(window).width()

              // When user click on bottom-left part of window
              if (renderedPos.y > horizontalSplit && renderedPos.x <= verticalSplit) {
                $cxtMenu.css('left', renderedPos.x + containerPos.left)
                $cxtMenu.css('bottom', windowHeight - (containerPos.top + renderedPos.y))
                $cxtMenu.css('right', 'auto')
                $cxtMenu.css('top', 'auto')
              } else if (renderedPos.y > horizontalSplit && renderedPos.x > verticalSplit) {
                // When user click on bottom-right part of window
               $cxtMenu.css('right', windowWidth - (containerPos.left + renderedPos.x))
               $cxtMenu.css('bottom', windowHeight - (containerPos.top + renderedPos.y))
               $cxtMenu.css('left', 'auto')
               $cxtMenu.css('top', 'auto')
              } else if (renderedPos.y <= horizontalSplit && renderedPos.x <= verticalSplit) {
                // When user click on top-left part of window
               $cxtMenu.css('left', renderedPos.x + containerPos.left)
               $cxtMenu.css('top', renderedPos.y + containerPos.top)
               $cxtMenu.css('right', 'auto')
               $cxtMenu.css('bottom', 'auto')
              } else {
                 // When user click on top-right part of window
               $cxtMenu.css('right', windowWidth - (renderedPos.x + containerPos.left))
               $cxtMenu.css('top', renderedPos.y + containerPos.top)
               $cxtMenu.css('left', 'auto')
               $cxtMenu.css('bottom', 'auto')
              }
        }
      }

      function createAndAppendMenuItemComponents (menuItems) {
        for (let i = 0; i < menuItems.length; i++) {
          createAndAppendMenuItemComponent(menuItems[i])
        }
      }

      function createAndAppendMenuItemComponent (menuItem) {
        // Create and append menu item
        const $menuItemComponent = createMenuItemComponent(menuItem)
        appendComponentToCxtMenu($menuItemComponent)

        performBindings($menuItemComponent, menuItem.onClickFunction, menuItem.selector, menuItem.coreAsWell)
      }// insertComponentBeforeExistingItem(component, existingItemID)

      function createAndInsertMenuItemComponentBeforeExistingComponent (menuItem, existingComponentID) {
        // Create and insert menu item
        const $menuItemComponent = createMenuItemComponent(menuItem)
        insertComponentBeforeExistingItem($menuItemComponent, existingComponentID)

        performBindings($menuItemComponent, menuItem.onClickFunction, menuItem.selector, menuItem.coreAsWell)
      }

      // create cxtMenu and append it to body
      function createAndAppendCxtMenuComponent () {
        const classes = getClassStr(options.contextMenuClasses)
//        classes += ' cy-context-menus-cxt-menu';
        $cxtMenu = $('<div class=' + classes + '></div>')
        $cxtMenu.addClass('cy-context-menus-cxt-menu')
        setScratchProp('cxtMenu', $cxtMenu)

        $('body').append($cxtMenu)
        return $cxtMenu
      }

      // Creates a menu item as an html component
      function createMenuItemComponent (item) {
        const classStr = getMenuItemClassStr(options.menuItemClasses, item.hasTrailingDivider)
        if (item.id === 'markTo') {
          var itemStr = `
          <div id="${item.id}" class="${classStr} menu-item-mark-to">
            <span class="mark-content">${item.content}</span>
            <span class="mark-color-item color-blue" data-color="blue"></span>
            <span class="mark-color-item color-green" data-color="green"></span>
            <span class="mark-color-item color-yellow" data-color="yellow"></span>
            <span class="mark-color-item color-red" data-color="red"></span>
            <span class="mark-color-item color-purple" data-color="purple"></span>
          </div>`
        } else {
          var itemStr = '<button id="' + item.id + '" class="' + classStr + '"'

          if (item.tooltipText) {
            itemStr += ' title="' + item.tooltipText + '"'
          }

          if (item.disabled) {
            itemStr += ' disabled'
          }
          if (!item.image) {
              itemStr += '>' + item.content + '</button>'
          } else {
              itemStr += '>' + '<img src="' + item.image.src + '" width="' + item.image.width + 'px"; height="' +
                  item.image.height + 'px"; style="position:absolute; top: ' + item.image.y + 'px; left: ' +
                  item.image.x + 'px;">' + item.content + '</button>'
          }
        }

        const $menuItemComponent = $(itemStr)

        $menuItemComponent.data('selector', item.selector)
        $menuItemComponent.data('on-click-function', item.onClickFunction)
        $menuItemComponent.data('show', (typeof (item.show) === 'undefined' || item.show))

        return $menuItemComponent
      }

      // Appends the given component to cxtMenu
      function appendComponentToCxtMenu (component) {
        $cxtMenu.append(component)
        bindMenuItemClickFunction(component)
      }

      // Insert the given component to cxtMenu just before the existing item with given ID
      function insertComponentBeforeExistingItem (component, existingItemID) {
        const $existingItem = $('#' + existingItemID)
        component.insertBefore($existingItem)
      }

      function destroyCxtMenu () {
        if (!getScratchProp('active')) {
          return
        }

        removeAndUnbindMenuItems()

        cy.off('tapstart', eventCyTapStart)

        $cxtMenu.remove()
        $cxtMenu = undefined
        setScratchProp($cxtMenu, undefined)
        setScratchProp('active', false)
        setScratchProp('anyVisibleChild', false)
      }

      function removeAndUnbindMenuItems () {
        const children = $cxtMenu.children()

        $(children).each(function () {
          removeAndUnbindMenuItem($(this))
        })
      }

      function removeAndUnbindMenuItem (itemID) {
        const $component = typeof itemID === 'string' ? $('#' + itemID) : itemID
        const cxtfcn = $component.data('cy-context-menus-cxtfcn')
        const selector = $component.data('selector')
        const callOnClickFcn = $component.data('call-on-click-function')
        const cxtCoreFcn = $component.data('cy-context-menus-cxtcorefcn')

        if (cxtfcn) {
          cy.off('cxttap', selector, cxtfcn)
        }

        if (cxtCoreFcn) {
          cy.off('cxttap', cxtCoreFcn)
        }

        if (callOnClickFcn) {
          $component.off('click', callOnClickFcn)
        }

        $component.remove()
      }

      function moveBeforeOtherMenuItemComponent (componentID, existingComponentID) {
        if (componentID === existingComponentID) {
          return
        }

        const $component = $('#' + componentID).detach()
        const $existingComponent = $('#' + existingComponentID)

        $component.insertBefore($existingComponent)
      }

      function bindMenuItemClickFunction (component) {
        component.click(function () {
            hideComponent($cxtMenu)
            setScratchProp('cxtMenuPosition', undefined)
        })
      }

      function disableComponent (componentID) {
        $('#' + componentID).attr('disabled', true)
      }

      function enableComponent (componentID) {
        $('#' + componentID).attr('disabled', false)
      }

      function setTrailingDivider (componentID, status) {
        const $component = $('#' + componentID)
        if (status) {
          $component.addClass(dividerCSSClass)
        } else {
          $component.removeClass(dividerCSSClass)
        }
      }

      function setEnable (enable) {
        setScratchProp('enabled', enable)
      }

      // Get an extension instance to enable users to access extension methods
      function getInstance (cy) {
        const instance = {
          // Returns whether the extension is active
         isActive: function () {
           return getScratchProp('active')
         },
         // Appends given menu item to the menu items list.
         appendMenuItem: function (item) {
           createAndAppendMenuItemComponent(item)
           return cy
         },
         // Appends menu items in the given list to the menu items list.
         appendMenuItems: function (items) {
           createAndAppendMenuItemComponents(items)
           return cy
         },
         // Removes the menu item with given ID.
         removeMenuItem: function (itemID) {
           removeAndUnbindMenuItem(itemID)
           return cy
         },
         // Sets whether the menuItem with given ID will have a following divider.
         setTrailingDivider: function (itemID, status) {
           setTrailingDivider(itemID, status)
           return cy
         },
         // Inserts given item before the existingitem.
         insertBeforeMenuItem: function (item, existingItemID) {
           createAndInsertMenuItemComponentBeforeExistingComponent(item, existingItemID)
           return cy
         },
         // Moves the item with given ID before the existingitem.
         moveBeforeOtherMenuItem: function (itemID, existingItemID) {
           moveBeforeOtherMenuItemComponent(itemID, existingItemID)
           return cy
         },
         // Disables the menu item with given ID.
         disableMenuItem: function (itemID) {
           disableComponent(itemID)
           return cy
         },
         // Enables the menu item with given ID.
         enableMenuItem: function (itemID) {
           enableComponent(itemID)
           return cy
         },
         // Disables the menu item with given ID.
         hideMenuItem: function (itemID) {
           $('#' + itemID).data('show', false)
           hideComponent($('#' + itemID))
           return cy
         },
         // Enables the menu item with given ID.
         showMenuItem: function (itemID) {
           $('#' + itemID).data('show', true)
           displayComponent($('#' + itemID))
           return cy
         },
         // Destroys the extension instance
         destroy: function () {
           destroyCxtMenu()
           return cy
         },
         enable: function () {
          setEnable(true)
          return cy
         },
         disable: function () {
           setEnable(false)
           return cy
         },
         show: function () {
          displayComponent($cxtMenu)
          return cy
         },
         hide: function () {
          hideComponent($cxtMenu)
          return cy
         }
        }

        return instance
      }

      if (opts !== 'get') {
        // merge the options with default ones
        options = extend(defaults, opts)
        setScratchProp('options', options)

        // Clear old context menu if needed
        if (getScratchProp('active')) {
          destroyCxtMenu()
        }

        setScratchProp('active', true)

        $cxtMenu = createAndAppendCxtMenuComponent()

        const menuItems = options.menuItems
        createAndAppendMenuItemComponents(menuItems)

        bindCyEvents()
        preventDefaultContextTap()
      }

      return getInstance(this)
    })
  }

  if (typeof module !== 'undefined' && module.exports) { // expose as a commonjs module
    module.exports = register
  }

  if (typeof define !== 'undefined' && define.amd) { // expose as an amd/requirejs module
    define('cytoscape-context-menus', function () {
      return register
    })
  }

  if (typeof cytoscape !== 'undefined' && $) { // expose to global cytoscape (i.e. window.cytoscape)
    register(cytoscape, $)
  }
})()
