.node-html-label-container {
  position: relative;
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .node-html-label-content {
    margin-top: 4px;
    cursor: auto;
    &:first-child {
      margin-top: 0;
    }
    &.content--comment {
      max-width: 180px;
      font-size: 0;
      border-radius: 8px;
      background: transparent;
      padding: 4px 8px;
      .node-html-label-span {
        display: inline-block;
        max-width: 100%;
        font-size: 12px;
        line-height: 16px;
        color: rgba(15, 34, 67, 0.72);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &:hover {
        background: #EEF0F4;
        .node-html-label-span {
          white-space: normal;
        }
      }
    }
    &.content--datacount {
      margin-top: 0;
      pointer-events: none;
      .node-html-label-span {
        font-style: normal;
        font-weight: normal;
        font-size: 12px;
        line-height: 20px;
        color: #1F71FF;
        background: rgba(43, 121, 255, 0.1);
        padding: 0 4px;
        border-radius: 4px;
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        -ms-border-radius: 4px;
        -o-border-radius: 4px;
      }
    }
    &.content--debug {
      position: relative;
      min-width: 60px;
      max-width: 220px;
      margin-top: 6px;
      background: #FFE5E8;
      border-radius: 6px;
      padding: 6px 8px;
      font-size: 0;
      transform: translateX(-16px) translateX(50%);
      .label-box {
        font-size: 0;
        .node-html-label-span {
          display: inline-block;
          max-width: 100%;
          font-size: 12px;
          line-height: 16px;
          color: #FF5266;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      &:hover {
        max-width: 256px;
      }
      &:hover .label-box .node-html-label-span {
        white-space: normal;
      }
      &:hover .label-box + .download-box {
        display: flex;
      }
      .download-box {
        display: none;
        align-items: center;
        width: fit-content;
        font-size: 12px;
        line-height: 16px;
        user-select: none;
        margin-top: 4px;
        &:hover {
          opacity: 0.85;
        }
        .debug-download-btn {
          color: #1F71FF;
          text-decoration: none;
          cursor: pointer;
          vertical-align: top;
        }
      }
      &::before {
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        background: #FFE5E8;
        position: absolute;
        left: 12px;
        top: 0;
        transform: translateY(-4px) rotate(45deg);
        -webkit-transform: translateY(-4px) rotate(45deg);
        -moz-transform: translateY(-4px) rotate(45deg);
        -ms-transform: translateY(-4px) rotate(45deg);
        -o-transform: translateY(-4px) rotate(45deg);
      }
    }
  }
}