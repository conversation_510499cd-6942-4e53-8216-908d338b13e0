.cy-context-menus-cxt-menu {
    display:none;
    z-index:2001;
    position:absolute;
    /* border:1px solid #A0A0A0; */
    padding: 8px 0;
    margin: 0;
    width:auto;
    // min-width: 172px;
    background: #FFFFFF;
    /* light/Container L2 */
    box-shadow: 0px 0px 8px rgba(16, 48, 102, 0.03), 0px 12px 24px rgba(16, 48, 102, 0.16);
    border-radius: 8px;
}

.cy-context-menus-cxt-menuitem {
    display:block;
    z-index:1000;
    box-sizing: border-box;
    width: 100%;
    height: 32px;
    padding: 3px 20px;
    position:relative;
    margin:0;
    background:#FFFFFF;
    font-weight:normal;
    font-size: 12px;
    white-space:nowrap;
    border: 0;
    text-align: left;
}

.cy-context-menus-cxt-menuitem:enabled {
    color:rgba(15, 34, 67, 0.72);
}

.cy-context-menus-ctx-operation:focus {
  outline: none;
}

.cy-context-menus-cxt-menuitem:hover:not(.menu-item-mark-to) {
    color:rgba(15, 34, 67, 0.72);
    text-decoration: none;
    background: rgba(86, 98, 118, 0.05);
    cursor: pointer;
}

.cy-context-menus-cxt-menuitem[content]:before {
    content:attr(content);
}

.cy-context-menus-cxt-menuitem.cy-context-menus-divider {
    border-bottom: 1px solid rgba(15, 34, 67, 0.11);
}

.cy-context-menus-cxt-menuitem.menu-item-mark-to {
    display: flex;
    align-items: center;
    .mark-content {
        margin-right: 8px;
    }
    .mark-color-item {
        display: inline-block;
        width: 12px;
        height: 12px;
        font-size: 0;
        margin-right: 8px;
        border-radius: 50%;
        -webkit-border-radius: 50%;
        -moz-border-radius: 50%;
        -ms-border-radius: 50%;
        -o-border-radius: 50%;
        cursor: pointer;
        &.color-blue {
            background: #0099EB;
            &:hover {
                box-shadow: 0 0 0 2px #0099EB;
            }
            &.select {
                box-shadow: 0 0 0 2px #fff, 0 0 0 4px #0099EB, 0 0 0 7px rgba(31, 113, 255, 0.15);
            }
        }
        &.color-green {
            background: #239545;
            &:hover {
                box-shadow: 0 0 0 2px #239545;
            }
            &.select {
                box-shadow: 0 0 0 2px #fff, 0 0 0 4px #239545, 0 0 0 7px rgba(35, 149, 69, 0.15);
            }
        }
        &.color-yellow {
            background:  #F5CD00;
            &:hover {
                box-shadow: 0 0 0 2px #F5CD00;
            }
            &.select {
                box-shadow: 0 0 0 2px #fff, 0 0 0 4px #F5CD00, 0 0 0 7px rgba(245, 205, 0, 0.15);
            }
        }
        &.color-red {
            background: #FF5266;
            &:hover {
                box-shadow: 0 0 0 2px #FF5266;
            }
            &.select {
                box-shadow: 0 0 0 2px #fff, 0 0 0 4px #FF5266, 0 0 0 7px rgba(255, 82, 102, 0.15);
            }
        }
        &.color-purple {
            background: #9646FF;
            &:hover {
                box-shadow: 0 0 0 2px #9646FF;
            }
            &.select {
                box-shadow: 0 0 0 2px #fff, 0 0 0 4px #9646FF, 0 0 0 7px rgba(150, 70, 255, 0.15);
            }
        }
    }
}

.cy-context-menus-divider {
  border-bottom:1px solid #A0A0A0;
}
