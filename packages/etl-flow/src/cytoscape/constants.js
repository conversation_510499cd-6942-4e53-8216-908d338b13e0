/* eslint-disable */
import keymirror from 'keymirror'
let allConfig
try {
  allConfig = JSON.parse(window.localStorage.getItem('All_CONFIG'))
} catch (e) {

}
// 节点类型
export const NODE_TYPES = keymirror({
    output: null,
    input: null,
    streaming_output: null,
    increment_output: null,
    alter_field: null,
    data_aggr: null,
    streaming_data_aggr: null,
    data_filter: null,
    id_trans: null,
    add_field: null,
    map_field: null,
    select: null,
    table: null,
    deduplication: null,
    machine_learning: null,
    addr_trans: null,
    join: null,
    left_join: null,
    anti_join: null,
    full_join: null,
    union: null,
    sql: null,
    'more-operation': null,
    jdbc: null,
    customize: null,
    json: null,
    number_handle: null,
    date_handle: null,
    string_handle: null,
    vacancy_rate: null
})

// 节点状态
// 0-默认；1-完成；2-错误；3-更新中；4-空表；8-临时锁定；9-永久锁定；
export const NODE_STATUS = {
  default: 0,
  complete: 1,
  error: 2,
  updating: 3,
  empty: 4,
  temporary_lock: 8,
  permanent_lock: 9,
  no_permission: 10
}

// 线类型
export const LINE_TYPES = keymirror({
  straight: null,
  polyline: null,
  arc: null,
  bezier: null
})

// 线样式
export const LINE_STYLES = keymirror({
  dot: null,
  solid: null
})

export const DEFAULT_COLOR = {
  line_color: '#C6CAD2'
}

export const CURSOR_TYPES = {
  auto: 'auto',
  default: 'default',
  none: 'none',
  contextMenu: 'context-menu',
  help: 'help',
  pointer: 'pointer',
  progress: 'progress',
  wait: 'wait',
  crosshair: 'crosshair',
  move: 'move',
  grab: 'grab'
}

// 自动定位的边距
export const FIT_PADDING = 10

function getNodeSize () {
  const baseSize = 48
  if (allConfig && allConfig.mode_node_size_ratio && allConfig.mode_node_size_ratio > 0) {
    return baseSize * allConfig.mode_node_size_ratio
  }
  return baseSize
}

// chart 默认配置
export const DefaultChartConfig = {
  minZoom: 0.25, // 最小缩放比
  maxZoom: 10, // 最大缩放比
  fitMaxZoom: 1.05, // 自动定位时的最大缩放比，为了限制结点个数少时放大比例过大
  nodeSize: getNodeSize(), // 48px * 48px
  nodeFontSize: (allConfig && allConfig.mode_node_font_size) || 14
}
