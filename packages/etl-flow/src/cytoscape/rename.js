import './rename.scss'
import Event from './event'
import { bindAll } from './chart-util'

export default class ReNameControl extends Event {
  constructor (parentEle, options = {}) {
    super()
    if (typeof parentEle === 'string') {
      this._parentEle = document.querySelector('#' + parentEle) || document.querySelector('.' + parentEle)
    } else {
      this._parentEle = parentEle
    }
    this.options = Object.assign({}, options)
    this._text = ''
    this._position = {
      left: 0,
      top: 0
    }

    bindAll(['confirmEventHandler', 'cancelEventHandler', 'handleKeyDown'], this)

    this._init()
  }

  _init () {
    const containerEle = this._container = document.createElement('div')
    containerEle.classList.add('cy-rename-control-container')
    containerEle.innerHTML = `
      <div class="row input-div">
        <input autofocus spellcheck="false" class="input" type="text">
      </div>
      <div class="row operate-div">
        <div class="btn-contanier">
          <button class="btn btn-confirm"></button>
          <button class="btn btn-cancel"></button>
        <div>
      </div>
    `

    this._inputEle = containerEle.querySelector('.input')
    this._confirmBtnEle = containerEle.querySelector('.btn-confirm')
    this._cancelBtnEle = containerEle.querySelector('.btn-cancel')

    document.addEventListener('keydown', this.handleKeyDown, false)
    this._inputEle.addEventListener('click', e => e.stopPropagation(), true)
    this._container.addEventListener('mousedown', e => e.stopPropagation())
    this._container.addEventListener('mousemove', e => e.stopPropagation())
    this._container.addEventListener('mouseup', e => e.stopPropagation())
    this._confirmBtnEle.addEventListener('click', this.confirmEventHandler, false)
    this._cancelBtnEle.addEventListener('click', this.cancelEventHandler, false)

    this._parentEle && this._parentEle.appendChild(containerEle)
  }

  setText (value) {
    if (this._inputEle) {
      this._inputEle.value = value
      this._text = value
    }
  }

  setPosition (position, zoom) {
    if (position === undefined) {
      this._container.style.display = 'none'
    } else {
      let { x: left, y: top } = position
      left -= 120 / 2
      // top += (24+8);
      this._container.style.left = left + 'px'
      this._container.style.top = top + 'px'
      this._container.style.display = 'flex'
      // this._container.style.transform = `translate(0, ${ zoom > 1 ? 12 * zoom : 0 }px) scale(${ Math.max(zoom, 1) })`;
      this._inputEle.focus()
    }
  }

  isShow () {
    return this._container.style.display !== 'none' && this._container.style.display !== ''
  }

  destroy () {
    document.removeEventListener('keydown', this.handleKeyDown, false)
    this._confirmBtnEle.removeEventListener('click', this.confirmEventHandler, false)
    this._cancelBtnEle.removeEventListener('click', this.cancelEventHandler, false)
    this.off()
    if (this._container && this._container.parentNode) {
      this._container.parentNode.removeChild(this._container)
      this._container = null
    }
  }

  confirmEventHandler () {
    this._text = this._inputEle.value.trim()
    this.emit('confirm', this._text)
  }

  cancelEventHandler () {
    this.emit('cancel')
  }

  handleKeyDown (e) {
    if (!this.isShow()) { return }
    if (e.keyCode === 13) { // 回车键
      this.confirmEventHandler()
    } else if (e.keyCode === 27) { // ESC 键
      this.cancelEventHandler()
    }
  }
}
