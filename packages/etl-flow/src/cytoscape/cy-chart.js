/* eslint-disable */
import * as $ from 'jquery'
import _, { keys } from 'lodash'
import Event from './event'
import cytoscape from './lib/cytoscape.umd'
import edgehandles from './lib/cytoscape-edgehandles'
import klay from './lib/cytoscape-klay'
import './lib/cytoscape-context-menus.scss'
import contextMenus from './lib/cytoscape-context-menus'
import './lib/cytoscape-navigator.scss'
import navigator from './lib/cytoscape-navigator'
import getCytoscapeStyle from './cy-style'
import { createNodeElement, createStatusNodeElement, createEdgeElement, isNodeCanLineTo, getEdgeType, bindAll, haveStatusNode, isStreamingInput } from './chart-util'
import { NODE_TYPES, NODE_STATUS, LINE_TYPES, CURSOR_TYPES, FIT_PADDING, DefaultChartConfig, DEFAULT_COLOR } from './constants'
import ReNameControl from './rename'
import nodeHtmlLabel from './lib/cytoscape-node-html-label'
import './lib/cytoscape-node-html-label.scss'
import './lib/cy.scss'
// import testData from './data_demo';

cytoscape.use(edgehandles)
cytoscape.use(klay)
cytoscape.use(contextMenus, $)
cytoscape.use(navigator)
cytoscape.use(nodeHtmlLabel)

const etlType = 'etlModel'

const DefaultDataObj = {
  position: '0 0',
  zoom: 1,
  line_type: 'bezier', // "straight(直线) | polyline(折线) | arc(弧线) | bezier(曲线)",
  line_color: DEFAULT_COLOR.line_color,
  is_trigger_auto_layout: false,
  is_comment: true, // 是否开启注释
  node_meta: [],
  line_meta: []
}

// eslint-disable-next-line no-unused-expressions
!(function (global) {
  let instance = null
  class CytoscapeChart extends Event {
    constructor (etlInstance) {
      // 单例模式
      if (instance) {
        instance.etlInstance = etlInstance
        return instance
      }
      super()
      this.etlInstance = etlInstance

      this._data = null
      this.cy = null
      this.isFirstHighlight = true // 标记是否首次使用高亮
      bindAll(['_onKeyUpEvent'], this)

      CytoscapeChart.setCommonlyUse = e => {
        // console.log('favUpdate', e, this);
        // {"optType":"table","opt_type":"table","name":"表结构处理","type":15,"close":true,"opt_id":"opt15","noShow":true,"opt_desc":"对父节点设置输出字段，修改字段名称及顺序","commonlyUse":true}
        if (this.cy == null) { return }
        const type = e.optType || e.opt_Type
        if (type === NODE_TYPES.customize) {
          this.cy.nodes(`.node_main[opt_id="${e.opt_id}"]`).forEach(n => n.data('commonlyUse', !!e.commonlyUse))
          // this.cy.$(`#${e.opt_id}`).data('commonlyUse', !!e.commonlyUse);
        } else {
          this.cy.nodes(`.node_main[type="${type}"]`).forEach(n => n.data('commonlyUse', !!e.commonlyUse))
        }
      }
      instance = this
    }

    initChart (data = {}, options) {
      this.destroyChart()
      // console.log('initChart', this);
      options = Object.assign({}, { dom: 'myDiagramDiv', mode: 'edit' }, options)
      // this._data = Object.assign({}, DefaultDataObj, data);
      this.assignData(DefaultDataObj, data)
      // 旧版兼容，如果是旧版，连线默认是折线，新版是曲线
      if (!data.hasOwnProperty('line_type') && Object.keys(data).length) {
        this._data.line_type = 'polyline'
      }
      this._mode = options.mode
      this.is_debug = options.is_debug === 1 // 调试模式标志
      this.is_version = options.is_version // 是否是查看版本模式
      const dom = typeof options.dom === 'string' ? document.getElementById(options.dom) : (options.dom instanceof HTMLElement ? options.dom : undefined)
      if (dom == undefined) {
        throw new Error('未获取到画布dom！！！')
      }

      const position = this._data.position.split(' ').map(e => Number(e))

      this.cy = cytoscape({

        // common option
        container: dom,
        elements: this.convertDataToElements(this._data),
        layout: {
          name: 'preset',
          fit: false, // 自动定位到全局视图，会覆盖 zoom、pan属性
          padding: FIT_PADDING
        },
        style: getCytoscapeStyle(this._data),

        // initial viewport state:
        zoom: this._data.zoom,
        pan: { x: position[0], y: position[1] },

        // interaction options:
        minZoom: DefaultChartConfig.minZoom, // 1e-50,
        maxZoom: DefaultChartConfig.maxZoom, // 1e50
        zoomingEnabled: true,
        userZoomingEnabled: true,
        panningEnabled: true,
        userPanningEnabled: true,
        boxSelectionEnabled: false,
        selectionType: 'single',
        touchTapThreshold: 8,
        desktopTapThreshold: 4,
        autolock: false,
        autoungrabify: false,
        autounselectify: false,

        // rendering options:
        headless: false,
        styleEnabled: true,
        hideEdgesOnViewport: false,
        textureOnViewport: false,
        motionBlur: false,
        motionBlurOpacity: 0.2,
        // wheelSensitivity: 1,
        // TODO: 低倍屏下字体模糊问题临时方案
        pixelRatio: 2
      })

      this.cy.ready(e => {
        this._cyReady(e)
      })
    }

    destroyChart () {
      if (this._eh) {
        this._eh.destroy()
        this._eh = null
      }
      if (this._ctxMenuInstance) {
        this._ctxMenuInstance.destroy()
        this._ctxMenuInstance = null
      }
      if (this._nav) {
        this._nav.destroy()
        this._nav = null
      }
      if (this.etlInstance) {
        this.etlInstance.hideTooltip()
        // this.etlInstance = null;
      }
      if (this._renameControl) {
        this._renameControl.destroy()
        this._renameControl = null
      }
      if (this.favoriteOptMap) {
        this.favoriteOptMap = null
      }
      if (this.currentHoverTarget) {
        this.currentHoverTarget = null
      }
      if (this.currentRenameNode) {
        this.currentRenameNode = null
      }
      if (this._preLayoutData) {
        this._preLayoutData = null
      }
      if (this._deleteBtnElement) {
        this._deleteBtnElement = null
      }
      if (this._nodeHtmlInstance) {
        this._nodeHtmlInstance = null
      }
      if (this.cy) {
        this.cy.removeAllListeners()
        this.cy.removeData()
        this.cy.removeScratch()
        this.cy.destroy()
        this.cy = null
      }
      document.removeEventListener('keyup', this._onKeyUpEvent)
      this.off()
      this._data = null
    }

    updateChart (data) {
      // console.log('updateChart', data, this);

      // this._data = Object.assign({}, this._data, data);
      this.assignData(this._data, data)

      this.cy.resize()
      this.cy.remove('*')

      const elements = this.convertDataToElements(this._data)
      this.cy.add(elements)

      this.updateLineType(this._data.line_type)
      this.updateLineColor(this._data.line_color)
      // this._initStatusNodes();
      this.highlightPath()
      this.updateView()
      // if (this._data.is_trigger_auto_layout) {
      //   this.setLayout();
      // }
      if (this.cy.nodes().length) {
        this._nav && this._nav.show()
      }
    }

    updateView () {
      const zoom = this._data.zoom
      const pos = this._data.position.split(' ').map(e => Number(e))
      const position = { x: pos[0], y: pos[1] }
      this.cy.zoom(zoom)
      this.cy.pan(position)
      // let posEle = this.cy.nodes(`.${POSITION_NODE_CLASS}`);
      // if (!posEle.length) {
      //   posEle = this.cy.add({
      //     group: 'nodes',
      //     position: position,
      //     classes: [POSITION_NODE_CLASS]
      //   });
      // }
      // posEle.position(position);
      // this.cy.zoom(zoom);
      // this.cy.center(posEle);
    }

    assignData (oldData = {}, newData = {}) {
      this._data = Object.assign({}, oldData)
      Object.keys(newData).forEach(key => {
        if (newData[key] != null) {
          this._data[key] = newData[key]
        }
      })
    }

    getChartData () {
      return Object.assign({}, this._data, {
        // node_meta: this.cy.nodes('.node_main').map(e => e.json().data),
        // line_meta: this.cy.edges('.edge_main').map(e => e.json().data)
        node_meta: this.cy.nodes('.node_main').map(e => {
          e.data('loc', `${e.position('x')} ${e.position('y')}`)
          return e.json().data
        }),
        line_meta: this.cy.edges('.edge_main').map(e => e.json().data)
      })
    }

    updateMode (modeName) {
      if (modeName === 'read') {
        // 预览模式
        this.cy.autolock(true) // 禁止拖动
        this._eh && this._eh.disable() // 禁止连线
        this._ctxMenuInstance && this._ctxMenuInstance.destroy()// 禁用右键
        // this._nav && this._nav.destroy();
      } else {
        this.cy.autolock(false)
        this._eh && this._eh.enable()
        this._initContextMenus()
        // this._initNavigator();
      }
      this._mode = modeName
    }

    updateLineType (lineType = LINE_TYPES.straight) {
      // console.log('updateLineType', lineType);
      this.cy.edges().removeClass('line_type_layout')
      const currentLineType = this.cy.data('currentLineType')
      if (currentLineType) {
        this.cy.edges().removeClass(`line_type_${currentLineType}`)
      }
      this.cy.edges().addClass(`line_type_${lineType}`)

      // 更新data
      this.cy.data('currentLineType', lineType)
      this._data.line_type = lineType

      // 触发事件
      this.emit('updateLineType', lineType)
    }

    updateLineColor (color) {
      // this.cy.edges().style('line-color', color);
      // this.cy.edges().style('source-arrow-color', color);
      // this.cy.edges().style('target-arrow-color', color);
      // 更新data
      this.cy.data('line_color', color)
      this._data.line_color = color
      // 更新样式
      this.cy.style().selector('edge').update()
      // 触发事件
      // this.emit('updateLineColor', color);
    }

    addNode (data) {
      // console.log('addNode', data);
      const newNode = this.cy.add(createNodeElement(data, true))
      this.addStatusNode(newNode)

      // 更新navigator
      if (this.cy.nodes().length) {
        this._nav && this._nav.show()
      }
      this._nav && this._nav.updateImage()
    }

    copyNode (node) {
      const data = JSON.parse(JSON.stringify(node.data()))
      const pos = node.renderedPosition()
      const copyX = pos.x + DefaultChartConfig.nodeSize * 1.5
      const copyY = pos.y
      data.loc = `${copyX} ${copyY}`
      // data.key = data.key + 'copy';
      // console.log('copyNode', data);
      this.emit('copyNode', data)
      // this.addNode(data);
    }

    addEdge (data) {
      const newEdge = createEdgeElement(data)
      const currentLineType = this.cy.data('currentLineType')
      const edgeElement = this.cy.add(newEdge).addClass(`line_type_${currentLineType}`)
      // edgeElement.style('line-color', this._data.line_color);
      // edgeElement.style('source-arrow-color', this._data.line_color);
      // edgeElement.style('target-arrow-color', this._data.line_color);
      // console.log('add edge:', edgeElement);
      return edgeElement
    }

    removeNode (n) {
      // 删除点要删除关联的节点的 parents
      const updateParents = (e) => {
        const targets = e.outgoers('edge').map(e => e.target())
        targets.forEach(n => {
          const parents = n.data('parents')
          const index = parents.findIndex(key => key === e.data('key'))
          if (index !== -1) { parents.splice(index, 1) }
          n.data('parents', parents)
        })
      }
      let node
      if (n.isNode && n.isNode()) {
        node = n
      } else {
        node = this.cy.$id(`${n.id || n.key}`)
      }
      if (node) {
        // 更新 parents 数组
        updateParents(node)
        // 先删除状态节点
        this.cy.$id(`${node.id()}_status_node`).remove()
        // 删除节点
        node.remove()
      }

      // 更新navigator
      if (!this.cy.nodes().length) {
        this._nav && this._nav.hide()
      } else {
        this._nav && this._nav.updateImage()
      }
    }

    removeEdge (edge) {
      // 更新 target 点的 parents 数组
      const sourceNode = edge.source()
      const targetNode = edge.target()
      const parents = targetNode.data('parents')
      const index = parents.findIndex(key => key === sourceNode.data('key'))
      if (index !== -1) {
        parents.splice(index, 1)
        targetNode.data('parents', parents)
      }
      // 删除边
      edge.remove()
    }

    addStatusNode (mainNode) {
      if (haveStatusNode(mainNode)) {
        const statusNode = createStatusNodeElement(mainNode)
        this.cy.add(statusNode)
        mainNode.data('cid', statusNode.data.id)
      }
    }

    updateStatusNode () {
      // 更新状态节点
      this.cy.nodes('.node_main').forEach(nodeElement => {
        if (haveStatusNode(nodeElement)) {
          const child_nodes = this.cy.$id(nodeElement.data('cid'))
          child_nodes.forEach(child => {
            child.position({
              x: nodeElement.position().x + nodeElement.width() / 2,
              y: nodeElement.position().y - nodeElement.height() / 2
            })
          })
        }
      })
   }

   updateNodeAttr (id, attr, value) {
     const node = this.cy.$id(id)
     if (!node || !node.isNode()) { return }
     node.data(attr, value)
     // 如果改变的注释属性，需要更新底部标签值
     if (attr === 'comment') {
       this._nodeHtmlInstance && this._nodeHtmlInstance.updateData(node)
     }
   }

   showDeleteBtn (evt) {
     const pos = evt.renderedPosition
     const container = this.cy.container()
     if (!this._deleteBtnElement) {
       const ele = document.createElement('div')
       ele.classList.add('cy-delete-edge-btn')
       ele.targetEdge = evt.target
       ele.addEventListener('click', e => {
        //  console.log('showDeleteBtn---', e.target.targetEdge.json().data);
         this._deleteHandler(e.target.targetEdge)
         this.hideDeleteBtn()
       })
       container.appendChild(ele)
       this._deleteBtnElement = ele
     }
     this._deleteBtnElement.targetEdge = evt.target
     this._deleteBtnElement.style.display = 'block'
     this._deleteBtnElement.style.left = pos.x + 'px'
     this._deleteBtnElement.style.top = pos.y + 'px'
   }

   hideDeleteBtn () {
     if (this._deleteBtnElement) {
       this._deleteBtnElement.style.display = 'none'
     }
   }

    setLayout (fit = true) {
      this._preLayoutData = this.getChartData()
      this._preLayoutLineType = this.cy.data('currentLineType')
      const options = {
        name: 'klay',
        fit: fit,
        padding: FIT_PADDING,
        nodeDimensionsIncludeLabels: false,
        klay: {
            // Following descriptions taken from http://layout.rtsys.informatik.uni-kiel.de:9444/    Providedlayout.html?algorithm=de.cau.cs.kieler.klay.layered
            addUnnecessaryBendpoints: true, // false Adds bend points(拐点) even if an edge does not change     direction.
            aspectRatio: this.cy.width() / this.cy.height(), // 1.6, // The aimed aspect ratio of the drawing,     that is the quotient of width by height
            borderSpacing: 20, // Minimal amount of space to be left to the border
            compactComponents: false, // Tries to further compact components (disconnected sub-graphs).
            crossingMinimization: 'LAYER_SWEEP', // Strategy for crossing minimization.
            /* LAYER_SWEEP The layer sweep algorithm iterates multiple times over the layers, trying to find node         orderings that minimize the number of crossings. The algorithm uses randomization to increase the     odds of     finding a good result. To improve its results, consider increasing the Thoroughness option,     which influences     the number of iterations done. The Randomization seed also influences results.
            INTERACTIVE Orders the nodes of each layer by comparing their positions before the layout algorithm     was     started. The idea is that the relative order of nodes as it was before layout was applied is     not changed.     This of course requires valid positions for all nodes to have been set on the input     graph before calling the     layout algorithm. The interactive layer sweep algorithm uses the     Interactive Reference Point option to     determine which reference point of nodes are used to compare     positions. */
            cycleBreaking: 'GREEDY', // Strategy for cycle breaking. Cycle breaking looks for cycles in the graph     and     determines which edges to reverse to break the cycles. Reversed edges will end up pointing to     the opposite     direction of regular edges (that is, reversed edges will point left if edges usually     point right).
            /* GREEDY This algorithm reverses edges greedily. The algorithm tries to avoid edges that have the     Priority     property set.
            INTERACTIVE The interactive algorithm tries to reverse edges that already pointed leftwards in the     input     graph. This requires node and port coordinates to have been set to sensible values. */
            direction: 'RIGHT', // 'UNDEFINED', // Overall direction of edges: horizontal (right / left) or vertical     (down / up)
            /* UNDEFINED, RIGHT, LEFT, DOWN, UP */
            edgeRouting: 'ORTHOGONAL', // Defines how edges are routed (POLYLINE, ORTHOGONAL, SPLINES)
            edgeSpacingFactor: 0.5, // Factor by which the object spacing is multiplied to arrive at the minimal     spacing     between edges.
            feedbackEdges: false, // Whether feedback edges should be highlighted by routing around the nodes.
            fixedAlignment: 'NONE', // 'NONE', // Tells the BK node placer to use a certain alignment instead of     taking the optimal     result.  This option should usually be left alone.
            /* NONE Chooses the smallest layout from the four possible candidates.
            LEFTUP Chooses the left-up candidate from the four possible candidates.
            RIGHTUP Chooses the right-up candidate from the four possible candidates.
            LEFTDOWN Chooses the left-down candidate from the four possible candidates.
            RIGHTDOWN Chooses the right-down candidate from the four possible candidates.
            BALANCED Creates a balanced layout from the four possible candidates. */
            inLayerSpacingFactor: 1.0, // Factor by which the usual spacing is multiplied to determine the     in-layer     spacing between objects.
            layoutHierarchy: false, // Whether the selected layouter should consider the full hierarchy
            linearSegmentsDeflectionDampening: 0.3, // Dampens the movement of nodes to keep the diagram from     getting     too large.
            mergeEdges: false, // Edges that have no ports are merged so they touch the connected nodes at the     same     points.
            mergeHierarchyCrossingEdges: true, // If hierarchical layout is active, hierarchy-crossing edges use     as few     hierarchical ports as possible.
            nodeLayering: 'NETWORK_SIMPLEX', // Strategy for node layering.
            /* NETWORK_SIMPLEX This algorithm tries to minimize the length of edges. This is the most     computationally     intensive algorithm. The number of iterations after which it aborts if it hasn't     found a result yet can be     set with the Maximal Iterations option.
            LONGEST_PATH A very simple algorithm that distributes nodes along their longest path to a sink node.
            INTERACTIVE Distributes the nodes into layers by comparing their positions before the layout algorithm     was     started. The idea is that the relative horizontal order of nodes as it was before layout was     applied is not     changed. This of course requires valid positions for all nodes to have been set on     the input graph before     calling the layout algorithm. The interactive node layering algorithm uses     the Interactive Reference Point     option to determine which reference point of nodes are used to     compare positions. */
            nodePlacement: 'LINEAR_SEGMENTS', // Strategy for Node Placement
            /* BRANDES_KOEPF Minimizes the number of edge bends at the expense of diagram size: diagrams drawn     with this     algorithm are usually higher than diagrams drawn with other algorithms.
            LINEAR_SEGMENTS Computes a balanced placement.
            INTERACTIVE Tries to keep the preset y coordinates of nodes from the original layout. For dummy nodes,     a     guess is made to infer their coordinates. Requires the other interactive phase implementations     to have run     as well.
            SIMPLE Minimizes the area at the expense of... well, pretty much everything else. */
            randomizationSeed: 1, // Seed used for pseudo-random number generators to control the layout algorithm;     0     means a new seed is generated
            routeSelfLoopInside: false, // Whether a self-loop is routed around or inside its node.
            separateConnectedComponents: true, // Whether each connected component should be processed separately
            spacing: 60, // 20, // Overall setting for the minimal amount of space to be left between objects
            thoroughness: 8 // 7, // How much effort should
        }
      }
      const layout = this.cy.$('node.node_main, edge').layout(options)
      this.cy.maxZoom(DefaultChartConfig.fitMaxZoom)
      layout.removeAllListeners()
      layout.on('layoutstop', () => {
        this.updateStatusNode()
        this.cy.maxZoom(DefaultChartConfig.maxZoom)
        this.cy.data('positionChanged', false)
        this._data.is_trigger_auto_layout = true
        const currentLineType = this.cy.data('currentLineType')
        if (currentLineType === LINE_TYPES.polyline) {
          this.cy.edges().removeClass(`line_type_${currentLineType}`)
          this.cy.edges().addClass('line_type_layout')
          // 只隐式更新
          // this.updateLineType(LINE_TYPES.layout);
        }
        // 更新navigator
        this._nav && this._nav.updateImage()
        this.emit('layoutStop')
      })
      layout.run()
    }

    restoreBeforeLayout () {
      if (!this._preLayoutData) { return }
      this.cy.data('positionChanged', true)
      this._data.is_trigger_auto_layout = false
      this.updateChart(this._preLayoutData)
      // this.updateLineType(this._preLayoutLineType);
    }

    convertDataToElements (data) {
      // const nodes = data.node_meta.map(e => createNodeElement(e));
      const nodes = data.node_meta.reduce((accumulate, e) => {
        const node = createNodeElement(e)
        let status_node
        if (e.status !== NODE_STATUS.default && e.status !== NODE_STATUS.no_permission) {
          status_node = createStatusNodeElement(node)
        }
        node && accumulate.push(node)
        status_node && accumulate.push(status_node) && (node.data.cid = status_node.data.id)
        return accumulate
      }, [])

      const edges = data.line_meta.map(e => createEdgeElement(e))

      return [...nodes, ...edges]
    }

    highlightPath (node) {
      const highlightNode = node || this.cy.nodes(`node.node_main[type!="${NODE_TYPES.input}"][?highlight]`)
      if (highlightNode && highlightNode.length) {
        this.cy.nodes(`node.node_main[type!="${NODE_TYPES.input}"][highlight]`).forEach(e => e.data('highlight', false))
        this.cy.edges('.edge_main').removeClass('highlight')
        const predesors = highlightNode.predecessors('edge')
        predesors.forEach(f => f.addClass('highlight'))
        highlightNode.data('highlight', true)
      }
    }

    clearHighlightPath () {
      this.cy.nodes(`node.node_main[type!="${NODE_TYPES.input}"][highlight]`).forEach(e => e.data('highlight', false))
      this.cy.edges('.edge_main').removeClass('highlight')
      // 更新navigator
      // this._nav && this._nav.updateImage();
    }

    setCursor (cursorType = CURSOR_TYPES.auto) {
      this.cy.container().style.cursor = cursorType
    }

    openComment (isopen = true) {
      this._data.is_comment = !!isopen
      this.createHtmlLabel()
    }

    _cyReady () {
      this.cy.data('positionChanged', false)
      this._data.position = `${this.cy.pan().x} ${this.cy.pan().y}`
      this._data.zoom = this.cy.zoom()
      this.cy.minZoom(DefaultChartConfig.minZoom)
      this.cy.maxZoom(DefaultChartConfig.maxZoom)
      // this.updateView();
      this.updateLineType(this._data.line_type)
      this.updateLineColor(this._data.line_color)
      // 只在预览模式下显示条数
      // if (this._mode === 'read') {
        this.createHtmlLabel()
      // }
      this._initCyEvents()
      // this._initStatusNodes();
      this._initNavigator()
      this.highlightPath()
      this._initRenameControl()
      if (this._data.is_trigger_auto_layout) {
        this.setLayout(false)
      }
      if (this._mode === 'read') {
        this.cy.autolock(true)
      } else {
        this.cy.autolock(false)
        this._initEdgeHandle()
        this._initContextMenus()
      }
      if (!this.cy.nodes().length) {
        this._nav && this._nav.hide()
      }
    }

    _initCyEvents () {
      this.cy.on('click', evt => {
        this.etlInstance && this.etlInstance.hideRightTooltip()
        if (this._renameControl && this._renameControl.isShow()) { return }
        const clickTarget = evt.target
        if (!clickTarget || clickTarget === this.cy) {
          // 点击空白处。。。
          this._eh && this._eh.hide()
          this.emit('emptyClick')
        } else if (clickTarget.isNode() && clickTarget.hasClass('node_main')) {
          // 点击节点
          this.currentHoverTarget = null
          this.emit('nodeClick', clickTarget.json().data)
        }
      })

      this.cy.on('tapstart', evt => {
        this.etlInstance && this.etlInstance.hideRightTooltip()
      })

      this.cy.on('tapdrag', evt => {
        // console.log('tapdrag--');
        if (this._eh && this._eh.active) {
          if (evt.target === this.cy || evt.target.isEdge()) {
            this.cy.$('node.node_main, edge').removeClass('active')
          } else {
            evt.target.addClass('active')
          }
        }
      })

      this.cy.on('tapdragover', 'node.node_main, edge', evt => {
        console.log('evt',evt.target,evt.target.isNode())
        if (evt.target.isNode()) {
          this._showTooltip(evt.target)
          if (this._mode !== 'read') { this.setCursor(CURSOR_TYPES.move) }
        } else if (evt.target.isEdge() && this._mode !== 'read') {
          this.showDeleteBtn(evt)
        }
        evt.target.addClass('active')
        this.currentHoverTarget = evt.target
      })

      this.cy.on('tapdragout', 'node.node_main, edge', evt => {
        if (evt.target.isEdge()) {
          this.hideDeleteBtn()
        }
        evt.target.removeClass('active')
        // 隐藏tooltip
        this.emit('hideTooltip')
        this.setCursor(CURSOR_TYPES.pointer)
        this.currentHoverTarget = null
      })

      this.cy.on('position', 'node.node_main', evt => {
        const nodeElement = evt.target
        this.cy.edges().updateStyle()
        // 更新状态节点
        // if (haveStatusNode(nodeElement)) {
        //   const child_nodes = this.cy.nodes(`.node_status[pid="${ nodeElement.id() }"]`);
        //   child_nodes.forEach(child => {
        //     child.position({
        //       x: nodeElement.position().x + nodeElement.width() / 2,
        //       y: nodeElement.position().y - nodeElement.height() / 2
        //     });
        //   });
        // }
        // 更新数据，兼容旧版
        this.cy.data('positionChanged', true)
        this._data.is_trigger_auto_layout = false
        nodeElement.data('loc', `${nodeElement.position('x')} ${nodeElement.position('y')}`)

        // 隐藏tooltip
        this.emit('hideTooltip')
      })

      this.cy.on('free', 'node.node_main', evt => {
        // 更新navigator
        this._nav && this._nav.updateImage()
        // 显示tooltip
        if (evt.target.isNode()) {
          this._showTooltip(evt.target)
        }
      })

      this.cy.on('viewport', evt => {
        // 更新数据
        this._data.position = `${this.cy.pan().x} ${this.cy.pan().y}`
        this._data.zoom = this.cy.zoom()

        if (this._renameControl && this._renameControl.isShow()) {
          const renderPosition = this.currentRenameNode.renderedPosition()
          renderPosition.y += this.cy.zoom() * this.currentRenameNode.height() / 2 + this.cy.zoom() * 8
          this._renameControl.setPosition(renderPosition, this.cy.zoom())
        }
      })

      this.cy.on('cxttap', evt => {
        if (evt.target === this.cy) { // 点击空白处，设置常用算子
          if (this._mode !== 'read') {
            const position = evt.renderedPosition
            this._ctxMenuInstance && this._ctxMenuInstance.hide()
            this.emit('emptyCtxClick', position)
          }
        } else {
          this.etlInstance && this.etlInstance.hideRightTooltip()
        }
        this.emit('hideTooltip')
      })

      // fix：解决图片异步加载时，缩略图无背景问题
      this.cy.on('background', 'node', _.debounce(evt => {
        this._nav && this._nav.updateImage()
      }, 200, { leading: true }))

      // 快捷键删除事件
      // document.addEventListener('keyup', this._onKeyUpEvent);
    }

    _onKeyUpEvent (event) {
      if (this._mode === 'read') { return }
      if (this._renameControl && this._renameControl.isShow()) { return }
      if (this._eh && this._eh.active) { return }
      // delete 键删除, 46-delete, 8-backspace
      if ((event.keyCode === 46 || event.keyCode === 8) && this.currentHoverTarget) {
        this._deleteHandler(this.currentHoverTarget)
      }
    }

    // _initStatusNodes() {
      // const haveStatusNodes = this.cy.nodes(`.node_main[status!=${NODE_STATUS.default}][status!=${NODE_STATUS.no_permission}]`);
      // const statusNodes = haveStatusNodes.map(e => createStatusNodeElement(e));
      // this.cy.add(statusNodes);
    // }

    _showTooltip (hoverNode) {
      const pos = hoverNode.renderedPosition()
      const width = hoverNode.renderedOuterWidth()
      const height = hoverNode.renderedOuterHeight()
      const hoverPosition = {
        x: pos.x,
        y: pos.y,
        width: width,
        height: height
      }
      this.emit('showTooltip',{
        hoverPosition: hoverPosition,
        data: hoverNode.json().data
      })
      // this.etlInstance && this.etlInstance.showToolTip(hoverPosition, hoverNode.json().data)
    }

    _initEdgeHandle () {
      const self = this
      const defaults = {
        preview: false, // whether to show added edges preview before releasing selection
        hoverDelay: 200, // 检测鼠标是否在节点周围的时间间隔
        handleNodes: `node.node_main[type!$="${NODE_TYPES.output}"]`, // selector/filter function for whether edges can be made from a given node
        // 不开启距离检测，节省性能开销
        snap: false, // when enabled, the edge can be drawn by just moving close to a target node
        snapThreshold: DefaultChartConfig.nodeSize, // 检测鼠标点击是否在节点附近的阈值
        // the target node must be less than or equal to this many pixels away from the cursor/finger
        snapFrequency: 10, // the number of times per second (Hz) that snap checks done (lower is less expensive)
        noEdgeEventsInDraw: true, // set events:no to edges during draws, prevents mouseouts on compounds
        disableBrowserGestures: true, // during an edge drawing gesture, disable browser gestures such as two-finger trackpad swipe and pinch-to-zoom
        handlePosition: function (node) {
          return 'right middle' // sets the position of the handle in the format of "X-AXIS Y-AXIS" such as "left top", "middle top"
        },
        handleInDrawMode: false, // whether to show the handle in draw mode
        edgeType: function (sourceNode, targetNode) {
          // can return 'flat' for flat edges between nodes or 'node' for intermediate node between them
          // returning null/undefined means an edge can't be added between the two nodes
          return 'flat'
        },
        loopAllowed: function (node) {
          // for the specified node, return whether edges from itself to itself are allowed
          return false
        },
        nodeLoopOffset: -50, // offset for edgeType: 'node' loops
        nodeParams: function (sourceNode, targetNode) {
          // for edges between the specified source and target
          // return element object to be passed to cy.add() for intermediary node
          return {}
        },
        edgeParams: function (sourceNode, targetNode, i) {
          // for edges between the specified source and target
          // return element object to be passed to cy.add() for edge
          // NB: i indicates edge index in case of edgeType: 'node'
          return {}
        },
        ghostEdgeParams: function () {
          // return element object to be passed to cy.add() for the ghost edge
          // (default classes are always added for you)
          return {
            classes: `line_type_${self.cy.data('currentLineType')}`
          }
        },
        show: function (sourceNode) {
          // fired when handle is shown
        },
        hide: function (sourceNode) {
          // fired when the handle is hidden
        },
        start: function (sourceNode) {
          // fired when edgehandles interaction starts (drag on handle)
        },
        complete: function (sourceNode, targetNode, addedEles) {
          // fired when edgehandles is done and elements are added
          // console.log('complete', sourceNode, targetNode, addedEles);
          const result = isNodeCanLineTo(targetNode, sourceNode, self.cy)
          if (result.flag) {
            const edgeData = {
              to: targetNode.data('key'),
              from: sourceNode.data('key'),
              ...getEdgeType(sourceNode, targetNode)
            }
            const edgeElement = self.addEdge(edgeData)
            targetNode.data('parents').push(sourceNode.data('key'))
            if (/data_aggr$/.test(targetNode.data('type'))) {
              const hasStreamingInput = targetNode.predecessors('node.node_main').some(node => isStreamingInput(node.data()))
              if (hasStreamingInput) {
                targetNode.data('type', 'streaming_data_aggr')
              } else {
                targetNode.data('type', 'data_aggr')
              }
            }
            // self.cy.data('positionChanged', true);
            self.emit('drawEdgeComplete', {
              source: sourceNode.json().data,
              target: targetNode.json().data,
              edge: JSON.parse(JSON.stringify(edgeData))
            })
            // 更新navigator
            self._nav && self._nav.updateImage()
          } else {
            if (result.message) {
              self.etlInstance && self.etlInstance.commonData.globalTip.set(result.message)
            }
          }
        },
        stop: function (sourceNode) {
          // fired when edgehandles interaction is stopped (either complete with added edges or incomplete)
        },
        cancel: function (sourceNode, cancelledTargets) {
          // fired when edgehandles are cancelled (incomplete gesture)
        },
        hoverover: function (sourceNode, targetNode) {
          // fired when a target is hovered
        },
        hoverout: function (sourceNode, targetNode) {
          // fired when a target isn't hovered anymore
        },
        previewon: function (sourceNode, targetNode, previewEles) {
          // fired when preview is shown
        },
        previewoff: function (sourceNode, targetNode, previewEles) {
          // fired when preview is hidden
        },
        drawon: function () {
          // fired when draw mode enabled
        },
        drawoff: function () {
          // fired when draw mode disabled
        }
      }
      this._eh = this.cy.edgehandles(defaults)
    }

    resetCommonlyUse (favoriteOptMap) {
      if (favoriteOptMap) { this.favoriteOptMap = favoriteOptMap }
      this.cy.nodes('.node_main').forEach(n => n.data('commonlyUse', false))
      this._initCommonlyUse()
    }

    _initCommonlyUse () {
      if (this.favoriteOptMap) {
        this.favoriteOptMap.forEach((val, key) => {
          const type = key.optType || key.opt_Type
          if (type === NODE_TYPES.customize) {
            this.cy.nodes(`.node_main[opt_id="${key.opt_id}"]`).map(n => n.data('commonlyUse', !!key.commonlyUse))
          } else {
            this.cy.nodes(`.node_main[type="${type}"]`).map(n => n.data('commonlyUse', !!key.commonlyUse))
          }
        })
      }
    }

    _initContextMenus () {
      const self = this
      const ctxOptions = {
        menuItems: [
          {
            id: 'markTo',
            content: '标记为',
            tooltipText: '',
            selector: `node.node_main[type!$="${NODE_TYPES.output}"]`,
            onClickFunction: function (e, color) {
              const targetNode = e.target
              if (targetNode) {
                const markColor = targetNode.data('markColor')
                if (markColor == color) {
                  targetNode.data('markColor', '')
                } else {
                  targetNode.data('markColor', color)
                }
                self.emit('markColorChange', targetNode.json().data)
              }
            },
            disabled: false,
            show: true,
            hasTrailingDivider: true,
            coreAsWell: false
          },
          {
            id: 'rename',
            content: '重命名',
            tooltipText: '',
            // node.node_main[type="${NODE_TYPES.output}"][status=${NODE_STATUS.complete}],
            selector: `node.node_main[type!="${NODE_TYPES.input}"][type!$="${NODE_TYPES.output}"][status=${NODE_STATUS.default}], node.node_main[type!="${NODE_TYPES.input}"][type!$="${NODE_TYPES.output}"][status=${NODE_STATUS.complete}]`,
            onClickFunction: function (e) {
              self._startRename(e.target)
            },
            disabled: false,
            show: true,
            hasTrailingDivider: false,
            coreAsWell: false
          },
          {
            id: 'copy',
            content: '复制',
            tooltipText: '',
            selector: `node.node_main[type!="${NODE_TYPES.input}"][type!$="${NODE_TYPES.output}"], node.node_main[type$="${NODE_TYPES.output}"][status!=${NODE_STATUS.empty}], node.node_main[type$="${NODE_TYPES.increment_output}"][status!=${NODE_STATUS.empty}]`,
            onClickFunction: function (e) {
              self.copyNode(e.target)
            },
            disabled: false,
            show: true,
            hasTrailingDivider: false,
            coreAsWell: false
          },
          {
            id: 'delete',
            content: '删除',
            tooltipText: '',
            selector: 'node.node_main',
            onClickFunction: function (e) {
              self._deleteHandler(e.target)
            },
            disabled: false,
            show: true,
            hasTrailingDivider: true,
            coreAsWell: false
          },
          {
            id: 'preview',
            content: '预览数据',
            tooltipText: '',
            selector: `node.node_main[type!="${NODE_TYPES.input}"][type!$="${NODE_TYPES.output}"], node.node_main[status!=${NODE_STATUS.empty}]`,
            onClickFunction: function (e) {
              self.emit('previewData', e.target.json().data)
            },
            disabled: false,
            show: true,
            hasTrailingDivider: false,
            coreAsWell: false
          },
          {
            id: 'highlightActive', // ID of menu item
            content: '高亮路径', // Display content of menu item
            tooltipText: '', // Tooltip text for menu item
            // image: {src : "./cyto-lib/img/remove.svg", width : 12, height : 12, x : 6, y : 4}, // menu icon
            // Filters the elements to have this menu item on cxttap
            // If the selector is not truthy no elements will have this menu item on cxttap
            selector: `node.node_main[type!="${NODE_TYPES.input}"][!highlight][[indegree > 0]]`,
            onClickFunction: function (e) { // The function to be executed on click
              const targetNode = e.target
              if (targetNode.isNode()) {
                if (self.isFirstHighlight) {
                  // 弹出提示框
                  self.emit('showMessage')
                  self.isFirstHighlight = false
                }
                self.highlightPath(targetNode)
                self.emit('highlight', targetNode.json().data)
                // 更新navigator
                // self._nav && self._nav.updateImage();
              }
            },
            disabled: false, // Whether the item will be created as disabled
            show: true, // Whether the item will be shown or not
            hasTrailingDivider: true, // Whether the item will have a trailing divider
            coreAsWell: false // Whether core instance have this item on cxttap
          },
          {
            id: 'highlightCancel',
            content: '取消高亮',
            tooltipText: '',
            selector: `node.node_main[type!="${NODE_TYPES.input}"][?highlight]`,
            onClickFunction: function (e) {
              const targetNode = e.target
              if (targetNode.isNode()) {
                const predesors = targetNode.predecessors('edge')
                predesors.forEach(f => f.removeClass('highlight'))
                targetNode.data('highlight', false)
                self.emit('cancelHighlight', targetNode.json().data)
                // 更新navigator
                // self._nav && self._nav.updateImage();
              }
            },
            disabled: false,
            show: true,
            hasTrailingDivider: true,
            coreAsWell: false
          },
          {
            id: 'nodenotes',
            content: '节点注释',
            tooltipText: '',
            selector: `node.node_main[type!="${NODE_TYPES.input}"]`,
            onClickFunction: function (e) {
              self.emit('nodenotes', e.target.json().data)
            },
            disabled: false,
            show: true,
            hasTrailingDivider: etlType === 'etlModel',
            coreAsWell: false
          }
        ],
        // css classes that menu items will have
        menuItemClasses: [
          // add class names to this list
        ],
        // css classes that context menu will have
        contextMenuClasses: [
          // add class names to this list
        ]
      }
      if (etlType === 'etlModel') {
        self._initCommonlyUse()
        ctxOptions.menuItems.push(...[
          {
            id: 'setCommonlyUse',
            content: '设为常用',
            tooltipText: '',
            selector: `node.node_main[!commonlyUse][type!="${NODE_TYPES.input}"][type!$="${NODE_TYPES.output}"],
            node.node_main[!commonlyUse][type$="${NODE_TYPES.output}"][status!=${NODE_STATUS.empty}]`,
            onClickFunction: function (e) {
              const targetNode = e.target
              const type = targetNode.data('type')
              const optId = targetNode.data('opt_id')
              if (type === NODE_TYPES.customize) {
                self.cy.nodes(`.node_main[opt_id="${optId}"]`).forEach(n => n.data('commonlyUse', true))
              } else {
                self.cy.nodes(`.node_main[type="${type}"]`).forEach(n => n.data('commonlyUse', true))
              }
              self.emit('setCommonlyUse', targetNode.json().data)
            },
            disabled: false,
            show: true,
            hasTrailingDivider: false,
            coreAsWell: false
          },
          {
            id: 'cancelCommonlyUse',
            content: '取消常用',
            tooltipText: '',
            selector: `node.node_main[?commonlyUse][type!="${NODE_TYPES.input}"]`,
            onClickFunction: function (e) {
              const targetNode = e.target
              const type = targetNode.data('type')
              const optId = targetNode.data('opt_id')
              if (type === NODE_TYPES.customize) {
                self.cy.nodes(`.node_main[opt_id="${optId}"]`).forEach(n => n.data('commonlyUse', false))
              } else {
                self.cy.nodes(`.node_main[type="${type}"]`).forEach(n => n.data('commonlyUse', false))
              }
              self.emit('cancelCommonlyUse', targetNode.json().data)
            },
            disabled: false,
            show: true,
            hasTrailingDivider: false,
            coreAsWell: false
          }
        ])
      }
      this._ctxMenuInstance = this.cy.contextMenus(ctxOptions)
    }

    _deleteHandler (target) {
      if (target) {
        if (target.isEdge && target.isEdge()) {
          // 如果删除了高亮线，则取消所有高亮状态
          if (target.hasClass('highlight')) {
            this.clearHighlightPath()
          }
          this.removeEdge(target)
          this.hideDeleteBtn()
          this.emit('deleteEdge', {
            target: target.target().json().data,
            edge: target.json().data
          })
        } else if (target.isNode && target.isNode()) {
          this.removeNode(target)
          this.emit('deleteNode', target.json().data)
        }
        // 更新navigator
        this._nav && this._nav.updateImage()
        // 隐藏tooltip
        this.emit('hideTooltip')
      }
    }

    _initNavigator () {
      const navOptions = {
        container: this.cy.container(), // can be a HTML or jQuery element or jQuery selector
        viewLiveFramerate: 0, // set false to update graph pan only on drag end; set 0 to do it instantly; set a number (frames per second) to update not more than N times per second
        // thumbnailEventFramerate: 30, // max thumbnail's updates per second triggered by graph updates
        // thumbnailLiveFramerate: false, // max thumbnail's updates per second. Set false to disable
        dblClickDelay: 200, // milliseconds
        removeCustomContainer: true, // destroy the container specified by user on plugin destroy
        rerenderDelay: 600 // ms to throttle rerender updates to the panzoom for performance
      }
      this._nav = this.cy.navigator(navOptions) // get navigator instance, nav
    }

    _initRenameControl () {
      this._renameControl = new ReNameControl(this.cy.container())
      this._renameControl.on('confirm', text => {
        if (this.etlInstance) {
          const data = this.currentRenameNode.json().data
          if (text === '') {
            this.etlInstance.commonData.globalTip.set('名称不能为空')
            return
          }
          data.title = text

          // test
          // this.currentRenameNode.data(data);

          // 请求服务端
          this.etlInstance.rename(data).then(e => {
            // 重命名成功
            if (e.status === '0') {
              this.currentRenameNode.data('title', data.title)
              // this.currentRenameNode.data('meta', data.meta);
            } else {
              this.etlInstance.commonData.globalTip.set(e.errstr)
            }
            this._endRename()
          }).catch(e => {
            this.etlInstance.commonData.globalTip.set('重命名失败！')
            this._endRename()
          })
        } else {
          this._endRename()
        }
      })
      this._renameControl.on('cancel', () => {
        this._endRename()
      })
    }

    createHtmlLabel () {
      const querySelectors = {
        datacount: 'node.node_main[data_count][data_count!=\'null\']',
        debug: 'node.node_main[?failmsg]',
        comment: 'node.node_main[?comment]'
      }

      const showInfo = {
        datacount: false,
        debug: false,
        comment: false,
        version: false
      }

      const selectArr = []
      if (this._mode === 'read') {
        if (this.is_debug) {
          selectArr.push(querySelectors.debug, querySelectors.datacount)
          showInfo.debug = true
          showInfo.datacount = true
        } else {
          selectArr.push(querySelectors.datacount)
          showInfo.datacount = true
        }
        if (this.is_version) {
          showInfo.version = true
        }
      } else {
        if (this.is_debug) {
          selectArr.push(querySelectors.debug)
          showInfo.debug = true
        }
      }
      if (this._data.is_comment) {
        selectArr.push(querySelectors.comment)
        showInfo.comment = true
      }

      const paramConfig = [{
        query: selectArr.join(','),
        halign: 'center', // title horizental position. Can be 'left',''center, 'right'
        valign: 'bottom', // title vertical position. Can be 'top',''center, 'bottom'
        halignBox: 'center', // title vertical position. Can be 'left',''center, 'right'
        valignBox: 'bottom', // title relative box vertical position. Can be 'top',''center, 'bottom'
        cssClass: 'node-html-label-container', // any classes will be as attribute of <div> container for every title
        tpl (data) {
          const html = []
          if (showInfo.comment && data.comment) {
            html.push('<div class="node-html-label-content content--comment">')
            html.push(`<span class="node-html-label-span">${data.comment}</span>`)
            html.push('</div>')
          }
          if (!(data.type == 'input' && data.originType == 'streaming') && showInfo.datacount && data.data_count != null && !showInfo.version) {
            html.push('<div class="node-html-label-content content--datacount">')
            html.push(`<span class="node-html-label-span">${data.data_count}条</span>`)
            html.push('</div>')
          }
          if (showInfo.debug && data.failmsg) {
            html.push('<div class="node-html-label-content content--debug">')
            html.push(`<div class="label-box"><span class="node-html-label-span">${data.failmsg}</span></div>`)
            html.push(`<div class="download-box"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 10.2929V1H8V10.2929L11.1464 7.14645L11.8536 7.85355L7.85355 11.8536L7.5 12.2071L7.14645 11.8536L3.14645 7.85355L3.85355 7.14645L7 10.2929ZM14 13V14H1V13H14Z" fill="#1F71FF"/>
            </svg>
            <a data-dbid='${data.id}' class="debug-download-btn">下载日志</a></div>`)
            html.push('</div>')
          }
          return html.join('')
        }
      }]

      if (this._nodeHtmlInstance) {
        this._nodeHtmlInstance.update(paramConfig)
      } else {
        this._nodeHtmlInstance = this.cy.nodeHtmlLabel(paramConfig)
      }

      const containerEle = this._nodeHtmlInstance.getLabelContainer()
      if (containerEle) {
        containerEle.addEventListener('click', e => {
          e.stopPropagation()
          if (e.target && e.target.dataset.dbid) {
            const dbid = e.target.dataset.dbid
            const node = this.cy.$id(dbid)
            if (node && node.isNode()) {
              // console.log('id', dbid, node.json().data);
              this.emit('downLoadDebugInfo', node.json().data)
            }
          }
          return false
        }, false)
      }
    }

    _startRename (node) {
      this.currentRenameNode = node
      if (node && this._renameControl) {
        const rendertPosition = node.renderedPosition()
        rendertPosition.y += this.cy.zoom() * this.currentRenameNode.height() / 2 + this.cy.zoom() * 8
        this._renameControl.setText(node.data('title'))
        this._renameControl.setPosition(rendertPosition, this.cy.zoom())
        this.cy.autolock(true)
        this.cy.zoomingEnabled(false)
        // this.cy.panningEnabled(false);
        this._eh.disable()
        this._ctxMenuInstance.disable()
        this._renameTextOpacity = this.currentRenameNode.style('text-opacity')
        this.currentRenameNode.style('text-opacity', 0)
      }
    }

    _endRename () {
      this._renameControl.setPosition(null) // 不传位置，则隐藏
      this.cy.autolock(false)
      this.cy.zoomingEnabled(true)
      // this.cy.panningEnabled(true);
      this._eh.enable()
      this._ctxMenuInstance.enable()
      this.currentRenameNode.style('text-opacity', this._renameTextOpacity || 0.72)
    }
  }

  if (typeof module === 'object' && module.exports) {
      module.exports = CytoscapeChart
  } else if (typeof define === 'function' && define.amd) {
      define(function () {
          return CytoscapeChart
      })
  }
  global.CytoscapeChart = CytoscapeChart
})(window || this || global)
