.cy-rename-control-container {
  position: absolute;
  overflow: hidden;
  /* 显示时 display 为 flex */
  display: none;
  flex-flow: column nowrap;
}

.cy-rename-control-container .operate-div {
  display: flex;
  justify-content: flex-end;
  flex-wrap: nowrap;
}

.cy-rename-control-container .operate-div .btn-contanier {
  display: flex;
  flex-flow: row nowrap;
  border: 1px solid rgba(16, 48, 102, 0.05);
  border-top: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0px 2px 4px rgba(16, 48, 102, 0.08);
}

.cy-rename-control-container .input {
  width: 120px;
  height: 28px;
  background: #F7F7F8;
  box-shadow: inset 0px -1px 0px rgba(95, 97, 102, 0.243226);
  border-radius: 4px 4px 0px 0px;
  border: none;
  outline: none;
  padding: 0 8px;

  font-family: 'Microsoft Yahei';
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 20px;
  color:  rgba(15, 34, 67, 0.92);
  border-bottom: 2px solid #ccc;
}

.cy-rename-control-container .input:focus {
  background: #E2E4E8;
  border-bottom-color: #1F71FF;
}

.cy-rename-control-container .btn {
  width:32px;
  height: 32px;
  border: none;
  outline: none;
  padding: 0;
  background-color: #ffffff; 
}

.cy-rename-control-container .btn:hover {
  background-color: #F1F2F4;
}

.cy-rename-control-container .btn-confirm {
  background: url(./assets/rename-confirm.svg) no-repeat center center;
  background-size: 16px 16px;
}

.cy-rename-control-container .btn-cancel {
  background: url(./assets/rename-cancel.svg) no-repeat center center;
  background-size: 16px 16px;
}