/* eslint-disable */
import { NODE_TYPES, NODE_STATUS, LINE_STYLES, DEFAULT_COLOR, DefaultChartConfig } from './constants'

export function createNodeElement (d, isRenderPosition = false) {
  const pos = d.loc.split(' ')
  return {
    group: 'nodes',
    data: {
      ...d,
      id: d.key
    },
    [isRenderPosition ? 'renderedPosition' : 'position']: {
      x: Number(pos[0]),
      y: Number(pos[1])
    },
    selected: false,
    selectable: true,
    locked: false,
    grabbable: true,
    pannable: false,
    classes: ['node_main']
  }
}

export function createStatusNodeElement (n) {
  if (n.isNode && n.isNode()) {
    return {
      group: 'nodes',
      data: {
        id: `${n.id()}_status_node`,
        pid: n.id(),
        status: n.data('status'),
        frame_status: n.data('frame_status')
      },
      position: {
        x: n.position().x + n.width() / 2,
        y: n.position().y - n.height() / 2
      },
      locked: false,
      // grabbable: false,
      classes: ['node_status']
    }
  } else {
    // 由 createNodeElement 返回json结构
    return {
      group: 'nodes',
      data: {
        id: `${n.data.id}_status_node`,
        pid: n.data.id,
        status: n.data.status,
        frame_status: n.data.frame_status
      },
      position: {
        x: n.position.x + DefaultChartConfig.nodeSize / 2,
        y: n.position.y - DefaultChartConfig.nodeSize / 2
      },
      locked: false,
      // grabbable: false,
      classes: ['node_status']
    }
  }
}

export function createEdgeElement (d) {
  return {
    group: 'edges',
    data: {
      ...d,
      // id: d.key || d.__gohashid, // 兼容旧代码
      source: d.from,
      target: d.to
    },
    classes: ['edge_main', `line_style_${d.status}`]
  }
}

export function isNodeCanLineTo (node, sourceNode, cy) {
  const parents = node.data('parents')
  const nodeType = node.data('type')
  const sourceType = sourceNode ? sourceNode.data('type') : null
  const tbCount = node.data('tb_count') || 0
  const currentToNum = Array.isArray(parents) ? parents.length : 0
  const outPutRegex = /output$/
  const data = {
    message: '暂不支持此操作',
    flag: true
  }

  let maxToNum = 0
  switch (nodeType) {
    case NODE_TYPES.output:
    case NODE_TYPES.streaming_output:
    case NODE_TYPES.increment_output:
    case NODE_TYPES.alter_field:
    case NODE_TYPES.data_aggr:
    case NODE_TYPES.streaming_data_aggr:
    case NODE_TYPES.data_filter:
    case NODE_TYPES.id_trans:
    case NODE_TYPES.add_field:
    case NODE_TYPES.map_field:
    case NODE_TYPES.select:
    case NODE_TYPES.table:
    case NODE_TYPES.deduplication:
    case NODE_TYPES.machine_learning:
    case NODE_TYPES.addr_trans:
    case NODE_TYPES.json:
    case NODE_TYPES.number_handle:
    case NODE_TYPES.date_handle:
    case NODE_TYPES.string_handle:
    case NODE_TYPES.vacancy_rate:
        maxToNum = 1; break
    case NODE_TYPES.join:
    case NODE_TYPES.left_join:
    case NODE_TYPES.anti_join:
    case NODE_TYPES.full_join:
        maxToNum = 2; break
    case NODE_TYPES.union:
    case NODE_TYPES.sql:
        maxToNum = Number.MAX_VALUE; break
    case NODE_TYPES.customize:
        maxToNum = tbCount; break
  }
  data.flag = currentToNum < maxToNum
  if (!data.flag) {
    if (maxToNum == 1) {
      data.message = '仅支持1个输入节点'
    }
    if (maxToNum == 2) {
      data.message = '仅支持2个输入节点'
    }
    if (sourceType === NODE_TYPES.input && nodeType === NODE_TYPES.input) {
      data.message = '输入表不支持连接输入表'
    }
    if (nodeType === NODE_TYPES.input && sourceType !== NODE_TYPES.input) {
      data.message = '算子不支持反向连接输入表'
    }
    if (nodeType === NODE_TYPES.customize && tbCount == 0) {
      data.message = '该算子为非功能性的业务算子，不支持连接输入节点'
    }
    if (sourceType === NODE_TYPES.customize && tbCount == 0 && nodeType === NODE_TYPES.input) {
      data.message = '输入节点不支持连接非功能性的业务算子'
    }
    if (nodeType === NODE_TYPES.customize && tbCount > 0) {
      data.message = '该算子仅需连接' + tbCount + '张输入表'
    }
    return data
  }
  // console.log('节点连线：', node.edgesWith(sourceNode));
  if (outPutRegex.test(nodeType) && sourceNode && sourceNode.edgesWith(`node.node_main[type$="${NODE_TYPES.output}"]`).length) {
    // 源节点如果已经连了输出节点，则不能再连输出
    // console.log('sourceEdge', sourceNode.edgesWith(`node.node_main[type="${NODE_TYPES.output}"]`))
    data.flag = false
    data.message = '暂不支持此操作'
    return data
  } else if (sourceNode && node.edgesWith(sourceNode).length) {
    // 两个节点不能连多条线
    data.flag = false
    data.message = '暂不支持此操作'
    return data
  } else if (outPutRegex.test(nodeType) && sourceType === NODE_TYPES.input) {
    // 输入表不可直接连接输出算子
    data.flag = false
    data.message = '输入节点不支持直接输出'
    return data
  } else if (node === sourceNode) {
    // 自己不能连自己
    data.message = '暂不支持此操作'
    data.flag = false
    return data
  } else if (/streaming_output$/.test(node.data('type'))) {
    const hasStreamingInput = sourceNode.predecessors('node.node_main').some(prev_node => isStreamingInput(prev_node.data()))
    if (!hasStreamingInput) {
      data.flag = false
      data.message = '流式输出对于流式输入'
      return data
    }
  } else {
    const hasSourceStreamingInput = hasStreamingInputParents(sourceNode)
    const hasSourceCommonInput = hasCommonInputParents(sourceNode)
    const unionParents = getNodeOutputs(node, 'union')
    // 流式表+普通表不能连全部合并
    // 从中间节点连线,targeNodes里面有全部合并,全部合并必须是流式表+流式表，普通表+普通表,
    if (unionParents.length > 0) {
      let hasTargetCommonInput = false
      let hasTargetStreamingInput = false
      unionParents.forEach(parent => {
        const matchNode = cy.$id(parent.data('key'))
        hasTargetCommonInput = hasCommonInputParents(matchNode) || hasTargetCommonInput
        hasTargetStreamingInput = hasStreamingInputParents(matchNode) || hasTargetStreamingInput
      })
      if (hasSourceStreamingInput && !hasTargetStreamingInput && hasTargetCommonInput || !hasSourceStreamingInput && hasSourceCommonInput && hasTargetStreamingInput) {
        data.flag = false
        data.message = '不同类型数据表不支持全部合并'
        return data
      }
    }
    // 流式表+数据聚合不能连交集
    // 从中间节点连线,targeNodes里面有数据聚合，数据聚合子节点含有交集,或者source里面有数据聚合
    const targetDataGggrs = getNodeOutputs(node, 'streaming_data_aggr')
    const sourceDataGggrs = getNodeIncomes(sourceNode, 'streaming_data_aggr')
    let hasJoin = false
    if (targetDataGggrs.length > 0) {
      targetDataGggrs.forEach(join => {
        const matchNode = cy.$id(join.data('key'))
        hasJoin = getNodeOutputs(matchNode, 'join').length > 0 || hasJoin
      })
      if (hasSourceStreamingInput && hasJoin) {
        data.flag = false
        data.message = '流式数据表聚合计算后不支持连接交集算子'
        return data
      }
    }
    if (targetDataGggrs.length <= 0 && sourceDataGggrs.length > 0) {
        hasJoin = getNodeOutputs(node, 'join').length > 0 || hasJoin
        if (hasSourceStreamingInput && hasJoin) {
          data.flag = false
          data.message = '流式数据表聚合计算后不支持连接交集算子'
          return data
        }
    }
    // targeNodes里面有交集，判断targeNodes 交集输入是否有流式输入，以及子节点是否有聚合，然后source是否有流式输入
    // targeNodes里面没有交集，判断source里面有交集，交集的父节点有两个流式输入targeNodes里面有聚合
    const targetJoins = getNodeOutputs(node, 'join')
    const sourceJoins = getNodeIncomes(sourceNode, 'join')
    let hasDataGggr = false
    let hasTargetStreamingInput = false
    if (targetJoins.length > 0) {
      targetJoins.forEach(join => {
        const matchNode = cy.$id(join.data('key'))
        hasDataGggr = getNodeOutputs(matchNode, 'streaming_data_aggr').length > 0 || hasDataGggr
        hasTargetStreamingInput = hasStreamingInputParents(matchNode) || hasTargetStreamingInput
      })
      if (hasSourceStreamingInput && hasDataGggr && hasTargetStreamingInput) {
        data.flag = false
        data.message = '流式表交集计算后不支持连接聚合算子'
        return data
      }
    }
    if (targetJoins.length <= 0 && sourceJoins.length > 0) {
      const dataGggrs = getNodeOutputs(node, 'streaming_data_aggr')
      const streamingInputNumArr = []
      hasDataGggr = dataGggrs.length > 0 || hasDataGggr
      sourceJoins.forEach(aggr => {
        const matchNode = cy.$id(aggr.data('key'))
        const streamingInputNum = matchNode.predecessors('node.node_main').filter(node => { return hasStreamingInputParents(node) }).length
        streamingInputNumArr.push(streamingInputNum)
      })
      let hasParentMore = false
      hasParentMore = streamingInputNumArr.filter(num => { return num >= 2 }).length > 0
      if (hasSourceStreamingInput && hasDataGggr && hasParentMore) {
        data.flag = false
        data.message = '流式表交集计算后不支持连接聚合算子'
        return data
      }
    }

    // 左连接：流式表和流式表，暂不支持连接左连接
    // 左连接：流式表和静态表连接，左表必须是流式表
    const hasSourceLeftJoinStreamingInput = hasStreamingInputParents(sourceNode)
    const hasSourceLeftJoinCommonInput = hasCommonInputParents(sourceNode)
    const targetLeftJoins = getNodeOutputs(node, 'left_join')
    const isDoubleFlow = false
    const modelType = document.cookie.replace(/(?:(?:^|.*;\s*)modelType\s*\=\s*([^;]*).*$)|^.*$/, '$1')
    if (targetLeftJoins.length > 0 && modelType === 'flow') {
      let hasTargetLeftJoinStreamingInput = false
      const hasTargetLeftJoinCommonInput = false
      targetLeftJoins.forEach(parent => {
        const matchNode = cy.$id(parent.data('key'))
        const parents = matchNode.data('parents')
        // 当连线并且只连了一根线的时候,并且targetNode == left_join
        if (parents.length == 1 && node.data('key') == parent.data('key')) {
          // 代表一根线有流式
          hasTargetLeftJoinStreamingInput = hasStreamingInputParents(matchNode) || hasTargetLeftJoinStreamingInput
        }
        if (parents.length == 2) {
          // 如果parents[0]和sourceNode不一侧
          if (node.successors('node.node_main').union(node).filter(targetNode => { return targetNode.data('key') == parents[0] }).length <= 0) {
            hasTargetLeftJoinStreamingInput = hasStreamingInputParents(cy.$id(parent.data(parents[0]))) || hasTargetLeftJoinStreamingInput
          } else {
            hasTargetLeftJoinStreamingInput = hasStreamingInputParents(cy.$id(parent.data(parents[1]))) || hasTargetLeftJoinStreamingInput
          }
        }
      })
      // 都为流式表
      if (hasSourceLeftJoinStreamingInput && hasTargetLeftJoinStreamingInput) {
        data.flag = false
        data.message = '2个流式数据表不支持同时连接左连接算子'
        return data
      }
    }

    let lineNotAllowed = false
    if (targetLeftJoins.length > 0 && modelType === 'flow') {
      targetLeftJoins.forEach(parent => {
        const matchNode = cy.$id(parent.data('key'))
        // 找到左连接的直接父节点
        const parents = matchNode.data('parents')
        if (parents.length > 0) {
          // cy.$id(parents[0]为左连接的左表
          // 如果左表和sourceNode不一侧，那只要判断右表有没有流式表即可
          if (node.successors('node.node_main').union(node).filter(targetNode => { return targetNode.data('key') == parents[0] }).length <= 0) {
            lineNotAllowed = hasSourceLeftJoinStreamingInput
          } else {
             // 如果左表和sourceNode一侧,判断左表都是普通表并且是完整的路径
            const allNodes = matchNode.predecessors('node.node_main').union(sourceNode).union(sourceNode.predecessors('node.node_main'))
            lineNotAllowed = lineError(allNodes, node) // 如果连线完整并且全是普通输入
          }
        } else {
            const allNodes = sourceNode.predecessors('node.node_main').union(sourceNode)
            lineNotAllowed = lineError(allNodes, node) // 如果连线完整并且全是普通输入
        }
      })
      //! 都为流式表-->只需要判断
      // 左有普通没有流式，右有流式
      if (lineNotAllowed) {
        data.flag = false
        data.message = '流式表和普通表左连接计算时，左表父节点必为流式数据表'
        return data
      }
    }
  }
  // else if (sourceType === NODE_TYPES.output && nodeType === NODE_TYPES.jdbc) {
  //   // 输出后面可以连多个JDBC算子，JDBC算子只能连在输出后面
  //   return true;
  // } else if (sourceType === NODE_TYqPES.output && nodeType !== NODE_TYPES.jdbc) {
  //   return false;
  // }
  return data
}

export function getEdgeType (sourceNode, targetNode) {
  if (Object.keys(sourceNode.data('meta')).length &&
  sourceNode.data('status') !== NODE_STATUS.empty &&
  Object.keys(targetNode.data('meta')).length &&
  targetNode.data('status') !== NODE_STATUS.empty) {
    return {
      status: LINE_STYLES.solid,
      dash: [5, 2]
    }
  }
  return {
    status: LINE_STYLES.dot,
    dash: [5, 2]
  }
}

export function bindAll (fns = [], context = null) {
  fns.forEach((fn) => {
      if (!context[fn]) { return }
      context[fn] = context[fn].bind(context)
  })
}

export function haveStatusNode (mainNode) {
  return mainNode.data('status') !== NODE_STATUS.default && mainNode.data('status') !== NODE_STATUS.no_permission
}

export function encodeSvgData (data) {
  return 'data:image/svg+xml;utf8,' + encodeURIComponent(data)
}

const customizeImgUrlMap = {
  '': require('./assets/sys_customize/etl-default.js').default.data,
  behavior: require('./assets/sys_customize/etl-system-behavior.js').default.data,
  diy: require('./assets/sys_customize/etl-system-diy.js').default.data,
  entity: require('./assets/sys_customize/etl-system-entity.js').default.data,
  relation: require('./assets/sys_customize/etl-system-relation.js').default.data,
  track: require('./assets/sys_customize/etl-system-track.js').default.data,
  'in-test': require('./assets/sys_customize/etl-in-test.js').default.data,
  'img-error': require('./assets/sys_customize/etl-img-error.js').default.data
}

export function getNodeBackgroundImage (node) {
  if (node.status === NODE_STATUS.empty || node.status === NODE_STATUS.no_permission) {
    return require('./assets/etl-placehoder.js').default.data
  } else {
    let url = ''
    if (node.type === NODE_TYPES.input && node.originType === 'streaming') {
      url = 'input-streaming'
    } else if (node.type === NODE_TYPES.streaming_output) {
      url = 'output'
    } else if (node.type === NODE_TYPES.streaming_data_aggr) {
      url = 'data_aggr'
    } else if (node.type === NODE_TYPES.customize) {
      if (node.opt_in_test) {
        return customizeImgUrlMap['in-test']
      } else {
        if (node.icon_source) {
          if (node.obj_id) {
            return node.url
          } else {
            return customizeImgUrlMap['img-error']
          }
        } else {
          return customizeImgUrlMap[node.obj_id]
        }
      }
    } else {
      url = node.type
    }
    return require(`./assets/common/etl-${url}.js`).default.data
  }
}

const defaultStatusImg = encodeSvgData(require('./assets/etl-flow-default.js').default.data)
export function getStatusBase64Image (node) {
  const nodeStatus = node.data('status')
  const frameStatus = node.data('frame_status')
  if (frameStatus !== 1) {
    return defaultStatusImg
  }
  switch (nodeStatus) {
    case NODE_STATUS.complete: return encodeSvgData(require('./assets/etl-flow-pass.js').default.data)
    // case NODE_STATUS.no_permission:
    case NODE_STATUS.error: return encodeSvgData(require('./assets/etl-flow-error.js').default.data)
    case NODE_STATUS.updating: return encodeSvgData(require('./assets/etl-flow-update.js').default.data)
    case NODE_STATUS.temporary_lock:
    case NODE_STATUS.permanent_lock: return encodeSvgData(require('./assets/etl-flow-lock.js').default.data)
    default: return defaultStatusImg
  }
}

export function getBorderAndOverlayColor (node, isActive) {
  const frameStatus = node.data('frame_status')
  switch (frameStatus) {
    // case 1: return '#239545'; // 配置成功
    case 2: return '#FF5266' // 配置错误
    default: return '#CCCED4' // 默认样式
  }
}

export function getBorderAndOverlayOpacity (node, isActive) {
  return isActive ? 0.25 : 1
}

export function getBorderStyle (node) {
  const frameStatus = node.data('frame_status')
  if (frameStatus === 0) {
    return 'dashed'
  }
  return 'solid'
}

export function getLineColor (cy) {
  const lineColor = cy.data('line_color')
  return lineColor || DEFAULT_COLOR.line_color
}

// 判断是否是流式输入
export function isStreamingInput (node) {
  return node.type === 'input' && node.originType === 'streaming'
}

// 判断是普通输入表
export function isCommonInput (node) {
  return node.type === 'input' && node.originType !== 'streaming'
}

//
export function getNodeOutputs (node, type) {
  const nodes = node.successors('node.node_main').union(node).filter(node => { return node.data('type') == type })
  return nodes
}

export function getNodeIncomes (node, type) {
  const nodes = node.predecessors('node.node_main').union(node).filter(node => { return node.data('type') == type })
  return nodes
}

export function hasStreamingInputParents (node) {
  const hasStreamingInput = node.predecessors('node.node_main').union(node).some(prev_node => isStreamingInput(prev_node.data()))
  return hasStreamingInput
}

export function hasCommonInputParents (node) {
  const hasCommonInput = node.predecessors('node.node_main').union(node).some(prev_node => isCommonInput(prev_node.data()))
  return hasCommonInput
}

export function lineError (allNodes, node) {
  let allLinePass = false
  let currentToNum = 0
  const topNodes = []
  const commonInputs = []
  let maxToNum = 0
  allNodes.forEach(e => {
    const type = e.data('type')
    const parents = e.data('parents')
    if (parents.length <= 0 && (e.data('type') === 'input')) {
      topNodes.push(e)
    }
    currentToNum = Array.isArray(parents) ? parents.length : 0
    switch (type) {
      case NODE_TYPES.input:
        maxToNum = 0; break
      case NODE_TYPES.output:
      case NODE_TYPES.streaming_output:
      case NODE_TYPES.increment_output:
      case NODE_TYPES.alter_field:
      case NODE_TYPES.streaming_data_aggr:
      case NODE_TYPES.data_aggr:
      case NODE_TYPES.data_filter:
      case NODE_TYPES.id_trans:
      case NODE_TYPES.add_field:
      case NODE_TYPES.map_field:
      case NODE_TYPES.select:
      case NODE_TYPES.table:
      case NODE_TYPES.deduplication:
      case NODE_TYPES.machine_learning:
      case NODE_TYPES.addr_trans:
      case NODE_TYPES.sql:
      case NODE_TYPES.json:
      case NODE_TYPES.vacancy_rate:
          maxToNum = 1; break
      case NODE_TYPES.join:
      case NODE_TYPES.left_join:
          maxToNum = 2; break
      case NODE_TYPES.union:
          maxToNum = 100; break
      case NODE_TYPES.customize:
        maxToNum = e.data('tb_count') || 0; break
    }
    if (e.data('key') == node.data('key')) {
      currentToNum += 1
    }
    allLinePass = currentToNum < maxToNum || allLinePass // allLinePass为true代表没有连线完整
  })
  topNodes.forEach(e => {
    if (e.data('type') === 'input' && e.data('originType') !== 'streaming') {
      commonInputs.push(e)
    }
  })
  return !allLinePass && (commonInputs.length == topNodes.length)
}
