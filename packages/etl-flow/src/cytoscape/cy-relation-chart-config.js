/* eslint-disable */
export const ChartType = {
  default: '',
  generationProcess: 'generationProcess', // 当前数据表生成过程
  associationProfile: 'associationProfile', // 当前数据表关联概况
  elementsSource: 'elementsSource' // 要素来源
}

function getColor (update_status, chartType) {
  if (chartType === ChartType.generationProcess) {
    let statusColor = '#2CBE44'
    switch (update_status) {
      case 1:
        statusColor = '#239545'
        break
      case 2:
        statusColor = '#FF5266'
        break
      case 3:
      case 0:
      case 6:
        statusColor = '#1F71FF'
        break
    }
    return statusColor
  } else if (chartType === ChartType.associationProfile) {
    return '#1F71FF'
  } else if (chartType === ChartType.elementsSource) {
    let statusColor = '#2CBE44'
    switch (update_status) {
      case 1:
        statusColor = '#2CBE44'
        break
      case 0:
        statusColor = '#FF9431'
        break
      case 3:
        statusColor = '#FF9431'
        break
      case 6:
        statusColor = '#FF9431'
        break
      case 2:
        statusColor = '#FF4672'
        break
    }
    return statusColor
  } else {
    return '#fff'
  }
}

export function getCytoscapeStyle (chartType) {
  return [
    /* 全局样式 */
    {
      selector: 'core',
      style: {
        // 鼠标点击空白处的背景
        'active-bg-color': '#000',
        'active-bg-opacity': 0,
        'active-bg-size': 20
      }
    },
    /* 激活的点或边覆盖样式 */
    {
      selector: ':active',
      style: {
        'overlay-color': '#0f0',
        'overlay-padding': 4,
        'overlay-opacity': 0
      }
    },
    /* 主节点样式 */
    {
      selector: 'node.node_main',
      style: {
        width: 168,
        height: 32,
        shape: 'polygon',
        // 'shape-polygon-points': '-1 -1, 0.5 -1, 1 -0.5, 1 1, -1 1',
        'shape-polygon-points': '-1 -1, 0.85 -1, 1 -0.65, 1 1, -1 1',
        // 背景
        'background-color': (e) => getColor(e.data('update_status'), chartType),
        'background-opacity': 0.1,
        'background-blacken': 0,
        'background-fill': 'solid',
        // 边框
        'border-width': 1,
        'border-style': 'solid',
        'border-color': (e) => getColor(e.data('update_status'), chartType),
        'border-radius': 8,
        // 文字
        label: chartType === ChartType.associationProfile ? 'data(name)' : 'data(tb_name)',
        color: (e) => getColor(e.data('update_status'), chartType),
        'font-family': 'Microsoft Yahei',
        'font-size': 12,
        'font-weight': 'normal',
        'text-wrap': 'ellipsis',
        'text-max-width': '100px',
        'line-height': 1.2,
        'text-halign': 'center',
        'text-valign': 'center',
        'min-zoomed-font-size': 10,
        'text-events': 'no'
      }
    },
    /* 分类节点 数据表关联概况独有 */
    {
      selector: 'node.node_classify',
      style: {
        width: 168,
        height: 32,
        shape: 'rectangle',
        'shape-polygon-points': '-1 -1, 1 -1, 1 1, -1 1',
        // 背景
        'background-color': '#FFF',
        'background-opacity': 0.1,
        'background-blacken': 0,
        'background-fill': 'solid',
        // 边框
        'border-width': 1,
        'border-style': 'solid',
        'border-color': (e) => getColor(e.data('update_status'), chartType),
        'border-radius': 8,
        // 文字
        label: 'data(name)',
        color: (e) => getColor(e.data('update_status'), chartType),
        'font-family': 'Microsoft Yahei',
        'font-size': 12,
        'font-weight': 'normal',
        'text-wrap': 'ellipsis',
        'text-max-width': '100px',
        'line-height': 1.2,
        'text-halign': 'center',
        'text-valign': 'center',
        'min-zoomed-font-size': 10,
        'text-events': 'no'
      }
    },
    /* 边样式 */
    {
      selector: 'edge',
      style: {
        width: 2,
        'line-color': '#CCCED4',
        'line-style': 'solid',
        'curve-style': 'taxi', // 'straight'
        opacity: 1,
        'source-arrow-color': '#CCCED4',
        'source-arrow-shape': 'circle',
        'source-arrow-fill': 'hollow',
        'target-arrow-color': '#CCCED4',
        'target-arrow-shape': 'triangle',
        'target-arrow-fill': 'filled',
        'arrow-scale': 1,
        'source-endpoint': 'outside-to-line',
        'target-endpoint': 'outside-to-line',
        'taxi-direction': 'rightward'
      }
    }
  ]
}
