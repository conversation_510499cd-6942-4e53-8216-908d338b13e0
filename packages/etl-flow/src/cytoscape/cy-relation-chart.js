/* eslint-disable */
import Event from './event'
import cytoscape from './lib/cytoscape.umd'
import { ChartType, getCytoscapeStyle } from './cy-relation-chart-config'
import dagre from 'cytoscape-dagre'
cytoscape.use(dagre)
// eslint-disable-next-line no-unused-expressions
!(function (global) {
  class CytoscapeRelationChart extends Event {
    constructor (chartType = ChartType.default) {
      super()
      this.chartType = chartType
    }

    initChart (data = {}, options) {
      this.destroyChart()
      this.options = Object.assign({ dom: '' }, options)
      // this.etlInstance = this.options.etlInstance;
      this._data = Object.assign({}, data)
      const dom =
        typeof this.options.dom === 'string'
          ? document.getElementById(this.options.dom)
          : this.options.dom instanceof HTMLElement
          ? this.options.dom
          : undefined
      if (dom == undefined) {
        throw new Error('未获取到画布dom！！！')
      }
      this.cy = cytoscape({
        container: dom,
        elements: this.convertDataToElements(this._data),
        layout: {
          name: 'preset',
          fit: false, // 自动定位到全局视图，会覆盖 zoom、pan属性
          padding: 30
        },
        style: getCytoscapeStyle(this.chartType),

        // interaction options:
        minZoom: 0.25, // 1e-50,
        maxZoom: 1, // 1e50, fix：在布局时防止放大比例太大
        zoomingEnabled: true,
        userZoomingEnabled: false,
        panningEnabled: true,
        userPanningEnabled: true,
        boxSelectionEnabled: false,
        selectionType: 'single',
        touchTapThreshold: 8,
        desktopTapThreshold: 4,
        autolock: false,
        autoungrabify: false,
        autounselectify: false,

        // rendering options:
        headless: false,
        styleEnabled: true,
        hideEdgesOnViewport: false,
        textureOnViewport: false,
        motionBlur: false,
        motionBlurOpacity: 0.2,
        // wheelSensitivity: 1,
        pixelRatio: 2
      })

      this.cy.ready((e) => {
        this._cyReady(e)
      })
    }

    convertDataToElements (data) {
      const nodes = data.nodeArray.map((e) => this.createNodeElement(e))
      const edges = data.eagesArray.map((e) => this.createEdgeElement(e))
      return [...nodes, ...edges]
    }

    createNodeElement (d) {
      const node = {
        group: 'nodes',
        data: {
          ...d
        },
        selected: false,
        selectable: true,
        locked: false,
        grabbable: true,
        pannable: false,
        classes: ['node_main']
      }
      if (this.chartType === ChartType.associationProfile) {
        if (d.node_type === 'classify') {
          node.classes.push('node_classify')
        }
      }
      return node
    }

    createEdgeElement (d) {
      return {
        group: 'edges',
        data: {
          ...d
        },
        classes: ['edge_main']
      }
    }

    destroyChart () {
      if (this.cy) {
        this.cy.removeAllListeners()
        this.cy.removeData()
        this.cy.removeScratch()
        this.cy.destroy()
      }
      this._data = null
      this.cy = null
    }

    _cyReady () {
      this.setLayout()
      this._initCyEvents()
    }

    setLayout () {
      const options = {
        name: 'dagre',
        fit: true,
        padding: 30,
        nodeSep: 60,
        rankSep: 60,
        rankDir: this.chartType === ChartType.elementsSource ? 'RL' : 'LR',
        minLen: function (edge) {
          return 2
        }
      }
      const layout = this.cy.$('node.node_main, edge').layout(options)
      layout.on('layoutstop', () => {
        this.cy.autolock(true)
      })
      layout.run()
    }

    _initCyEvents () {
      this.cy.on('click', (evt) => {
        const clickTarget = evt.target
        if (!clickTarget || clickTarget === this.cy) {
          // 点击空白处。。。
          this.emit('click-nothing')
        } else if (clickTarget.isNode() && clickTarget.hasClass('node_main')) {
          // 点击节点
          const data = clickTarget.json().data
          this.emit('click', { data: data })
        }
      })

      this.cy.on('tapdragover', 'node.node_main', (evt) => {
        const target = evt.target
        if (target) {
          const pos = target.renderedPosition()
          const data = target.json().data
          this.emit('mouseover', { data: data, pos: pos })
        }
      })

      this.cy.on('tapdragout', 'node.node_main', (evt) => {
        const target = evt.target
        if (target) {
          const data = target.json().data
          this.emit('mouseout', { data: data })
        }
      })
    }
  }

  if (typeof module === 'object' && module.exports) {
    module.exports = CytoscapeRelationChart
  } else if (typeof define === 'function' && define.amd) {
    define(function () {
      return CytoscapeRelationChart
    })
  }
  global.CytoscapeRelationChart = CytoscapeRelationChart
})(window || this || global)
