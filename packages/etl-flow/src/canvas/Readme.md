示例
```vue
<template>
  <div class="relative h-200">
    <etlCanvas ref="etlCanvas" :nodeDesc="formulaDesc" :showTooltip="true" />
  </div>
</template> 
<script>
const meta =  {
  "zoom": 1,
  "is_comment": true,
  "line_type": "bezier",
  "node_meta": [
    {
      "status": 0,
      "loc": "552 328",
      "nodeType": "translate",
      "markColor": "",
      "title": "机器学习",
      "commonlyUse": false,
      "meta": {
        "features": [
          {
              "data_type": "number",
              "fieldName": "城市人均犯罪率",
              "name": "城市人均犯罪率",
              "fid": "fke9da829a"
          },
          {
              "data_type": "number",
              "fieldName": "住宅用地比率",
              "name": "住宅用地比率",
              "fid": "fkbb6b8a64"
          },
          {
              "data_type": "number",
              "fieldName": "住宅非商业尺寸",
              "name": "住宅非商业尺寸",
              "fid": "fk630e3194"
          },
          {
              "data_type": "number",
              "fieldName": "查尔斯河虚拟变量",
              "name": "查尔斯河虚拟变量",
              "fid": "fk6ac9d507"
          },
          {
              "data_type": "number",
              "fieldName": "环保指数",
              "name": "环保指数",
              "fid": "fkbe7a9e16"
          },
          {
              "data_type": "number",
              "fieldName": "每栋住宅的房间数",
              "name": "每栋住宅的房间数",
              "fid": "fk5d6a5578"
          },
          {
              "data_type": "number",
              "fieldName": "1940年前建筑比例",
              "name": "1940年前建筑比例",
              "fid": "fk7bd9d8f5"
          },
          {
              "data_type": "number",
              "fieldName": "加权距离",
              "name": "加权距离",
              "fid": "fkcc61b6d1"
          },
          {
              "data_type": "number",
              "fieldName": "高速便利指数",
              "name": "高速便利指数",
              "fid": "fkb923f6e3"
          },
          {
              "data_type": "number",
              "fieldName": "不动产率万元",
              "name": "不动产率万元",
              "fid": "fk4023504a"
          },
          {
              "data_type": "number",
              "fieldName": "教师学生比例",
              "name": "教师学生比例",
              "fid": "fkec45719b"
          },
          {
              "data_type": "number",
              "fieldName": "黑人比例",
              "name": "黑人比例",
              "fid": "fk5ae881b8"
          },
          {
              "data_type": "number",
              "fieldName": "房东低收入比例",
              "name": "房东低收入比例",
              "fid": "fkc764df35"
          }
        ],
        "title": "机器学习",
        "new_field_name": "输出样本",
        "ml_id": "ml_691cd1f7015f43ca83d8d0cc56f61137",
        "select_ml": {
            "status": 0,
            "name": "房价预测",
            "selectHit": true,
            "ml_id": "ml_691cd1f7015f43ca83d8d0cc56f61137",
            "operator": 2,
            "type": 1
        },
        "field_dict": {
            "高速便利指数": "高速便利指数",
            "不动产率万元": "不动产率万元",
            "黑人比例": "黑人比例",
            "查尔斯河虚拟变量": "查尔斯河虚拟变量",
            "住宅非商业尺寸": "住宅非商业尺寸",
            "城市人均犯罪率": "城市人均犯罪率",
            "教师学生比例": "教师学生比例",
            "房东低收入比例": "房东低收入比例",
            "环保指数": "环保指数",
            "加权距离": "加权距离",
            "每栋住宅的房间数": "每栋住宅的房间数",
            "1940年前建筑比例": "1940年前建筑比例",
            "住宅用地比率": "住宅用地比率"
        }
      },
    "parents": [
        "input977b0ed02715etl"
    ],
    "key": "machine_learning8f249024389eetl",
    "frame_status": 1,
    "type": "machine_learning",
    "id": "machine_learning8f249024389eetl"
    },
        {
            "status": 1,
            "loc": "357 329",
            "nodeType": "base_tb",
            "is_streaming": 0,
            "data_count": 709,
            "title": "流式样本输出",
            "commonlyUse": false,
            "cid": "input977b0ed02715etl_status_node",
            "comment": null,
            "streaming_node": null,
            "parents": [],
            "type": "input",
            "meta": {
                "title": "样本数据输出_样本",
                "fields": [
                    {
                        "name": "城市人均犯罪率",
                        "title": "城市人均犯罪率",
                        "formula": null,
                        "type": "double",
                        "id": "fke9da829a",
                        "desc": ""
                    },
                    {
                        "name": "住宅用地比率",
                        "title": "住宅用地比率",
                        "formula": null,
                        "type": "double",
                        "id": "fkbb6b8a64",
                        "desc": ""
                    },
                    {
                        "name": "住宅非商业尺寸",
                        "title": "住宅非商业尺寸",
                        "formula": null,
                        "type": "double",
                        "id": "fk630e3194",
                        "desc": ""
                    },
                    {
                        "name": "查尔斯河虚拟变量",
                        "title": "查尔斯河虚拟变量",
                        "formula": null,
                        "type": "double",
                        "id": "fk6ac9d507",
                        "desc": ""
                    },
                    {
                        "name": "环保指数",
                        "title": "环保指数",
                        "formula": null,
                        "type": "double",
                        "id": "fkbe7a9e16",
                        "desc": ""
                    },
                    {
                        "name": "每栋住宅的房间数",
                        "title": "每栋住宅的房间数",
                        "formula": null,
                        "type": "double",
                        "id": "fk5d6a5578",
                        "desc": ""
                    },
                    {
                        "name": "1940年前建筑比例",
                        "title": "1940年前建筑比例",
                        "formula": null,
                        "type": "double",
                        "id": "fk7bd9d8f5",
                        "desc": ""
                    },
                    {
                        "name": "加权距离",
                        "title": "加权距离",
                        "formula": null,
                        "type": "double",
                        "id": "fkcc61b6d1",
                        "desc": ""
                    },
                    {
                        "name": "高速便利指数",
                        "title": "高速便利指数",
                        "formula": null,
                        "type": "double",
                        "id": "fkb923f6e3",
                        "desc": ""
                    },
                    {
                        "name": "不动产率万元",
                        "title": "不动产率万元",
                        "formula": null,
                        "type": "double",
                        "id": "fk4023504a",
                        "desc": ""
                    },
                    {
                        "name": "教师学生比例",
                        "title": "教师学生比例",
                        "formula": null,
                        "type": "double",
                        "id": "fkec45719b",
                        "desc": ""
                    },
                    {
                        "name": "黑人比例",
                        "title": "黑人比例",
                        "formula": null,
                        "type": "double",
                        "id": "fk5ae881b8",
                        "desc": ""
                    },
                    {
                        "name": "房东低收入比例",
                        "title": "房东低收入比例",
                        "formula": null,
                        "type": "double",
                        "id": "fkc764df35",
                        "desc": ""
                    },
                    {
                        "name": "房价中位数",
                        "title": "房价中位数",
                        "formula": null,
                        "type": "double",
                        "id": "fk039a4e4e",
                        "desc": ""
                    }
                ],
                "storage_type": 1,
                "tb_id": "tb_48f8ecfef7e2413888f5e332de62b9c4",
                "pt_unit": null,
                "partition_info": {
                    "interval": null,
                    "empty": true,
                    "unit": null
                },
                "outter_app_inner_uid": null
            },
            "frame_status": 1,
            "key": "input977b0ed02715etl",
            "highlight": false,
            "__gohashid": null,
            "originType": "flow",
            "id": "input977b0ed02715etl",
            "markColor": null
        },
        {
            "status": 1,
            "loc": "769 324",
            "nodeType": "translate",
            "markColor": "",
            "data_count": 709,
            "title": "结果输出",
            "commonlyUse": true,
            "cid": "output658f698cb4aeetl_status_node",
            "meta": {
                "maptb_info": {},
                "tagtb_info": null,
                "partition_unit": null,
                "is_increment": 0,
                "title": "结果输出",
                "partition_field": null,
                "data_process_mode": null,
                "partition_field_name": null,
                "storage": "dc0b603f3ff84e598bb76acf0732b30f",
                "folder_name": "机器学习",
                "sdtb_info": {},
                "tb_id": "tb_38ba1590a56b4e258ce29f5688daf44f",
                "folder_id": "folder_4c6c9c149c725b0c243168c9d044e219",
                "is_tagtb": null,
                "is_sdtb": 0,
                "is_maptb": 0
            },
            "parents": [
                "machine_learning8f249024389eetl"
            ],
            "key": "output658f698cb4aeetl",
            "frame_status": 1,
            "type": "output",
            "id": "output658f698cb4aeetl"
        }
    ],
    "is_trigger_auto_layout": false,
    "position": "0 0",
    "line_meta": [
        {
            "status": "solid",
            "from": "input977b0ed02715etl",
            "target": "machine_learning8f249024389eetl",
            "to": "machine_learning8f249024389eetl",
            "dash": null,
            "source": "input977b0ed02715etl",
            "id": "e1ac8a1c-ca31-4798-a1d2-9741d676074c"
        },
        {
            "status": "solid",
            "from": "machine_learning8f249024389eetl",
            "target": "output658f698cb4aeetl",
            "source": "machine_learning8f249024389eetl",
            "dash": null,
            "to": "output658f698cb4aeetl",
            "id": "847d89b6-2944-41d3-9d8b-77b5269dce83"
        }
    ]
}

// const meta = {
//   "zoom": 1,
//   "is_comment": true,
//   "line_type": "bezier",
//   "node_meta": [
//       {
//           "status": 1,
//           "loc": "669 305",
//           "nodeType": "base_tb",
//           "is_streaming": 0,
//           "title": "T_SB_ZNYC_DYHGQ_电压互感器",
//           "highlight": false,
//           "cid": "input1fc20c4e3981etl_status_node",
//           "comment": null,
//           "streaming_node": null,
//           "type": "input",
//           "meta": {},
//           "frame_status": 1,
//           "key": "input1fc20c4e3981etl",
//           "__gohashid": null,
//           "parents": [],
//           "commonlyUse": false,
//           "originType": "excel",
//           "id": "input1fc20c4e3981etl",
//           "markColor": null
//       }
//   ],
//   "is_trigger_auto_layout": false,
//   "position": "0 0",
//   "line_meta": []
// }
const formulaDesc = {}
// const formulaDesc = {
//   output: '将父节点的数据进行输出，保存为实体数据，生成一张新表',
//   increment_output: '将父节点的数据进行输出，保存为实体数据，生成一张新表',
//   streaming_output: '将父节点的数据进行输出，保存为实体数据或流式数据，生成一张新表',
//   alter_field: '修改父节点的字段名称、类型及描述信息',
//   data_aggr: '对父节点的数据，按照指定的字段做聚合操作（SQL语义上等同于GROUP BY）',
//   streaming_data_aggr: '对父节点的数据，按照指定的字段做聚合操作（SQL语义上等同于GROUP BY）',
//   data_filter: '通过条件或者表达式两种方式对数据进行过滤筛选',
//   add_field: '使用公式处理数据生成新的字段（如：`a` + `b`）',
//   select: '设置父节点的输出字段',
//   join: '根据2个父节点的关联字段做交集，仅保留同时匹配的数据（SQL语义上等同于INNER JOIN）',
//   left_join: '根据2个父节点的关联字段做左连接，除匹配的数据外，还保留左表的未匹配数据（SQL语义上等同于LEFT JOIN）',
//   anti_join: '根据2个父节点的关联字段做差集，仅保留左表未匹配的数据（SQL语义上等同于ANTI JOIN）',
//   union: '将2到多个父节点数据，按照指定的字段对应关系进行合并（SQL语义上等同于UNION ALL）',
//   full_join: '根据2个父节点的关联字段做全连接，除匹配的数据外，会同时保留2个父节点未匹配的数据（SQL语义上等同于FULL JOIN）',
//   sql: '使用标准SQL语句对数据进行加工处理。注：支持使用BDP扩展的SQL语法',
//   table: '对父节点设置输出字段，修改字段名称及顺序',
//   deduplication: '根据指定的去重字段及排序规则，对父节点数据进行去重处理',
//   addr_trans: '将字符串地址信息转换为经纬度数据（如：“北京”，转换为：39.9，116. 3）',
//   id_trans: '将15位的身份证号转为18位',
//   map_field: '算子中包含code、value等字典表信息，可以将任意表中的某一字段与字典进行映射翻译',
//   machine_learning: '使用BDP机器学习模块，训练生成的模型，对父算子数据进行机器学习算法处理，处理结果存于新的字段中',
//   json: '对选定的JSON文本字段进行解析，追加输出为新字段，例如：{"a": 1, "b": "str"}，可新增a、b两个字段',
//   number_handle: '对父节点数据进行加、减、乘、除、取余运算，并对运算生成的结果字段输出',
//   date_handle: '对父节点数据可实现对输入时间字段的逻辑运算和提取等操作，支持的时间处理方式包括时间计算、时间提取、时间差',
//   string_handle: '对父节点数据可实现对输入字段中的值进行清洗、处理等操作，支持的字符串处理方式包括字符串拼接、按位数截取、字符串拆分等',
//   vacancy_rate: '对选定的字段进行空值率计算。字段空值率：该字段下空值行数/总行数'
// }
export default {
  data() {
    return { 
      formulaDesc: formulaDesc
    }
  },
  created(){
    setTimeout(()=>{
      this.init()
    },0)
  },
  methods: {
    init(){
      this.$refs.etlCanvas.updateCanvas(_.keys(meta).length > 0 ? meta : { node_meta: [], line_meta: [] }, { mode: 'read',is_debug: false})
    }
  }
}
</script>
```