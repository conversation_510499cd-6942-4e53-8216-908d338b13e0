<template>
  <div class="w-full h-full relative">
    <div id="myDiagramDiv" class="absolute w-full" :class="canvasLayoutClass">
    </div>
    <div class="canvas-tooltip-layout w-72 p-4 shadow-container-c200 absolute bg-mono-100 rounded-md" :style="tooltipData.pos" v-show="tooltipData.show">
      <ul class="w-full">
        <li class="flex">
          <label class="w-15 text-type-700">{{tooltipData.title.label}}</label>
          <p class="flex-auto w-0">{{tooltipData.title.name}}</p>
        </li>
        <li v-show="tooltipData.showDesc" class="flex">
          <label class="w-15 text-type-700">{{tooltipData.desc.label}}</label>
          <p class="flex-auto w-0">{{tooltipData.desc.content}}</p>
        </li>
      </ul>
    </div>
    <div class="absolute left-0 bottom-0 px-4 rounded-lg hover:bg-mono-a-200 w-auto leading-8 h-8 cursor-pointer" @click="switchLegend" v-if="mode === 'read'">
      <hz-icon hzName="layer-ctrl" /> 画布比例
      <etlCanvasLegend class="absolute left-0 bottom-8 z-50" v-show="displayLegend" />
    </div>
    <div class="footer-bar" v-if="mode === 'edit'">
      <ul class="float-right">
        <li><hz-icon hzName="remark" /> 隐藏节点注释</li><li><hz-icon hzName="curve" /> 曲线</li><li><hz-icon hzName="layout" /> 一键布局</li>
      </ul>
      <ul>
        <li v-show="showLegend" class=" relative" @click="switchLegend" >
          <hz-icon hzName="layer-ctrl" /> 画布比例
          <etlCanvasLegend class="absolute left-0 bottom-8 z-50" v-show="displayLegend" @closeLegend="closeLegend" />
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import '../cytoscape/cy-chart.js'
import etlCanvasLegend from '../canvas-legend/Index.vue'
// eslint-disable-next-line no-undef
let cytoInstance = new CytoscapeChart()
/**
 * <AUTHOR>
 * @displayName etlCanvas 建模画布组件
 */
export default {
  name: 'etlCanvas',
  components: {
    etlCanvasLegend
  },
  props: {
    /**
     * 节点描述
     */
    nodeDesc: Object,
    /**
     * 画布模式 read | edit
     */
    mode: {
      type: String,
      default: 'read'
    },
    /**
     * 是否展示tooltip
     */
    showTooltip: {
      type: Boolean,
      default: true
    },
    /**
     * 是否展示图例
     */
    showLegend: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      // cytoInstance: null,
      nodeArray: [], // 节点信息
      lineArray: [], // 连线信息
      tooltipData: {
        title: {}, // 节点名称
        desc: {}, // 节点描述
        tbId: '', // 表名称
        handle: false,
        show: false
      },
      displayLegend: false,
      canvasLayoutClass: ''
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if (this.mode === 'edit') {
        this.canvasLayoutClass = 'h-calc8'
      } else {
         this.canvasLayoutClass = 'h-full'
      }
    },
    updateCanvas (meta, option) {
        // this.flowService.nodeMeta = meta.node_meta;
      if (meta && Object.keys(meta).length > 0) {
          this.nodeArray = meta.node_meta
          this.lineArray = meta.line_meta
          this.lineArray.forEach((item) => {
            if (item.status === 'dot') {
              item.dash = [5, 2]
            } else {
              item.dash = null
            }
          })
      } else {
          meta.line_type = 'bezier'
      }

      if (cytoInstance) {
        // cytoInstance.favoriteOptMap = this.flowService.favoriteOptMap;
        cytoInstance.initChart(meta, option)
        if (!(!!option && !!option.mode && option.mode === 'read')) {
            this.toggleComment(meta.is_comment)
        }
        this.initEvent()
      }
      this.noneMeta = !(meta.node_meta && meta.node_meta.length > 0)
    },
    initEvent () {
      if (this.showTooltip) {
        cytoInstance.on('showTooltip', (nodeData) => {
          this.showTooltipHandle(nodeData)
        })
        cytoInstance.on('hideTooltip', () => {
          this.tooltipData.show = false
        })
      }
    },
    // ToDo 输出表操作（查看详情 | 新建图表）
    showTooltipHandle (nodeData) {
      this.tooltipData.title = {
        label: nodeData.data.type !== 'output' || nodeData.data.type !== 'input' ? '算子名称' : '数据表',
        name: nodeData.data.title
      }
      if (nodeData.data.type !== 'input') {
        this.tooltipData.desc = {
          label: '算子描述',
          content: this.nodeDesc[nodeData.data.type]
        }
        this.tooltipData.showDesc = true
      } else {
        this.tooltipData.showDesc = false
      }
      this.tooltipData.pos = {
        left: (nodeData.hoverPosition.x + (nodeData.hoverPosition.width / 2 - 5)) + 'px',
        bottom: (nodeData.hoverPosition.y + (nodeData.hoverPosition.height - 5)) + 'px'
      }
      this.tooltipData.show = true
    },
    switchLegend () {
      this.displayLegend = !this.displayLegend
    },
    closeLegend () {
      this.displayLegend = false
    }
  },
  destroyed () {
    if (cytoInstance) {
      cytoInstance.destroyChart(true)
      cytoInstance = null
    }
  }
}
</script>
<style scoped>
  .footer-bar{
    @apply absolute leading-8 bottom-0 left-0 w-full border border-mono-a-300 border-solid;

    & ul:first-child li {
      @apply inline-block px-2 border-l border-mono-a-300 border-solid cursor-pointer hover:bg-mono-a-200;
    }
    & ul:last-child li {
      @apply inline-block px-2 border-r border-mono-a-300 border-solid cursor-pointer hover:bg-mono-a-200;
    }
  }
</style>
