<template>
  <div class="etl-canvas-legend">
    <span class="float-right cursor-pointer -mt-1">
      <hz-icon hzName="f-close" className="hz-action-icon" @click="close()"/>
    </span>
    <div class="head">
      画布图例
    </div>
    <div class="item">
      <p>节点配置状态</p>
      <ul>
        <li>
          <i class="border border-dashed border-type-600">
            <hz-icon hzName="sql-script-gray" className="w-8 h-8 text-teal-900"/>
          </i>
          <span>未配置</span>
        </li>
        <li>
          <i class="border border-solid border-type-600">
            <hz-icon hzName="sql-script-gray" className="w-8 h-8 text-teal-900"/>
          </i>
          <span>配置成功</span>
        </li>
        <li>
          <i class="border border-solid border-red-900">
            <hz-icon hzName="sql-script-gray" className="w-8 h-8 text-teal-900"/>
          </i>
          <span>配置错误</span>
        </li>
      </ul>
    </div>
    <div class="item">
      <p>节点运行状态</p>
      <ul>
        <li>
          <i class="border border-solid border-type-600 relative">
            <hz-icon hzName="sql-script-gray" className="w-8 h-8 text-teal-900"/>
            <hz-icon hzName="success" class="up-icon" className="w-2.5 h-2.5 text-lime-900"/>
          </i>
          <span>运行成功</span>
        </li>
        <li>
          <i class="border border-solid border-type-600 relative">
            <hz-icon hzName="sql-script-gray" className="w-8 h-8 text-teal-900"/>
            <hz-icon hzName="warning" class="up-icon" className="w-2.5 h-2.5 text-red-900"/>
          </i>
          <span>运行失败</span>
        </li>
        <li>
          <i class="border border-solid border-type-600 relative">
            <hz-icon hzName="sql-script-gray" className="w-8 h-8 text-teal-900"/>
            <hz-icon hzName="check-checking" class="up-icon" className="w-2.5 h-2.5 text-primary-900"/>
          </i>
          <span>运行中</span>
        </li>
        <li>
          <i class="border border-solid border-type-600 relative">
            <hz-icon hzName="sql-script-gray" className="w-8 h-8 text-teal-900"/>
            <hz-icon hzName="lock" class="up-icon" className="w-2.5 h-2.5 text-orange-900"/>
          </i>
          <span>锁定状态</span>
        </li>
      </ul>
    </div>
    <div class="item">
      <p>连线状态</p>
      <ul>
        <li>
          <i>
            <hz-icon hzName="line-hight" className="w-8 h-8 text-mono-500"/>
          </i>
          <span>正常状态</span>
        </li>
        <li>
          <i >
            <hz-icon hzName="line-hight" className="w-8 h-8 text-teal-900"/>
          </i>
          <span>未配置</span>
        </li>
        <li>
          <i class="border-solid border-type-600">
            <hz-icon hzName="line-hight" className="w-8 h-8 text-orange-900"/>
          </i>
          <span>拖拽连线</span>
        </li>
        <li>
          <i class="border-solid border-type-600">
            <hz-icon hzName="line-hight" className="w-8 h-8 text-primary-900"/>
          </i>
          <span>高亮路径</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import { hzIcon } from '@hertz/base-components'
/**
 * <AUTHOR>
 * @displayName etlCanvasLegend 建模画布图例组件
 */
export default {
  name: 'etlCanvasLegend',
  components: {
    hzIcon
  },
  methods: {
    close () {
      /**
       * 关闭图例事件
       */
      this.$emit('closeLegend')
    }
  }
}
</script>
<style scoped>
.etl-canvas-legend{
  @apply px-6 pt-4 pb-6 w-82.5 shadow-container-c300 rounded-md bg-mono-100;
  & .head{
    @apply leading-4 text-type-800 font-semibold;
  }
  & .item {
    @apply mt-4;
  }
  & .item p {
    @apply text-type-700 pb-2;
  }
  & .item li{
    @apply inline-block w-12 text-center mr-6;
    & > i {
      @apply block text-center w-8 h-8 mx-auto rounded-sm;
      & i{
        @apply -m-px;
      }
    }
    & span {
      @apply block leading-4 mt-2
    }
  }
  & .item li:last-child {
    @apply mr-0
  }
  & .up-icon{
    @apply absolute right-0 top-0 bg-background-100
  }
}
</style>
