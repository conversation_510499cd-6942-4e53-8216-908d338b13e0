<template>
  <div class="field-list">
    <div class="search-wrap" v-if="showSearch">
      <a-input placeholder="请输入字段名称" v-model="keyword" @change="searchValueChange">
        <template #prefix>
          <hz-icon hzName="search" />
        </template>
      </a-input>
    </div>
    <ul class="list">
      <li v-for="item in fields" :key="item.field_id">
        <a-tooltip :title="item.title" placement="topLeft">
          <p class="flex-auto truncate">
            <hz-icon :hzName="item.type | fieldTypeShowIcon" class="-mt-0.5 mr-1" className="text-primary-900"/>{{ item.title }}
          </p>
        </a-tooltip>
        <div class="operator-layer" v-if="showOperatorWrap">
          <slot name="operator" :item="item" >
          </slot>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
import { debounce } from 'lodash'
export default {
  name: 'FieldFlatList',
  props: {
    /**
     * 是否展示搜素框
     */
    showSearch: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示字段右侧操作区，可通过插槽operator自定义操作区按钮
     */
    showOperatorWrap: {
      type: Boolean,
      default: true
    },
    /**
     * 字段列表数组
     */
    fieldList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    fieldList (newValue) {
      this.fields = [...newValue]
    }
  },
  data () {
    return {
      keyword: '',
      fields: []
    }
  },
  methods: {
    searchValueChange: debounce(function () {
      const list = []
      this.fieldList.forEach(item => {
        if (item.title.toLowerCase().indexOf(this.keyword.toLowerCase()) > -1) {
          list.push(item)
        }
      })
      this.fields = [...list]
    },
    500,
    {
      trailing: true
    })
  }
}
</script>
<style lang="scss" scoped>
.field-list {
  @apply flex-auto overflow-hidden flex flex-col;
  .search-wrap {
    @apply mx-4 mt-2;
  }
}
.list {
  @apply leading-9 flex-auto overflow-auto px-4 my-2;
  li{
    @apply hover:bg-mono-a-100 cursor-pointer pl-4 flex rounded-md;
    .operator-layer {
      @apply w-4 mr-2 invisible;
    }
  }
  li:hover {
    .operator-layer {
      @apply visible;
    }
  }
}
</style>
