示例
```vue
<template>
  <div class="w-98 h-54 p-4 bg-mono-100 rounded-2xl top-11 right-0 z-10 shadow-container-c500">
    <SeniorSearchInfo :seniorSearchInfo="seniorAllCondition" ref="seniorSearchInfo" @selectOne="selectOne($event)">
    </SeniorSearchInfo>
  </div>
 </template>
 
<script>
const originSeniorAllCondition = [
   {
     value: 'auth_status',
     title: '权限状态',
     statusList: [
       {
         value: null,
         title: '全部',
         checked: true
       },
       {
         value: 1,
         title: '有',
         checked: false
       },
       {
         value: 0,
         title: '无',
         checked: false
       }
     ]
   },
   {
     value: 'depart_type',
     title: '是否分区表',
     statusList: [
       {
         value: null,
         title: '全部',
         checked: true
       },
       {
         value: 0,
         title: '是',
         checked: false
       },
       {
         value: 1,
         title: '否',
         checked: false
       },
     ]
   },
   {
     value: 'table_types',
     title: '表类型',
     statusList: [
       {
         value: null,
         title: '全部',
         checked: true
       },
       {
         value: 0,
         title: '普通表',
         checked: false
       },
       {
         value: 2,
         title: '流式表',
         checked: false
       },
       {
         value: 1,
         title: '在线表',
         checked: false
       }
     ]
   }
 ]
export default {
  data() {
    return { 
     seniorAllCondition: originSeniorAllCondition
    }
  },
  methods: {
    selectOne (data) {
      // 全选不可取消
     if (data.status.value == null && data.status.checked) return
     if (data.status.value == null && !data.status.checked) {
       setClasssifyChecked({ index: data.index, checked: false })
       return
     }
     data.status.checked = !data.status.checked
     if (data.status.checked) {
       store.commit('seniorSearchInfo/setConditionChecked', { index: data.index, checked: false, value: null })
     }
     if (!data.status.checked) {
       const currentCondition = state.seniorAllCondition[data.index]
       if (_filter(currentCondition.statusList, condition => condition.checked && condition.value != null).length <= 0) {
         store.commit('seniorSearchInfo/setConditionChecked', { index: data.index, checked: true, value: null })
       }
     }
    },
    setClasssifyChecked ( payload) {
      const currentCondition = this.seniorAllCondition[payload.index]
      _each(currentCondition.statusList, status => {
        status.checked = status.value !== null ? payload.checked : !payload.checked
      })
      store.commit('seniorSearchInfo/resetPrevSeniorCondition')
    },
  }
}
</script>
```

