<template>
  <div class="w-full h-full relative" v-clickoutside="close">
    <!-- <p class="text-type-800 leading-8 font-semibold text-sm mb-2">高级搜索</p> -->
    <dl class="search-content overflow-auto ml-2">
      <div v-for="(condition,index) in seniorSearchInfo" :key="condition.value">
        <dt class="text-type-700 font-semibold w-15 mb-2 inline-block">{{condition.title}}</dt>
        <dd class="mb-4 ml-4 inline-block">
          <ul>
            <li class="inline-block mr-2 pl-3 pr-3 pt-1.5 pb-1.5 rounded-5.5 bg-mono-300 cursor-pointer hover:text-primary-900 mb-2"
                :class="{'active': status.checked}"
                v-for="status in condition.statusList" :key="status.value"
                @click="selectOne({index,status})">
                {{status.title}}
            </li>
          </ul>
        </dd>
      </div>
    </dl>
    <div class="search-footer text-right">
      <a-button type="primary-text text-primary-900 font-semibold" @click="search">搜索</a-button>
      <a-button type="primary-text text-primary-900 font-semibold" @click="reset">重置</a-button>
    </div>
  </div>
</template>

<script>
/**
 * @displayName SeniorSearchInfo 高级搜索弹层
 */
export default {
  name: 'seniorSearchInfo',
  props: {
    /**
     * 高级搜索的搜索列表，结构如下：
     * [{value: '**',title:'**',statusList:'[**]'}]
     */
    seniorSearchInfo: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      selectSeniorCondition: []
    }
  },
  watch: {
    seniorSearchInfo: {
      handler (val) {
        this.selectSeniorCondition = val
      },
      deep: true
    }
  },
  mounted () {
    console.log('searchList', this.seniorSearchInfo)
  },
  methods: {
    close () {
      this.$emit('close')
    },
    selectOne (item) {
      this.$emit('selectOne', item)
    },
    reset () {
      this.$emit('reset')
    },
    search () {
      this.$emit('search')
    }
  }
}
</script>

<style scoped>
li {
  transition: all 0.2s ease-in-out;
}
.active {
  @apply text-primary-900 bg-primary-a-100 font-semibold;
}
</style>
