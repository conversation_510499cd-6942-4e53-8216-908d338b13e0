示例
```vue
<template>
  <div>
    <SeniorSearchList :searchInfoList="searchInfoList" ref="SeniorSearchList" @deleteCondtion="onDeleteCondtion($event)" @clearCondition="onClearCondition">
    </SeniorSearchList>
  </div>
 </template>
 
<script>
const searchInfoList = [
    {
        "value": "depart_type",
        "title": "是否分区表",
        "statusList": [
            {
                "value": 1,
                "title": "是",
                "checked": true
            }
        ]
    },
    {
        "value": "table_types",
        "title": "表类型",
        "statusList": [
            {
                "value": "0",
                "title": "普通表",
                "checked": true
            },
            {
                "value": "1",
                "title": "流式表",
                "checked": true
            }
        ]
    }
]
export default {
  data() {
    return { 
     searchInfoList: searchInfoList
    }
  },
  methods: {
    onDeleteCondtion(data) {
      const currentSelectCondition = this.searchInfoList[data.index]
      currentSelectCondition.statusList.splice(data.statusIndex, 1)
    },
    onClearCondition() {
      this.searchInfoList = []
    }
  }
}
</script>
```

