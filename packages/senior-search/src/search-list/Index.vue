<template>
  <div class="condition-container left-0 w-full flex box-border">
    <dl class="w-calc33 overflow-auto">
      <template v-for="(condition,index) in searchInfoList">
        <div class="rounded-2xl border-mono-a500 border-solid border text-type-800 h-7 leading-5 pl-1.5 pr-1.5 pt-0.75 pb-0.75 inline-block ml-2 hover:border-primary-900 relative"
            :key="condition.value"
            v-if="condition.statusList.length > 0">
          <dt class="inline-block">{{condition.title}}:</dt>
          <dd class="inline-block ml-1" v-for="(status,statusIndex) in condition.statusList" :key="status.value">
            <span>{{status.title}}</span>
            <hz-icon hzName="close-dense" className="h-5 w-5 text-type-700 align-bottom cursor-pointer" class="-mt-0.5" @click="deleteCondtion({value:condition.value,index,status,statusIndex})"></hz-icon>
          </dd>
        </div>
      </template>
    </dl>
    <template v-if="searchInfoList.length > 0">
      <a-button type="text w-27 p-0 mr-4" size="small"  @click="clearCondition">
        <hz-icon hzName="trash" className="h-4 w-4 text-type-700 align-bottom cursor-pointer mr-1"></hz-icon>清空筛选条件
      </a-button>
    </template>
  </div>
</template>

<script>
/**
 * @displayName SeniorSearchList 高级搜索选中列表
 */
export default {
  name: 'seniorSearchList',
  props: {
    /**
     * 高级搜索的搜索列表，结构如下：
     * [{value: '**',title:'**',statusList:'[**]'}]
     */
    searchInfoList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      selectSeniorCondition: []
    }
  },
  watch: {
    searchInfoList: {
      handler (val) {
        this.selectSeniorCondition = val
      },
      deep: true
    }
  },
  mounted () {
    console.log('searchList', this.searchInfoList)
  },
  methods: {
    deleteCondtion (condtion) {
      this.$emit('deleteCondtion', condtion)
      // this.$store.commit('search/deleteCondition', condtion)
      // emitter.emit('modelSearch')
    },
    clearCondition () {
      this.$emit('clearCondition')
      // this.$store.commit('search/resetSelectCondition')
      // this.$store.commit('search/resetSeniorCondition')
      // emitter.emit('modelSearch')
    }
    // deleteClassify (item) {
    //   this.$store.commit('search/deleteClassify', item)
    //   emitter.emit('modelSearch')
    // },
    // showIcon (item) {
    //   // 有影响，所以可以重新clone一份，暂时不用不改了
    //   this.$store.commit('search/showIcon', item)
    // },
    // hideIcon (item) {
    //   this.$store.commit('search/showIcon', item)
    // }

  }
}
</script>

<style lang="scss" scoped>

</style>
