<template>
<div class="h-20 relative">
  <div class="approve-step overflow-x-auto pt-5.5 pb-6 ml-15 mr-15">
    <div class="box-border m-0 p-0 text-type-800 text-xs flex w-full">
        <div class=" steps-item pb-1 min-w-25 whitespace-nowrap relative inline-block flex-1 overflow-hidden align-top"  v-for="(item, index) in steplist" :key="index">
          <div class="steps-item-container leading-6 outline-none">
            <!-- 状态流程设置 -->
            <div class="steps-item-icon" :class="{'current-step-icon': item.status === 1, 'success-step-icon': item.status ===2, 'edit-step-icon': item.status === 0, 'error-step-icon': item.status === 3}">
              <span class="edit-btn" v-if="item.status === 0">
                <!-- <hz-icon hzName="edit" className="w-2.5 h-2.5 absolute top-1 left-1 text-background-100 z-20"></hz-icon> -->
                <hz-icon hzName="edit" className="w-2.5 h-2.5 absolute -top-1 left-3.5 text-background-100 z-20"></hz-icon>
              </span>
            </div>
            <!-- 标题设置 -->
            <div class="steps-item-content inline-block align-top">
              <div class="steps-item-content relative inline-block pl-1.5 pr-1.5 text-xs leading-6" :class="{'overflow-x-hidden inline-flex': index == steplist.length-1 }" :title="item.name">{{ item.name }}</div>
              <div class="text-type-600 max-w-10 whitespace-normal leading-4" v-if="type!=='create'"><span class="inline-block w-14 transform scale-85 pl-1">{{ stepInfo[item.status]}}</span> </div>
            </div>
          </div>
        </div>
    </div>
    <!-- 状态右上角设置 -->
    <div v-if="type !=='create'" class="inline-block absolute top-0 right-0 h-6 pl-2 pr-2 pt-0.5 pb-0.5 text-xs leading-5 text-general-100 font-semibold" :style="{backgroundColor: bgColor[status], borderRadius: '2px 8px'}">
      <span>{{stepStatus[status]}}</span>
    </div>
  </div>
</div>
</template>

<script>
/** 横向审批流组件
 * <AUTHOR>
 */
import { hzIcon } from '@hertz/base-components'
/**
 * @displayName stepHorizontal 横向审批流
 */
export default {
  name: 'stepHorizontal',
  components: { hzIcon },
  props: {
    /**
     * 表数据，结构如下：
     * [steplist:[{status: '**',comment: '**',user_id: '**',name: '**',approve_time: '**'}]]
     */
    steplist: {
      type: Array,
      default: () => []
    },
    /**
     * 字符串类型：'create', '!create'
     */
    type: {
      type: String,
      default: ''
    },
    /**
     * 数值类型：0 表示 待审核，1 表示 审核中，2 表示 已通过，3 表示 已驳回， 4 表示 已撤回
     */
    status: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      stepInfo: ['提交审批', '审批中', '审批通过', '审批驳回', '已撤回'],
      stepStatus: ['待审核', '审核中', '已通过', '已驳回', '已撤回'],
      bgColor: ['#FF9431', '#1F71FF', '#239545', '#FF5266', 'rgba(21, 22, 24, 0.36)']
    }
  }
}
</script>
<style scoped>
.steps-item:last-child {
  @apply flex-none;
}

.steps-item:not(last-child) {
  @apply  mr-4;
}

.steps-item:not(first-child) {
  @apply pl-1;
}
/* 默认状态 */
.steps-item-icon{
  @apply w-4 h-4 inline-block align-middle rounded-full ml-px border-solid text-primary-800 border-5;
}
/* 状态-待审核 status为0*/
.edit-step-icon{
  @apply border-none;
}
.edit-btn {
  @apply relative -top-px -ml-2;
}
.edit-btn::after {
  @apply absolute w-4 h-4 inline-block align-middle rounded-full bg-primary-800;
  content: '';
}

/* 状态-审核中 status为1*/
.current-step-icon {
  @apply relative border-8 border-solid text-primary-800;
}
.current-step-icon::after {
  @apply w-6 h-6 absolute -ml-3 -mt-3 rounded-full bg-primary-a-300;
  content: '';
}

/* 状态-已通过 status为2*/
.success-step-icon{
   @apply relative border-8 border-solid text-primary-800;
}
.success-step-icon::after{
  @apply absolute bg-transparent w-1 h-2 -mt-1 -ml-0.5 opacity-100 transform rotate-45
  rounded-5.5;
  content: '';
  border: 1px solid #fff;
  border-top: none;
  border-left: none;
}

/* 状态-已驳回 status为3*/
.error-step-icon{
  @apply w-4 h-4 border-8 border-solid text-red-900;
}
.error-step-icon::before, .error-step-icon::after {
  @apply absolute w-px left-3 top-1.5 h-3;
    content: '';
    background-color:#fff;
}
.error-step-icon::before {
   @apply transform rotate-45;
}
.error-step-icon::after {
   @apply transform -rotate-45;
}
/* 标题设置 */
.steps-item-content::after {
  @apply absolute left-full block h-0.5 w-screen bg-primary-800 top-2.5;
  content: '';
}
.steps-item-content::after:last-child {
  @apply w-0 hidden;
}
</style>
