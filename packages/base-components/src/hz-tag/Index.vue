<template>
  <span class="hz-tag" :class="[textColor, bgColor, bColor]">{{ text }}</span>
</template>
<script>
export default {
  name: 'HzTag',
  props: {
    text: {
      type: String,
      default: 'text'
    },
    /**
     * 字体颜色 默认是text-primary-900 颜色方案和tailwindcss保持一致；如 text-primary-900 text-mono-200
     * @values  'text-primary-900' | ''
     */
    textColor: {
      type: String,
      default: 'text-primary-900'
    },
    /**
     * 背景色 默认是透明色 颜色方案和tailwindcss保持一致；如 bg-primary-900
     * @values  'bg-transparent' | ''
     */
    bgColor: {
      type: String,
      default: 'bg-transparent'
    },
    /**
     * 边框颜色，默认为空，如不设置边框颜色（为空），边框颜色与字体颜色保持一致
     * @values ''
     */
    borderColor: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      bColor: ''
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if (!this.borderColor) {
        this.bColor = this.textColor.replace('text', 'border')
      } else {
        this.bColor = this.borderColor
      }
    }
  }
}
</script>
<style scoped>
  .hz-tag {
    @apply inline-block px-1.5 py-0.5 rounded border-1 border-solid text-xs font-normal leading-normal;
    height: fit-content;
  }
</style>
