```vue
<template>
  <div class="main">
    <ul class="flex flex-row space-x-4">
      <li class="w-16 h-16 rounded-md flex items-center justify-center cursor-pointer">
        <hz-tag text="默认">
      </li>
      <li class="w-16 h-16 rounded-md flex items-center justify-center cursor-pointer">
        <hz-tag text="primary" textColor="text-primary-900">
      </li>
      <li class="w-16 h-16 rounded-md flex items-center justify-center cursor-pointer">
        <hz-tag text="原子指标" textColor="text-cerulean-900">
      </li>
    </ul>
  </div>
</template>
<script>

export default {
  data() {
    return { 
    }
  }
}
</script>
<style lang="scss" scoped>
  .main{
    li{
      @apply w-8 h-8 rounded-md flex items-center justify-center;
    }
  }
</style>
```