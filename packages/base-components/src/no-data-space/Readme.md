
```vue
<template>
  <div class="main relative">
    <ul class="flex" style="height: 300px">
      <li class=" w-1/2 h-full flex">
        <no-data-space class="self-center" size="md"></no-data-space>
      </li>
      <li class=" w-1/2 h-full relative">
        <no-data-space place-type="relative"></no-data-space>
      </li>
    </ul>
  </div>
</template>
<script>

export default {
  data() {
    return { 
      
    }
  }
}
</script>
<style lang="scss" scoped>
 
</style>
```