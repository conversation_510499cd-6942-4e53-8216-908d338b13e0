<template>
  <div class="no-data" :class="className" ref="noDataRef">
    <img :src="url" v-if="url">
    <i :style="noDataImg" v-else></i>
    <p v-html="text || '暂无数据'" class="default-text"></p>
    <slot></slot>
  </div>
</template>
<script>
/**
 * @displayName noDataSpace 空状态
 */

export default {
  name: 'noDataSpace',
  props: {
    /**
     * 显示文案
     */
    text: String,
    /**
     * 尺寸大小
     * @values sm | md | lg
     */
    size: {
      type: String,
      default: 'base'
    },
    /**
     * 居中方案，默认父级flex布局 由组件加class self-center ; relative 相对居中 父级样式需要position:relative
     * @values relative | ''
     */
    placeType: {
      type: String,
      default: ''
    },
    /**
     * 图片类型
     * @values no-data | no-result-light
     */
    imgType: {
      type: String,
      default: 'no-data'
    },
    /**
     * 图片url
     */
    url: String
  },
  data () {
    return {
      className: '',
      imgClassName: '',
      noDataImg: {}
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      let className = ''
      if (this.placeType === 'relative') {
        className = 'absolute top-1/2 left-1/2'
        this.$nextTick(() => {
          this.selfCenterHandle()
        })
      } else {
        className = 'mx-auto'
      }
      if (this.size) {
        className += ` ${this.size}`
      }
      if (this.url) {
        className += ' use-img'
      }
      this.className = className
      const url = require(`./${this.imgType}.svg`)
      this.noDataImg = { background: `url(${url}) no-repeat center center`, 'background-size': 'contain' }
    },
    selfCenterHandle () {
      const width = this.$el.scrollWidth
      const height = this.$el.scrollHeight
      if (!this.url) {
        this.$el.style.marginLeft = `-${width / 2}px`
        this.$el.style.marginTop = `-${height / 2}px`
      }
    }
  }
}
</script>
<style scoped>
  .no-data {
    @apply text-center text-type-700 w-auto h-auto;
  }
  .no-data i{
    @apply  w-48 h-48 bg-no-repeat inline-block bg-cover;
  }
  .no-data p{
    @apply leading-6
  }
  .no-data.md i {
    @apply transform scale-75;
  }
  .no-data.md p {
    @apply -mt-5;
  }
  .no-data.lg i {
    @apply transform scale-125;
  }
  .no-data.lg p {
    @apply mt-4;
  }
  .no-data.sm i {
    @apply transform scale-50;
  }
  .no-data.sm p {
    @apply -mt-10;
  }
  .absolute-center {
    @apply absolute left-1/2 top-1/2
  }
  .use-img.no-data {
    transform: translate(-50%, -50%);
  }
  .no-data.sm img {
    @apply w-50
  }
  .use-img.no-data p {
    @apply mt-0
  }
  .use-img.sm p {
    @apply text-xs
  }
</style>
