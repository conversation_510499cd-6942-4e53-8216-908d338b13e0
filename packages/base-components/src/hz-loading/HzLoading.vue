<template>
  <div class="base-loading-layer" v-show="showLoading">
    <div class="loader-outer" v-bind:class="className">
      <img :src="loadImg" />
    </div>
  </div>
</template>

<script>
const loadImg = require('./loading.png')
/**
 * @displayName hz-loading 加载
 */
export default {
  name: 'hzLoading',
  props: {
    showLoading: {
      default: false,
      type: Boolean
    },
    className: { // sm: 小号的loading
      type: String,
      default: ''
    }
  },
  data () {
    return {
      loadImg: loadImg
    }
  }
}
</script>

<style scoped>
.base-loading-layer {
  @apply absolute top-0 right-0 bottom-0 left-0 z-50
}
.base-loading-layer .loader-outer{
  @apply absolute z-50 w-15 h-15 -mt-5 -ml-5 top-2/4 left-2/4
}
.loader-outer.sm {
  transform: scale(0.5);
}
</style>
