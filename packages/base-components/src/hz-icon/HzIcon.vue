<template>
  <i class="hz-icon" v-bind:class="classNamenew" v-on="$listeners">
    <svg width="100%" height="100%">
      <use :xlink:href="svgHref + '#' + this.hzName"></use>
    </svg>
    <span class="hz-icon-bg" :class="bgClass" :style="bgStyle"></span>
  </i>
</template>
<script>
// import svgImg from '@hertz/svg-icons'
const svgImg = require('@hertz/svg-icons/src/svg-sprite/svg-symbols.svg')
/**
 * <AUTHOR>
 * @displayName hzIcon 图标组件
 */
export default {
  name: 'hzIcon',
  props: {
    hzName: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    },
    /**
     * action icon背景大小
     * @values 'sm','default'
     */
    bgSize: {
      type: String,
      default: 'default'
    },
    /**
     * icon背景形状
     * @values 'square','circle'
     */
    bgShape: {
      type: String,
      default: 'circle' // square | circle
    }
  },
  data () {
    return {
      svgHref: typeof svgImg === 'string' ? svgImg : svgImg.default,
      classNamenew: '',
      bgClass: '',
      bgStyle: {}
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      let strClass = []
      const className = this.className.split(' ')
      const defaultClass = {
        // color: "text-type-800",
        height: 'h-4',
        width: 'w-4'
      }
      let width = ''
      let height = ''
      const wIndex = className.findIndex((item) => item.indexOf('w-') > -1)
      if (wIndex > -1) {
        width = className[wIndex]
        strClass.push(className[wIndex])
        className.splice(wIndex, 1)
      } else {
        strClass.push(defaultClass.width)
        width = defaultClass.width
      }

      const hIndex = className.findIndex((item) => item.indexOf('h-') > -1)
      if (hIndex > -1) {
        height = className[hIndex]
        strClass.push(className[hIndex])
        className.splice(hIndex, 1)
      } else {
        strClass.push(defaultClass.height)
        height = defaultClass.height
      }
      const hoverIndex = className.findIndex(
        (item) => item.indexOf('hover:text-') > -1
      )
      let hoverClass = ''
      if (hoverIndex > -1) {
        hoverClass = className[hoverIndex]
        strClass.push(hoverClass)
        className.splice(hoverIndex, 1)
      }
      const actionIndex = className.findIndex(
        (item) => item.indexOf('hz-action-icon') > -1
      )
      if (actionIndex > -1) {
        const afterClass = []
        if (hoverIndex > -1) {
          afterClass.push(hoverClass.replace('hover:text', 'bg'))
        } else {
          afterClass.push('bg-type-600')
        }
        const bgSize = this.bgSize === 'sm' ? 1.6 : 1.8
        const w = parseInt(width.replace('w-', '')) / 4 * bgSize
        const h = parseInt(height.replace('h-', '')) / 4 * bgSize
        this.bgClass = afterClass.join(' ')
        if (this.bgShape === 'square') {
          this.bgClass += ` bg-${this.bgShape}`
        }
        this.bgStyle = {
          width: w + 'rem',
          height: h + 'rem',
          top: '50%',
          left: '50%',
          marginLeft: -w / 2 + 'rem',
          marginTop: -h / 2 + 'rem'
        }
      }
      const textIndex = className.findIndex((item) => item.indexOf('text-') > -1)
      if (textIndex > -1) {
        strClass.push(className[textIndex])
        className.splice(textIndex, 1)
      } else {
        strClass.push(defaultClass.color)
      }
      strClass = strClass.concat(className)
      this.classNamenew = strClass.join(' ')
    }
  }
}
</script>
 <style scoped>
  .hz-icon{
    @apply inline-block align-middle relative;
  }
  .hz-action-icon{
    @apply cursor-pointer;
  }
  .hz-action-icon .hz-icon-bg, .hz-bg-icon .hz-icon-bg{
    @apply absolute rounded-full opacity-0 pointer-events-none;
    transition: all 0.4s cubic-bezier(0.2, 1.2, 0.5, 0.9);
  }
  .hz-action-icon .hz-icon-bg.bg-square,.hz-bg-icon .hz-icon-bg.bg-square{
    @apply rounded;
  }
  .hz-action-icon:hover .hz-icon-bg,.hz-bg-icon .hz-icon-bg{
    @apply opacity-20;
  }
</style>
