```vue
<template>
  <div class="main">
    <ul class="flex flex-row space-x-4">
      <li v-for="it in iconList" class="w-16 h-16 rounded-md flex items-center justify-center cursor-pointer">
        <hz-icon :hzName="it.name" :className="it.size"/>
      </li>
      <li class="w-16 h-16 rounded-md flex items-center justify-center cursor-pointer">
        <hz-icon hzName="rule-add" className="hz-action-icon w-10 h-10 text-lime-900"/>
      </li>
      <li class="w-16 h-16 rounded-md flex items-center justify-center cursor-pointer">
        <hz-icon hzName="default-icon" className="hz-action-icon w-4 h-4 text-lime-900"/>
      </li>
    </ul>
  </div>
</template>
<script>
const iconList = [
  {
    name:'account-admin',
    size: 'w-16 h-16 text-yellow-900'
  },
  {
    name:'account-admin',
    size: 'w-16 h-16 text-primary-900'
  },
  {
    name:'account-admin',
    size: 'w-10 h-10 hz-action-icon'
  },
  {
    name:'account-admin',
    size: 'w-10 h-10'
  },
  {
    name:'account-admin',
    size: 'w-10 h-10'
  },
]
export default {
  data() {
    return { 
      iconList: iconList
    }
  }
}
</script>
<style lang="scss" scoped>
  .main{
    li{
      @apply w-8 h-8 rounded-md flex items-center justify-center;
    }
  }
</style>
```