<template>
  <div class="inline-block">
    <span class="catalog-status inline-block align-middle" :class="className"></span><span :class="textColor" class="ml-2">{{ textInfo }}</span>
  </div>
</template>

<script>
/**
 * @displayName hz-status-text 字段状态
 */
export default {
  name: 'hzStatusText',
  props: {
    /**
     * 颜色值
     * @values bg-lime-900: 绿色 | bg-mono-a-500: 灰色 | bg-orange-900：橙色 | bg-red-900: 红色 | bg-primary-900: 蓝色
     */
    className: {
      type: String,
      default: 'bg-lime-900'
    },
    /**
     * 文本值
     */
    textInfo: {
      type: String,
      default: ''
    },
    /**
     * 文本颜色
     * @values text-type-900
     */
    textColor: {
      type: String,
      default: 'text-type-900'
    }
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped>
.catalog-status{
  width: 6px;
  height: 6px;
  border-radius: 33px;
}
</style>
