<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title><%= icons.length + ' Symbols' %></title>
  <meta name="viewport" content="width=device-width" />
  <style>
    html {
      font-family: "Helvetica Neue", Arial, sans-serif;
      color: black;
      background: #e4e4e4;
      text-align: center;
    }
    body {
      margin: 0;
      padding-top: 2.5rem;
    }
    main {
      padding: 1rem .5rem;
    }
    button {
      font: inherit;
      background: transparent;
      border: none;
    }
    button::-moz-focus-inner {
      padding: 0;
      border: none;
    }
    .symbols-dictionary {
      border: 0 !important;
      clip: rect(0 0 0 0) !important;
      height: 1px !important;
      margin: -1px !important;
      overflow: hidden !important;
      padding: 0 !important;
      position: absolute !important;
      width: 1px !important;
    }
    .gss-toolbar {
      position: absolute;
      z-index: 100;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      border-bottom: solid 1px #c6c6c6;
      text-align: left;
      background: white;
    }
    .gss-toolbar--active {
      position: fixed;
    }
    .gss-toolbar > * {
      display: inline-block;
      vertical-align: middle;
      white-space: nowrap;
      margin: 0;
      padding: .5rem 1rem;
      line-height: 1.35;
    }
    .gss-toolbar h1 {
      display: none;
      font: inherit;
    }
    .gss-toolbar:not(.gss-toolbar--active) h1 {
      display: inline-block;
    }
    .gss-toolbar:not(.gss-toolbar--active) h1 ~ * {
      display: none !important;
    }
    .gss-toolbar.gss-toolbar > * + * {
      border-left: solid 1px #c6c6c6;
    }
    .gss-toolbar > *:focus {
      outline: none;
      box-shadow: inset 0 0 0 1px #c71585;
    }
    .gss-toolbar [hidden] {
      display: none;
    }
    .gss-toolbar-filter {
      width: 15rem;
      flex-grow: 10;
      border: none;
      font: inherit;
    }
    .gss-toolbar button {
      position: relative;
      z-index: 1;
      font-size: 87.5%;
      background-color: #f4f4f4;
    }
    .gss-toolbar-filter + button {
      margin-left: auto;
    }
    .gss-toolbar button[aria-pressed="true"] {
      z-index: 2;
      color: #ddd;
      background-color: #444;
      outline-color: rgba(255,255,255,.6);
    }
    .gss-toolbar .gss-toolbar-list {
      font-size: 100%;
    }
    .gss-toolbar button > svg {
      fill: currentColor;
      vertical-align: -2px;
      margin: 0 4px 0 2px;
    }
    .gss-toolbar-list[aria-pressed="true"] ~ .gss-toolbar-orig {
      display: none;
    }
    @media (max-width: 40em) {
      .gss-toolbar-filter {
        margin-left: 0 !important;
      }
      .gss-toolbar-filter ~ * {
        display: none !important;
      }
    }

    /* Grid of small icons */
    .gss-iconlist ul {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-content: center;
      min-height: calc(100vh - 100px);
      margin: 0;
      padding: 0;
      list-style: none;
    }
    .gss-iconlist-item {
      display: inline-block;
      vertical-align: top;
      margin: .5rem;
    }
    .gss-iconlist-item a {
      display: block;
      box-sizing: border-box;
      min-height: 100%;
      border-radius: .35em;
      padding: .5rem;
      color: inherit;
      background-color: #fff;
      transition: all 100ms;
    }
    .gss-iconlist-item a:hover,
    .gss-iconlist-item a:focus {
      outline: none;
      color: #fff;
      background-color: rgba(0,0,0,.8);
    }
    .gss-iconlist-item a > svg {
      width: 2.75rem;
      height: 2.75rem;
      vertical-align: top;
      fill: currentColor;
    }
    @media (min-width: 40em) {
      .gss-iconlist-item a > svg {
        width: 3.5rem;
        height: 3.5rem;
      }
    }

    /* Grid of big icons with lots of info */
    .gss-iconboxes {
      display: flex;
      flex-wrap: wrap;
      margin: 0;
    }
    .gss-iconboxes[hidden] {
      display: none;
    }
    .gss-iconboxes::after {
      content: '';
      flex-grow: 1000;
    }
    .gss-iconbox {
      display: inline-block;
      vertical-align: top;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      width: calc(100% - 1rem);
      max-width: 40rem;
      overflow: hidden;
      margin: .5rem;
      border-radius: .5rem;
      background-color: #fff;
    }
    .gss-iconbox:focus {
      outline: none;
      box-shadow: 0 0 0 4px #e960b2;
    }
    .gss-iconbox[data-state="info"] {
      background: #f4f4f4;
    }
    @media (min-width:40em) {
      .gss-iconbox { width: calc(50% - 1rem); }
    }
    @media (min-width:60em) {
      .gss-iconbox { width: calc(33.33% - 1rem); }
    }
    @media (min-width:80em) {
      .gss-iconbox { width: calc(25% - 1rem); }
    }
    @media (min-width:100em) {
      main { width: 105rem; max-width: calc(100% - 1rem); margin: 0 auto; }
      .gss-iconbox { width: calc(20% - 1rem); }
    }
    .gss-iconbox-header,
    .gss-iconbox-head2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0;
      padding: 0 .5rem;
      font-size: 87.5%;
      color: #444;
      border-bottom: 1px solid #e6e6e6;
    }
    .gss-iconbox-header {
      background-color: #fff;
    }
    .gss-iconbox[data-state="info"] .gss-iconbox-header,
    .gss-iconbox:not([data-state="info"]) .gss-iconbox-head2 {
      display: none;
    }
    .gss-iconbox-header > *,
    .gss-iconbox-head2 > * {
      display: inline-block;
      white-space: nowrap;
      padding: .75rem .5rem;
    }
    .gss-iconbox-head2 > :nth-child(1) {
      margin-right: auto;
      text-align: left;
    }
    .gss-iconbox-header h2 {
      position: relative;
      top: -1px;
      overflow: hidden;
      text-align: left;
      text-overflow: ellipsis;
      margin: 0;
      font: inherit;
      font-size: 115%;
      line-height: 1;
      color: black;
    }
    .gss-iconbox-header > span {
      color: #777;
    }
    .gss-iconbox-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex-grow: 1;
      position: relative;
      overflow: hidden;
      min-height: 8rem;
    }
    .gss-iconbox:not([data-state="info"]) .gss-iconbox-wrapper {
      background-color: #fafafa;
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="rgb(238,238,238)" width="100" height="100"><rect x="0" y="0" width="50" height="50"></rect><rect x="50" y="50" width="50" height="50"></rect></svg>');
      background-size: 24px 24px;
    }
    @media (min-width:40em) {
      .gss-iconbox-wrapper { min-height: 10rem; }
    }
    .gss-iconbox-icon {
      padding: 1rem;
    }
    .gss-iconbox[data-state="info"] .gss-iconbox-icon {
      display: none;
    }
    .gss-iconbox-icon > svg {
      fill: currentColor;
      vertical-align: middle;
      width: 6rem;
      height: 6rem;
      max-width: 16rem;
      max-height: 16rem;
    }
    @media (min-width:40em) {
      .gss-iconbox-icon > svg {
        width: 8rem;
        height: 8rem;
      }
    }
    .gss-showColor svg[id^="gss-icon-"] {
      color: red;
      fill: red;
    }
    .gss-showOriginal .gss-iconbox-icon > svg {
      margin-top: 1rem;
      outline: solid 1px rgba(0,0,0,.5);
      box-shadow: 0 0 0 250px rgba(255,255,255,.8);
    }
    @media (min-resolution: 2dppx) {
      .gss-showOriginal .gss-iconbox-icon > svg {
        outline-width: .5px;
      }
    }
    .gss-showOriginal .gss-iconbox-icon::after {
      content: attr(data-dimensions);
      display: table;
      margin: .5rem auto 0;
      padding: 1px 3px;
      font-size: 75%;
      color: #444;
    }
    .gss-iconbox-code {
      flex-grow: 1;
      margin: 0;
      padding: 1rem;
      text-align: left;
      font: inherit;
      line-height: 1.35;
      background-color: #f4f4f4;
    }
    .gss-iconbox-code code {
      display: block;
      white-space: pre-wrap;
      font-family: monospace, monospace;
      font-size: 80%;
    }
    /* can't display none: copy won't work */
    .gss-iconbox[data-state="icon"] .gss-iconbox-code {
      position: absolute;
      top: -200px;
      opacity: 0;
      pointer-events: none;
    }
    .gss-iconbox-action {
      display: none;
      border-top: 1px solid #e6e6e6;
    }
    .gss-iconbox[data-state] .gss-iconbox-action {
      display: block;
      display: flex;
    }
    .gss-iconbox-action > * {
      flex-grow: 1;
      flex-basis: 33.3%;
      padding: .8rem .5rem;
      border: 0;
      font: inherit;
      font-size: 87.5%;
      color: #444;
      background: none;
    }
    .gss-iconbox-action > * + * {
      border-left: 1px solid #e6e6e6;
    }
    .gss-iconbox-action button {
      cursor: pointer;
      outline: none;
    }
    .gss-iconbox-action button:hover,
    .gss-iconbox-action button:focus {
      color: #c71585;
    }
    .gss-iconbox-action button:focus {
      outline: solid 1px;
      outline-offset: -6px;
    }
    .gss-iconbox-action button:active {
      color: black;
    }
  </style>
  <style>
    <% _.forEach( icons, function( icon ){
    %>.gss-showOriginal .gss-iconbox #gss-icon-<%= icon.id %> {
      width: <%= icon.width %>;
      height: <%= icon.height %>;
    }
    <%= icon.style %>
    <% }); %>
  </style>
  <style class="gss-filter-css"></style>
</head>
<body>
<svg xmlns="http://www.w3.org/2000/svg" class="symbols-dictionary">
  <defs>
    <%= defs %>
  </defs>
  <% _.forEach( icons, function( icon ){ %>
    <symbol id="<%= icon.id %>" viewBox="<%= icon.svg.viewBox %>"<% if (icon.svg.originalAttributes.preserveAspectRatio) {%> preserveAspectRatio="<%= icon.svg.originalAttributes.preserveAspectRatio %>"<% }%>><% if (icon.title) {%>
      <title><%= icon.title %></title><% }%>
      <%= icon.svg.content %>
    </symbol><%
}); %>
  <symbol id="gss-icon-list" viewBox="0 0 5 5">
    <rect width="2" height="2" x="0" y="0"></rect>
    <rect width="2" height="2" x="3" y="0"></rect>
    <rect width="2" height="2" x="0" y="3"></rect>
    <rect width="2" height="2" x="3" y="3"></rect>
  </symbol>
</svg>
<header class="gss-toolbar">
  <h1><%= icons.length %> Symbols</h1>
  <button class="gss-toolbar-list">
    <svg aria-label="Compact" width="15" height="15"><use xlink:href="#gss-icon-list"></use>
    </svg> List
  </button>
  <input class="gss-toolbar-filter" type="search"
         placeholder="Filter <%= icons.length %> symbols…"
         aria-label="Filter by id" title="Filter by id">
  <button class="gss-toolbar-orig" data-toggle-class="gss-showOriginal">Original Size</button>
  <button class="gss-toolbar-outl" data-toggle-class="gss-showColor">Test Color</button>
</header>
<main>
<nav class="gss-iconlist" hidden></nav>
<div class="gss-iconboxes"><%
icons.forEach(function(icon) {
  // Approximate size = content + id + <symbol> with viewBox markup
  var size = icon.svg.content.length + icon.id.length + 50;
  var hrSize = Math.max(Math.round(size/100), 1) / 10;
  var dimensions = [
    Math.round(icon.svg.width),
    Math.round(icon.svg.height)
  ];
  var search = [
    icon.svg.name.trim().toLowerCase(),
    icon.id.trim().toLowerCase(),
    dimensions.join('x')
  ].concat([1, 2, 3, 4, 5, 6, 7, 8, 9].map(function(s){
    return (hrSize >= s ? '>' : '<') + s;
  }));
  var example = '<svg role="img" class="' + icon.id + '">\n' +
    '  <use xlink:href="#' + icon.id + '"></use>\n</svg>';
%>
  <div class="gss-iconbox gss-searchable"
       id="gss-iconbox-<%= icon.id %>"
       data-search="<%= search.join(' ') %>">
    <div class="gss-iconbox-header">
      <h2><%= icon.id %></h2>
      <span><%= hrSize %>&thinsp;kB</span>
    </div>
    <p class="gss-iconbox-head2">
      <span><%= icon.svg.name %>.svg</span>
      <span><%= dimensions.join('&thinsp;×&thinsp;') %></span>
      <span><%= size %>&thinsp;B</span>
    </p>
    <div class="gss-iconbox-wrapper">
      <div class="gss-iconbox-icon" data-dimensions="<%= dimensions.join('&thinsp;×&thinsp;') %>" data-icon-desc="">
        <svg role="img" id="gss-icon-<%= icon.id %>">
          <use xlink:href="#<%= icon.id %>"></use>
        </svg>
      </div>
      <div class="gss-iconbox-code">
        <code><%= _.escape(example) %></code>
      </div>
    </div>
    <div class="gss-iconbox-action">
      <button data-action="copy">Copy</button>
      <button data-action="info">Show</button>
    </div>
  </div><% }); %>
</div>
</main>
<script type="text/javascript">
  (function () {
    var header = document.querySelector('.gss-toolbar');
    var boxes = document.querySelector('.gss-iconboxes');
    var clist = document.querySelector('.gss-iconlist');
    var main = document.querySelector('main');

    // Show header
    header.classList.add('gss-toolbar--active');

    // Hide example code and show controls
    [].forEach.call(
      boxes.querySelectorAll('.gss-iconbox'),
      toggleBoxContent
    );

    // Toggle list of smaller icons
    var listToggle = header.querySelector('.gss-toolbar-list');
    listToggle.hidden = false;
    listToggle.addEventListener('click', function(event) {
        toggleCompactList();
    });
    // Close list and navigate
    clist.addEventListener('click', function(event) {
      var link = closest(
        event.target,
        function(el){return el.hasAttribute('href')}
      );
      if (link) {
        event.preventDefault();
        var target = document.querySelector(link.getAttribute('href'));
        if (target) toggleCompactList('big', target);
      }
    });

    // Enable filter box
    var filterInput = header.querySelector('.gss-toolbar-filter');
    var filterStyle = document.querySelector('.gss-filter-css');
    // filter once on page load, then on key presses
    if (filterInput && filterStyle) {
      filterIcons(filterInput.value, filterStyle);
      filterInput.addEventListener('keypress', function(e) {
        if (e.keyCode === 27) {
          e.currentTarget.value = '';
          filterIcons('', filterStyle);
        }
      });
      filterInput.addEventListener('input', function(e) {
        filterIcons(e.currentTarget.value, filterStyle);
      });
      filterInput.hidden = false;
    }

    // Icon actions (with event delegation)
    boxes.addEventListener('click', function(event) {
      var action = event.target.getAttribute('data-action');
      if (!action) return;
      var box = closest(event.target, function(el){
        return el.classList.contains('gss-iconbox');
      });
      if (box && action === 'info') {
        var state = toggleBoxContent(box);
      }
      if (box && action === 'copy') {
        selectAndCopy(box.querySelector('code'));
      }
    });
    // Remove JS-added tabindex from boxes, to avoid giving a focusring
    // to a box by clicking it or its content.
    boxes.addEventListener('blur', function(event) {
      var t = event.target;
      if (t.classList.contains('gss-iconbox') && t.hasAttribute('tabindex')) {
        t.removeAttribute('tabindex');
      }
    }, true);

    // Enable checkboxes
    header.addEventListener('click', function(event) {
      var attr = 'data-toggle-class';
      var btn = closest(event.target, function(el) {
        return el.hasAttribute(attr);
      });
      if (btn) {
        var c = btn.getAttribute(attr);
        if (main.classList.contains(c)) {
          main.classList.remove(c);
          btn.setAttribute('aria-pressed', 'false');
        } else {
          main.classList.add(c);
          btn.setAttribute('aria-pressed', 'true');
        }
      }
    });

    /**
     * Quick and dirty polyfill for element.closest
     * @param {Element} self
     * @param {Function} cb - must return true for a matching element
     * @return {Element|null}
     */
    function closest(self, cb) {
      if (cb(self)) return self;
      var p = self.parentElement;
      return p === null ? null : closest(p, cb);
    }

    /**
     * Toggle code example visibility
     */
    function toggleBoxContent(box) {
      var a = 'data-state', x = 'icon', y = 'info';
      var s = box.getAttribute(a) === x ? y : x;
      box.setAttribute(a, s);
      return s;
    }

    /**
     * Copy example code to clipboard
     * https://developers.google.com/web/updates/2015/04/cut-and-copy-commands
     */
    function selectAndCopy(element) {
      window.getSelection().removeAllRanges();
      var range = document.createRange();
      range.selectNode(element);
      window.getSelection().addRange(range);
      document.execCommand('copy');
    }

    /**
     * Filter content by id, with multiple words possible (additive)
     * Filtering is done by injecting CSS rules in a stylesheet
     */
    function filterIcons(query, stylesheet) {
      // clean up input
      var search = query.toLowerCase()
        .replace(/["'\\\]){}]+/g, ' ')
        .replace('/\s+/g', ' ')
        .trim();
      // undo filter
      if (search === '') {
        stylesheet.innerHTML = '';
        return;
      }
      // add filter
      var keywords = search.split(' ');
      var css = '';
      for (var i=0; i<keywords.length; i++) {
        if (i>0) css += ',\n';
        css += '.gss-searchable:not([data-search*="' + keywords[i] + '"])';
      }
      css += '\n{ display: none !important; }';
      stylesheet.innerHTML = css;
    }

    /**
     * Show the compact list
     * @param {string} mode - 'small' or 'big'
     * @param {Element|undefined} target - element to focus
     */
    function toggleCompactList(mode, target) {
      if (clist.childElementCount === 0) {
        clist.innerHTML = '<ul>' + getCompactList(boxes).join('\n') + '</ul>';
      }
      if (['small', 'big'].indexOf(mode) === -1) {
        mode = clist.hidden ? 'small' : 'big';
      }
      // switch content
      clist.hidden = (mode === 'big');
      boxes.hidden = (mode === 'small');

      // update button status
      listToggle.setAttribute('aria-pressed', String(mode === 'small'));

      // scroll to the top if showing the small list
      if (mode === 'small' || (mode === 'big' && !target)) {
        window.scrollTo(0, 0);
      }
      // scroll to element and give it focus if going to big mode
      if (mode === 'big' && target) {
        // focus element
        target.setAttribute('tabindex', '-1');
        target.focus();
        // centering in viewport
        var y = target.offsetTop - window.innerHeight / 2 + target.offsetHeight / 2;
        window.scrollTo(0, Math.max(Math.round(y) - 20 /* adjust for header */, 0));
      }
    }

    /**
     * Make the list of smaller icons by copying from the big list
     */
    function getCompactList(source) {
      var icons = source.querySelectorAll('.gss-iconbox');
      return [].map.call(icons, function(el) {
        var search = el.getAttribute('data-search');
        var icon = el.querySelector('.gss-iconbox-icon > svg').outerHTML;
        return '<li class="gss-iconlist-item gss-searchable" ' +
          'data-search="' + search +'"><a href="#' + el.id + '">' +
          icon + '</a></li>';
      });
    }

  }());
</script>
</body>
</html>
